﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    Layout = "_LayoutRoot";
}
@section scripts {
    @RenderSection("scripts", required: false)
}

@section Styles {
    @RenderSection("Styles", required: false)
}

@section BodyArea {
    @RenderSection("BodyArea", required: false)
}
<div class="layout-h @(ViewData["BodyClass"] ?? "")" style="height: 100%">
    <div class="wrapper">
        <!-- top navbar-->
        <header class="topnavbar-wrapper">
            <partial name="_TopNavbarHorizontal" />
        </header>

        <!-- offsidebar-->
        @if (await ViewManager.IsHcaUser)
        {
            <aside class="offsidebar d-none">
                <partial name="_Offsidebar" />
            </aside>
        }
        <!-- Main section-->
        <section class="section-container">
            <!-- Page content-->
            <div class="content-wrapper">
                <div class="container-fluid">
                    <div class="topRow">
                        <div>
                            @if (Context.Request.Path != "/")
                            {
                                <ol class="breadcrumb">
                                    @if (await ViewManager.IsAdminUser)
                                    {
                                        <li class="breadcrumb-item">
                                            <a href="/">Home</a>
                                        </li>
                                    }
                                </ol>
                            }
                        </div>
                        <div class="buttonCol">
                            <a href="/support" class="btn btn-info">Need Help?</a>

                            @if (await ViewManager.IsAdminUser)
                            {
                                <a href="/admin" class="btn btn-dark">Admin</a>
                            }

                            <form style="display: inline-block;"
                                  asp-area="Identity" 
                                  asp-page="/Account/Logout" 
                                  asp-route-returnUrl="@Url.Page("/", new { area = "" })" 
                                  method="post">
                                <button type="submit" class="btn btn-danger">Logout</button>
                            </form>
                        </div>
                    </div>
                    <style>
                        .topRow {
                            display: flex; 
                            justify-content: space-between;
                            align-items: flex-start;
                        }

                        .buttonCol {
                            display: flex;
                            flex-wrap: nowrap; 
                            justify-content: flex-end;
                            gap: 10px;
                        }

                        .buttonCol a {
                            min-width: 100px;
                        }
                    </style>
                    @RenderBody()
                </div>
            </div>
        </section>
        <!-- Page footer-->
        <footer class="footer-container">
            <partial name="_Footer" />
        </footer>
    </div>
</div>