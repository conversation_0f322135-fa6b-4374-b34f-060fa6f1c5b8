﻿@page "/clients/{clientId:guid}/sites/{siteId:guid}/plans/new"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitePlanUploadModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutSite";
    ViewData["Client"] = Model.Client;
    ViewData["Site"] = Model.Site;
}

<div class="row" id="divLocations">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">UPLOAD SITE PLAN</small>
            </div>
            <form method="post" enctype="multipart/form-data">
                <div class="card-body">
                    <div class="form-group row">
                        <label class="col-xl-2 col-form-label" for="UploadImage">Upload a Site Plan</label>
                        <div class="col-xl-10">
                            <div id="file-drop-container"></div>
                            <div asp-validation-summary="ModelOnly" class="text-danger text-center"></div>
                        </div>
                    </div>
                    <input type="text" asp-for="Plan.Notes" row-label="Notes" />
                </div>
                <save-cancel-footer CancelUrl="@Urls.ClientSitePlans(Model.Client.Id, Model.Site.Id)"></save-cancel-footer>
            </form>
        </div>
    </div>
</div>

@section scripts {
    <script>
        $(() => {
            initializeFileDragAndDrop({
                selector: '#file-drop-container',
                inputName: '@Html.NameFor(m => m.UploadFile)',
            });

            drawBreadcrumb([
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                { url: '@Urls.ClientSite(Model.Client.Id, Model.Site.Id)', text: '@Model.Site.GetDisplayText()' },
                { url: '@Urls.ClientSitePlans(Model.Client.Id, Model.Site.Id)', text: 'Plans' },
                { text: 'New' },
            ]);
        });

    </script>
}