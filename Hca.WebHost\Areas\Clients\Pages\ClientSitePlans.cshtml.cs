﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSitePlansModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSitePlansModel(
        ClientService clientService,
        IMediator mediator,
        ViewManager viewManager) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    [BindProperty]
    public SiteDtoExtended Site { get; set; }
    public IEnumerable<SitePlanDto> Plans { get; private set; }
    public ClientDto Client { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);
        Plans = (await _clientService.GetSitePlansAsync(clientId, siteId, cancellationToken)).Items;

        return Page();
    }

    public Task<string> GetPlanUrl(SitePlanDto plan) => this.GetBlobUrl(plan.ContainerName, plan.BlobName);
    public Task<string> GetPlanThumbnailUrl(SitePlanDto plan) => this.GetBlobUrl(plan.ContainerName, plan.ThumbnailName);
}
