﻿
//using System;

//namespace Hca.Lib.Features.Inspections
//{
//    public enum InspectionType
//    {
//        AsbestosReinspection = 1,
//        AsbestosManagementSurvey,
//        AsbestosRefurbishmentSurvey,
//        AsbestosDemolitionSurvey
//    }

//    public static class InspectionTypeExtensions
//    {
//        public static string DisplayName(this InspectionType inspectionType) =>
//            inspectionType switch
//            {
//                InspectionType.AsbestosReinspection => "Asbestos Reinspection",
//                InspectionType.AsbestosManagementSurvey => "Asbestos Management Survey",
//                InspectionType.AsbestosRefurbishmentSurvey => "Asbestos Refurbishment Survey",
//                InspectionType.AsbestosDemolitionSurvey => "Asbestos Demolition Survey",
//                _ => throw new ApplicationException("Unknown inspection type")
//            };
//    }
//}
