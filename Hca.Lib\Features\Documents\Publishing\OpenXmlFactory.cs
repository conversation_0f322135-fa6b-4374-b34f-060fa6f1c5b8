﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Wordprocessing;

using A = DocumentFormat.OpenXml.Drawing;
using DW = DocumentFormat.OpenXml.Drawing.Wordprocessing;
using PIC = DocumentFormat.OpenXml.Drawing.Pictures;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class OpenXmlFactory
    {
        public static Paragraph CreatePageBreak()
        {
            return new Paragraph(
              new Run(
                new Break() { Type = BreakValues.Page }));
        }

        public static Paragraph CreateTextParagraph(string displayText)
        {
            var text = new Text(displayText);
            var run = new Run(new RunProperties(new Bold()));
            run.Append(text);
            return new Paragraph(run);
        }

        public static Table CreateTable()
        {
            Table table = new();

            TableProperties props = new(
                new TableBorders(
                new TopBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                },
                new BottomBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                },
                new LeftBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                },
                new RightBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                },
                new InsideHorizontalBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                },
                new InsideVerticalBorder
                {
                    Val = new EnumValue<BorderValues>(BorderValues.Single),
                    Size = 12
                }));

            table.AppendChild<TableProperties>(props);

            return table;
        }

        public static Drawing CreateDrawing(string relationshipId)
        {
            return new Drawing(
               new DW.Inline(
                   new DW.Extent() { Cx = 990000L, Cy = 792000L },
                   new DW.EffectExtent()
                   {
                       LeftEdge = 0L,
                       TopEdge = 0L,
                       RightEdge = 0L,
                       BottomEdge = 0L
                   },
                   new DW.DocProperties()
                   {
                       Id = (UInt32Value)1U,
                       Name = "Picture 1"
                   },
                   new DW.NonVisualGraphicFrameDrawingProperties(
                       new A.GraphicFrameLocks() { NoChangeAspect = true }),
                   new A.Graphic(
                       new A.GraphicData(
                           new PIC.Picture(
                               new PIC.NonVisualPictureProperties(
                                   new PIC.NonVisualDrawingProperties()
                                   {
                                       Id = (UInt32Value)0U,
                                       Name = "New Bitmap Image.jpg"
                                   },
                                   new PIC.NonVisualPictureDrawingProperties()),
                               new PIC.BlipFill(
                                   new A.Blip(
                                       new A.BlipExtensionList(
                                           new A.BlipExtension()
                                           {
                                               Uri =
                                                 "{28A0092B-C50C-407E-A947-70E740481C1C}"
                                           })
                                   )
                                   {
                                       Embed = relationshipId,
                                       CompressionState =
                                       A.BlipCompressionValues.Print
                                   },
                                   new A.Stretch(
                                       new A.FillRectangle())),
                               new PIC.ShapeProperties(
                                   new A.Transform2D(
                                       new A.Offset() { X = 0L, Y = 0L },
                                       new A.Extents() { Cx = 990000L, Cy = 792000L }),
                                   new A.PresetGeometry(
                                       new A.AdjustValueList()
                                   )
                                   { Preset = A.ShapeTypeValues.Rectangle }))
                       )
                       { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" })
               )
               {
                   DistanceFromTop = (UInt32Value)0U,
                   DistanceFromBottom = (UInt32Value)0U,
                   DistanceFromLeft = (UInt32Value)0U,
                   DistanceFromRight = (UInt32Value)0U,
                   EditId = "50D07946"
               });
        }
    }
}
