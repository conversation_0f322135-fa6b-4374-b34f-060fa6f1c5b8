﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class UpdateValuePriority : ICommand
    {
        public UpdateValuePriority(Guid id, int priority) 
        {
            Id = id;
            Priority = priority;
        }

        public Guid Id { get; }
        public int Priority { get; }
    }

    public class UpdatePriorityValueHandler : DapperRequestHandler<UpdateValuePriority, CommandResult>
    {
        public UpdatePriorityValueHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateValuePriority request)
        {
            var dto = await db.GetAsync<ValueDto>(request.Id);
            dto.Priority = request.Priority;
            await db.UpdateAsync(dto);
            return CommandResult.Success();
        }
    }
}
