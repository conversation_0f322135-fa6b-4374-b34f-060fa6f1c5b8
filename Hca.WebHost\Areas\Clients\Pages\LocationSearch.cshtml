﻿@page "/clients/{clientId:guid}/properties/search"
@model Hca.WebHost.Areas.Clients.Pages.LocationsModel
@{
    if (HttpContext.IsAjaxRequest())
    {
        Layout = null;
    }
}
<div class="col-12">
    <div class="card card-default">
        <div class="card-header">
            Locations
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <partial name="Client\_LocationsTablePartial" model="Model.Locations.Items" />
            </div>
        </div>
        <div class="card-footer">
            <div class="d-flex">
                <button class="btn btn-sm btn-secondary" id="btnClearLocationSearch">Clear</button>
                <nav class="ml-auto">
                    <ul class="pagination pagination-sm">
                        @for (int i = 1; i <= Model.Locations.TotalPages; i++)
                        {
                            <li class="page-item @(i == Model.Locations.CurrentPage ? " active" : "")">
                                <a class="page-link" href="#">@i</a>
                            </li>
                        }
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
<div class="col-xl-4">
    <div class="card b" id="crdLocation" style="display: none;">
    </div>
</div>