﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Exceptions;
using MediatR;

namespace Hca.Lib.Core
{
    public class CommandHandler<TAgg> : IRequestHandler<AggregateCommand<TAgg>, CommandResult> where TAgg : IAggregate
    {
        private readonly IAggregateRepository<TAgg> _repo;

        public CommandHandler(IAggregateRepository<TAgg> repo)
        {
            _repo = repo;
        }

        public async Task<CommandResult> Handle(AggregateCommand<TAgg> request, CancellationToken cancellationToken)
        {
            var id = new EntityId(request.AggregateId);
            TAgg agg;

            await _repo.BeginTransactionAsync();

            try
            {
                agg = request is AggregateCreateCommand<TAgg>
                    ? (TAgg)Activator.CreateInstance(typeof(TAgg), id)
                    : await _repo.LoadByIdAsync(id);
            }
            catch (Exception ex)
            {
                //scope.Dispose();

                await _repo.RollbackTransactionAsync();

                throw new AggregateNotFoundException($"Failed to load {typeof(TAgg)} with id {request.AggregateId}", ex);
            }

            try
            {
                agg.Execute((dynamic)request);
            }
            catch (Exception ex)
            {
                //scope.Dispose();

                await _repo.RollbackTransactionAsync();

                throw new CommandExecutionException($"Failed to change {typeof(TAgg)} with id {request.AggregateId}", ex);
            }

            try
            {
                await _repo.SaveAsync(agg);

                await _repo.CommitTransactionAsync();

                //scope.Complete
            }
            catch (Exception ex)
            {
                //scope.Dispose();

                await _repo.RollbackTransactionAsync();

                throw new AggregateSaveException($"Failed to save {typeof(TAgg)} with id {request.AggregateId}", ex);
            }

            return CommandResult.Success();
        }
    }
}
