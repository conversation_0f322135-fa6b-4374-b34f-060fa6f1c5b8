﻿using Dapper;
using System;

namespace Hca.Lib.Features.Clients
{
    [Table(TableNames.PropertyDocuments)]
    public class PropertyDocumentDto
    {
        public Guid Id { get; set; }

        public Guid PropertyId { get; set; }

        public string ContainerName { get; set; }

        public string BlobName { get; set; }

        public DateTime DocumentDate { get; set; }

        public string CompanyName { get; set; }

        // [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
        // [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
        [IgnoreUpdate, IgnoreInsert]
        public string DocumentType => PropertyDocumentType.ToString();

        public PropertyDocumentType PropertyDocumentType { get; set; }

        public string Notes { get; set; }

        public DateTime? NextInspection { get; set; }

        public string QrCodeId { get; set; }
    }
}
