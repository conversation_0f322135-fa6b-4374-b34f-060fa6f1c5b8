﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class LocationsModel : HcaPageModel
{
    public LocationsModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public PagedDtoSet<PropertyDtoExtended> Locations { get; private set; }

    // todo: add paging
    public async Task<IActionResult> OnGetAsync(
        Guid clientId,
        [FromQuery] string searchText,
        CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Locations = await _mediator.Send(
            new FindClientProperties(
                clientId,
                searchText),
            cancellationToken);

        return Page();
    }
}
