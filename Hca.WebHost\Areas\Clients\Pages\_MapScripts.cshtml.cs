﻿using System;
using Hca.Lib.Features.Clients;

namespace Hca.WebHost.Areas.Clients.Pages
{
    public class _MapScriptsModel
    {
        public _MapScriptsModel(PropertyDtoExtended property, AddressDto address)
        {
            property.Address ??= address;
            DisplayText = property.GetDisplayText();
            Lat = property.Address?.Lat;
            Lon = property.Address?.Lon;
            DirectUrl = Urls.ClientProperty(property.ClientId, property.Id);
            NextInspection = property.NextInspection;
        }

        public _MapScriptsModel(SiteDtoExtended site, AddressDto address)
        {
            site.Address ??= address;
            DisplayText = site.GetDisplayText();
            Lat = site.Address?.Lat;
            Lon = site.Address?.Lon;
            DirectUrl = Urls.ClientSite(site.ClientId, site.Id);
            PropertyCount= site.PropertyCount;
        }

        public string DisplayText { get; set; }

        public double? Lat { get; set; }

        public double? Lon { get; set; }

        public string DirectUrl { get; set; }

        public DateTime? NextInspection { get; set; }

        public int PropertyCount { get; set; } = 1;

        public string PointerColour { get; set; }

        public bool ShowMapNumbers { get; set; } = false;
    }
}

