﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class FindInspectionReports : IQueryMany<InspectionReportDto>
    {
        public FindInspectionReports(Guid inspectionId) 
        {
            InspectionId = inspectionId;
        }

        public Guid InspectionId { get; }
    }

    public class FindInspectionReportsHandler : <PERSON><PERSON>RequestHandler<FindInspectionReports, DtoSet<InspectionReportDto>>
    {
        public FindInspectionReportsHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<InspectionReportDto>> OnHandleAsync(IDbHelper db, FindInspectionReports request) =>
            DtoSet.From(await db.GetListAsync<InspectionReportDto>(
                $"WHERE {nameof(InspectionReportDto.InspectionId)} = @{nameof(request.InspectionId)}",
                request));
    }
}
