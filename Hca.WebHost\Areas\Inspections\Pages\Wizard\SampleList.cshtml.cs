﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

[IgnoreAntiforgeryToken(Order = 1001)]
public class SampleListModel : HcaPageModel
{
    public SampleListModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task OnGetAsync(Guid inspectionId, string selected, CancellationToken cancellationToken = default)
    {
        Samples = (await _mediator.Send(new GetSamplesByInspectionId(inspectionId), cancellationToken)).Items;
        if (Guid.TryParse(selected, out var selectedId))
        {
            Selected = selectedId;
        }
    }

    public IEnumerable<InspectionSampleDto> Samples { get; private set; }
    public Guid? Selected { get; private set; }

    public async Task<IActionResult> OnPostAsync(
        Guid inspectionId,
        [FromBody] AddNewSampleModel model,
        CancellationToken cancellationToken)
    {
        var sampleId = Guid.NewGuid();

        await _mediator.Send(
            new AddSampleToInspection(
                inspectionId,
                sampleId,
                model.SampleReference,
                model.SampleNotes,
                null,
                Guid.Empty,
                Guid.Empty,
                null,
                null,
                null,
                null,
                Guid.Empty,
                null,
                null,
                null,
                Guid.Empty,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                0,
                Guid.Empty,
                Guid.Empty),
            cancellationToken);

        return new OkObjectResult(await _mediator.Send(new GetInspectionSampleById(sampleId), cancellationToken));
    }
}

[JsonObject]
public class AddNewSampleModel
{
    [JsonProperty(Required = Required.Always, PropertyName = "sampleReference")]
    public string SampleReference { get; set; }
    [JsonProperty(PropertyName = "sampleNotes")]
    public string SampleNotes { get; set; }
}
