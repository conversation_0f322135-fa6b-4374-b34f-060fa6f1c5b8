﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientLogoModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientLogoModel(ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public ClientDto Client { get; private set; }

    [BindProperty]
    public IFormFile Logo { get; set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        if (Logo != null)
        {
            using var ms = new MemoryStream();
            Logo.CopyTo(ms);
            await _clientService.UpdateClientLogoAsync(clientId, Logo.FileName, ms.ToArray(), cancellationToken);
        }

        return Redirect(Urls.Client(clientId));
    }
}
