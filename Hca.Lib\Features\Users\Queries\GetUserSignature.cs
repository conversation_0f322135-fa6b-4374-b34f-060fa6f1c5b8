﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Users.Queries
{
    public class GetUserSignature : IRequest<string>
    {
        public GetUserSignature(Guid userId)
        {
            UserId = userId;
        }

        public Guid UserId { get; }
    }

    public class GetUserSignatureHandler : DapperRequestHandler<GetUserSignature, string>
    {
        private readonly IMediator _mediator;

        public GetUserSignatureHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public async override Task<string> OnHandleAsync(IDbHelper db, GetUserSignature request)
        {
            var user = await db.GetAsync<UserDto>(request.UserId);
            var sig = await _mediator.Send(new GetSasUrl("users", user.SignatureFileName, false));
            return sig.AbsoluteUri;
        }
    }
}
