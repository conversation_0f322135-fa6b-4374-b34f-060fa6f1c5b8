﻿using Hca.WebHost.Pipeline;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("delete")]
    public class FormDeleteRowTagHelper : TagHelper
    {
        private readonly IHtmlGenerator _html;
        private readonly ViewManager _viewManager;

        public FormDeleteRowTagHelper(IHtmlGenerator html, ViewManager viewManager) : base()
        {
            _html = html;
            _viewManager = viewManager;
        }

        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("item-name")]
        public string ItemName { get; set; }

        [ViewContext]
        [HtmlAttributeNotBound]
        public ViewContext ViewContext { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            if (!_viewManager.UserCanDelete.Result)
            {
                output.TagName = "span";
                output.Attributes.SetAttribute("style", "display: none");
                return;
            }

            output.TagMode = TagMode.StartTagAndEndTag;
            output.TagName = "form";
            output.Attributes.Add("method", "post");
            output.Attributes.Add("style", "display: inline;");
            output.Attributes.Add("action", Action);
            
            output.PreContent.SetHtmlContent(
                _html.GenerateAntiforgery(ViewContext).ToHtmlString() +
                $"<button class=\"btn btn-sm btn-danger mr-2\" data-confirm-dialog=\"Are you sure you wish to delete {ItemName}?\">" +
                $"<em class=\"fa fa-trash-alt fa-fw\"></em>");

           // output.Content.SetContent("delete");

            output.PostContent.SetHtmlContent("</button>");
        }
    }
}
