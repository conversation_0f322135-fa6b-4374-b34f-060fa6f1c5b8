﻿using System;
using Hca.Lib.Features.Users;

namespace Hca.Lib.Identity
{
    public class HcaRole
    {
        public string Name { get; set; }

        public const string ROLE_CLIENT = "Client";

        public const string ROLE_HCA = "Hca";

        public const string ROLE_ADMIN = "Admin";
    }

    public static class HcaRoleExtensions
    {
        public static string ToTokenString(this UserRole role)
        {
            return role switch
            {
                UserRole.Admin => HcaRole.ROLE_ADMIN,
                UserRole.Hca => HcaRole.ROLE_HCA,
                UserRole.Client => HcaRole.ROLE_CLIENT,
                _ => throw new ArgumentException(null, nameof(role))
            };
        }
    }
}
