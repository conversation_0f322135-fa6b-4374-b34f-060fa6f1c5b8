﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Xml.Linq;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using Hca.Lib.Features.Documents.Fields;
using OpenXmlPowerTools;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class SectionExtensions
    {
        public static CompiledDocument AddSections(
            this CompiledDocument doc,
            IEnumerable<DocumentSection> sections)
        {
            if (!sections?.Any() ?? true) return doc;

            using var wordDoc = doc.AsWordProcessingDocument;
            var mainPart = wordDoc.MainDocumentPart;

            foreach (var section in sections)
            {
                var tagName = "{{" + section.SectionCode ?? section.SectionTitle + "}}";

                // consolidate into a single run if already across several runs
                TextReplacer.SearchAndReplace(wordDoc, tagName, tagName, true);

                var tagToReplace = mainPart.FindFirstTextElement(tagName);
                if (tagToReplace == null) return doc;

                var insertAfter = tagToReplace.Parent.Parent;

                if (!string.IsNullOrWhiteSpace(section.SectionTitle))
                {
                    insertAfter = insertAfter.InsertAfterSelf(
                        OpenXmlFactory.CreateTextParagraph(section.SectionTitle).Heading1());
                }

                foreach (var field in section.DocumentFields)
                {
                    foreach(var element in CreateFieldElements(mainPart, (dynamic)field)) //.Specialised))
                    {
                        insertAfter = insertAfter.InsertAfterSelf(element);
                    }
                }

                mainPart.Document.Body.RemoveChild(tagToReplace.Parent.Parent);
            }

            mainPart.Document.Save();

            return doc;
        }

        //private static OpenXmlElement[] CreateFieldElements(MainDocumentPart mainDocumentPart, Field field)
        //{
        //    // no-op for dynamic
        //    return Array.Empty<OpenXmlElement>();
        //}

        private static OpenXmlElement[] CreateFieldElements(MainDocumentPart mainDocumentPart, DocumentTextField field)
        {
            if (field.Value.IsHtml)
            {
                var html = System.Net.WebUtility.HtmlDecode(field.Value.Actual);
                var parsedElement = XElement.Parse(html);

                // Loosing all inline styles
                var convertedWml = HtmlToWmlConverter.ConvertHtmlToWml(
                    String.Empty,
                    String.Empty,
                    String.Empty,
                    parsedElement,
                    HtmlToWmlConverter.GetDefaultSettings());

                return convertedWml.MainDocumentPart.Elements().Select(e => e.ToOpenXml()).ToArray();
            }

            //var result = new List<OpenXmlElement>();
            //var textLines = field.Value.Actual.Split(new[] { Environment.NewLine, "\v" }, StringSplitOptions.None);

            //var previousEmpty = false;

            //foreach (string line in textLines)
            //{
            //    if (!string.IsNullOrEmpty(line)) previousEmpty = false;
            //    else if(!previousEmpty) previousEmpty = true;

            //    var lineText = line.Trim();

            //}

            return new[] { OpenXmlFactory.CreateTextParagraph(field.Value.Actual) };
        }

        private static OpenXmlElement[] CreateFieldElements(MainDocumentPart mainDocumentPart, DocumentChoiceField field)
        {
            return field.Value.Choices
                .Where(c => c.Chosen)
                .Select(c => OpenXmlFactory.CreateTextParagraph(c.Text))
                .ToArray();
        }
    }

    public static class XExtensions
    {
        public static OpenXmlElement ToOpenXml(this XElement xElement)
        {
            using StreamWriter sw = new(new MemoryStream());
            sw.Write(xElement.ToString());
            sw.Flush();
            sw.BaseStream.Seek(0, SeekOrigin.Begin);

            OpenXmlReader re = OpenXmlReader.Create(sw.BaseStream);

            re.Read();
            OpenXmlElement oxe = re.LoadCurrentElement();
            re.Close();

            return oxe;
        }
    }
}
