﻿@model IEnumerable<Hca.Lib.Features.Inspections.InspectionWithDetailsDto>
<table class="table table-striped table-bordered table-hover" id="tblInspections">
    <thead class="thead-dark">
        <tr>
            <th>Client Name</th>
            <th>Location Name</th>
            <th></th>
        </tr>
    </thead>
    <tbody>
        @foreach (var inspection in Model)
        {
        <tr>
            <td>@inspection.ClientName</td>
            <td>@inspection.LocationCode</td>
            <td>
                <button type="button" class="btn btn-sm btn-info mr-2 command-edit" data-row-id="@inspection.Id">
                    <em class="fa fa-edit fa-fw"></em>edit
                </button>
                <a class="btn btn-sm btn-info mr-2 command-edit" href="Inspections/@inspection.Id/Samples/New">
                    <em class="fa fa-plus fa-fw"></em>add unique inspection record
                </a>
            </td>
        </tr>
        }
    </tbody>
</table>


@this.ScriptBlock(
@<script type='text/javascript'>
    $('#tblInspections button').on('click', (e) => {
        window.location.href = '/inspections/' + $(e.currentTarget).data("row-id");
    });
</script>)
