﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetInspectionDefaultTemplate : IRequest<Guid?>
    {
        public GetInspectionDefaultTemplate(Guid inspectionTypeId)
        {
            InspectionTypeId = inspectionTypeId;
        }

        public Guid InspectionTypeId { get; }
    }

    public class GetInspectionDefaultTemplateHandler : DapperRequestHandler<GetInspectionDefaultTemplate, Guid?>
    {
        public GetInspectionDefaultTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<Guid?> OnHandleAsync(IDb<PERSON><PERSON>per db, GetInspectionDefaultTemplate request)
        {
            var result = await db.ExecuteScalarAsync<Guid>(
                $"SELECT InspectionTypeDefaultTemplateId FROM {TableNames.ValueLists} WHERE Id = @{nameof(request.InspectionTypeId)}",
                request);

            return result == Guid.Empty ? null : result;
        }
    }
}
