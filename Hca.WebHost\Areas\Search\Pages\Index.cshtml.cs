﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Services;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Search.Pages;

public class IndexModel : HcaPageModel
{
    public PagedDtoSet<SearchResult> SearchResults { get; private set; }
    public string SearchTerm { get; private set; }

    [BindProperty]
    public bool ShowArchived { get; set; }
    public PagedDtoSet<SearchResult> ArchivedResults { get; private set; }

    public IndexModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
        {
        }

public async Task<IActionResult> OnGetAsync(string searchTerm, CancellationToken cancellationToken)
    {
        PagedDtoSet<SearchResult> results = await GetDataAsync(searchTerm, cancellationToken);

        if (results.TotalItems == 1)
        {
            if (results.Items.Count() != 1) throw new ApplicationException("Number of results should be one BUT IT ISN'T!");

            return new RedirectResult(BuildRedirectUrl(results.Items.Single()));
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(string searchTerm, CancellationToken cancellationToken)
    {
        PagedDtoSet<SearchResult> results = await GetDataAsync(searchTerm, cancellationToken);

        if (results.TotalItems == 1)
        {
            if (results.Items.Count() != 1) throw new ApplicationException("Number of results should be one BUT IT ISN'T!");

            return new RedirectResult(BuildRedirectUrl(results.Items.Single()));
        }

        return Page();
    }

    private async Task<PagedDtoSet<SearchResult>> GetDataAsync(string searchTerm, CancellationToken cancellationToken)
    {
        var results = await _mediator.Send(new SearchRequest(searchTerm), cancellationToken);

        SearchResults = results;

        if (ShowArchived)
        {
            ArchivedResults = await _mediator.Send(new SearchRequest(searchTerm, true), cancellationToken);
        }

        SearchTerm = searchTerm;
        return results;
    }

    public static string BuildRedirectUrl(SearchResult result) =>
        result.EntityType switch
        {
            SearchEntity.Client => $"{Urls.Clients}/{result.EntityId}",
            SearchEntity.Location => $"{Urls.Properties}/{result.EntityId}",
            _ => throw new NotImplementedException(),
        };
}
