﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using Dapper;

    [Table(TableNames.Properties)]
    public class PropertyDtoExtended : PropertyDto
    {
        [IgnoreInsert, IgnoreUpdate]
        public AddressDto Address { get; set; } // todo: remove children from DTOs, leave on models

        [IgnoreInsert, IgnoreUpdate]
        public string SiteName { get; set; }

        [IgnoreInsert, IgnoreUpdate]
        public DateTime? NextInspection { get; set; }

        [IgnoreInsert, IgnoreUpdate]
        public bool ClientIsResponsibleForCompliance { get; set; } = true;
    }
}
