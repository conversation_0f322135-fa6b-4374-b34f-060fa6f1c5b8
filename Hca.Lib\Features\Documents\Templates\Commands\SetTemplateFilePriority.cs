﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Commands
{
    public class SetTemplateFilePriority : ICommand
    {
        public SetTemplateFilePriority(Guid id, int priority)
        {
            Id = id;
            Priority = priority;
        }

        public Guid Id { get; }
        public int Priority { get; }
    }

    public class SetTemplateFilePriorityHandler : DapperRequestHandler<SetTemplateFilePriority, CommandResult>
    {
        public SetTemplateFilePriorityHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetTemplateFilePriority request)
        {
            var dto = await db.GetAsync<TemplateFileDto>(request.Id);
            dto.DisplayOrder = request.Priority;
            await db.UpdateAsync(dto);
            return CommandResult.Success();
        }
    }
}
