﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Inspections.Queries.Models;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages;

public class NewModel : HcaPageModel
{
    public NewModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task OnGetAsync([FromQuery] Guid clientId, [FromQuery] Guid propertyId, CancellationToken cancellationToken)
    {
        var client = await _mediator.Send(new GetClientByIdQuery(clientId), cancellationToken);
        var location = await _mediator.Send(new GetProperty(propertyId), cancellationToken);
        Inspection = new InspectionQueryModel
        {
            Id = Guid.NewGuid(),
            ClientId = clientId,
            PropertyId = propertyId,
            ClientName = client.ClientName,
            PropertyCode = location.PropertyCode
        };
    }

    public InspectionQueryModel Inspection { get; private set; }
}
