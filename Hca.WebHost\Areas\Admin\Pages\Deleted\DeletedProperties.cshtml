﻿@page "/deletedProperties"
@using Hca.Lib.Features.Clients
@model Hca.WebHost.Areas.Admin.Pages.DeletedPropertiesModel
@{
    ViewData["Title"] = "Deleted Properties";
}
<div class="card card-default">
    <div class="card-header">
        <h5>DELETED PROPERTIES</h5>
    </div>
    <div id="divUsers" class="card-body">
        <table class="table table-striped table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>Client Name</th>
                    <th>Site Name</th>
                    <th>Property</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var property in Model.Properties.Items)
                {
                    <tr>
                        <td>@property.ClientName</td>
                        <td>@property.SiteName</td>
                        <td>@property.GetDisplayText()</td>
                        <td class="d-flex">
                            <div class="ml-auto">
                                <anchor action="@Urls.ClientProperty(property.ClientId, property.Id)" text="view" style=""></anchor>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>

        @if (Model.Properties.TotalPages > 1)
        {
            <nav>
                <ul class="pagination">
                    @if (Model.Properties.CurrentPage > 1)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Properties.CurrentPage - 1)">Previous</a>
                        </li>
                    }

                    @for (int i = 1; i <= Model.Properties.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.Properties.CurrentPage ? "active" : "")">
                            <a class="page-link" href="?pageNum=@i">@i</a>
                        </li>
                    }

                    @if (Model.Properties.CurrentPage < Model.Properties.TotalPages)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Properties.CurrentPage + 1)">Next</a>
                        </li>
                    }
                </ul>
            </nav>
        }
    </div>
</div>

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([{ url: '@Urls.DeletedProperties', text: 'Deleted Properties' }]);
        });</script>
}