﻿using System;
using Hca.Lib.Identity;
using Hca.Lib.Identity.Stores;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;

namespace Hca.WebHost.Identity
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddHcaIdentity(this IServiceCollection services)
        {
            services
                .Configure<IdentityOptions>(options =>
                {
                    // Password settings.
                    options.Password.RequireDigit = true;
                    options.Password.RequireLowercase = true;
                    options.Password.RequireNonAlphanumeric = false;
                    options.Password.RequireUppercase = true;
                    options.Password.RequiredLength = 6;
                    options.Password.RequiredUniqueChars = 1;

                    // Lockout settings.
                    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
                    options.Lockout.MaxFailedAccessAttempts = 5;
                    options.Lockout.AllowedForNewUsers = true;

                    // User settings.
                    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
                    options.User.RequireUniqueEmail = false;

                })

                .ConfigureApplicationCookie(options =>
                {
                    // Cookie settings
                    options.Cookie.HttpOnly = true;
                    options.ExpireTimeSpan = TimeSpan.FromDays(3);

                    options.LoginPath = Urls.Login;
                    options.LogoutPath = Urls.Logout;
                    options.AccessDeniedPath = Urls.AccessDenied;
                    options.SlidingExpiration = true;
                })

                .Configure<DataProtectionTokenProviderOptions>(opt =>
                    opt.TokenLifespan = TimeSpan.FromHours(2))

                .AddTransient<IUserStore<HcaUser>, HcaUserStore>()
                .AddTransient<IRoleStore<HcaRole>, HcaRoleStore>()
                .AddIdentity<HcaUser, HcaRole>()
                .AddDefaultTokenProviders()
                .AddClaimsPrincipalFactory<ClaimsFactory>()

                .AddDefaultUI();

            services
                .AddAuthorization(options =>
                {
                    options.AddPolicy(Policies.ADMIN, policy =>
                       policy.RequireAssertion(context =>
                           context.User.HasClaim(c => c.Type == HcaClaimTypes.USERACCESS && c.Value == HcaRole.ROLE_ADMIN)));

                    options.AddPolicy(Policies.HCA, policy =>
                       policy.RequireAssertion(context =>
                           context.User.HasClaim(c => c.Type == HcaClaimTypes.USERACCESS && c.Value == HcaRole.ROLE_HCA)));

                    options.AddPolicy(Policies.CLIENT, policy =>
                       policy.RequireAssertion(context =>
                           context.User.HasClaim(c => c.Type == HcaClaimTypes.USERACCESS && c.Value == HcaRole.ROLE_CLIENT)));

                    options.AddPolicy(Policies.CANDELETE, policy =>
                        policy.RequireClaim("Permission", Permissions.CANDELETE));
                })
                .AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
                .AddCookie(options =>
                {
                    options.Cookie.HttpOnly = true;
                    options.Cookie.SameSite = SameSiteMode.Strict;
                    options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                });

            return services;
        }
    }
}
