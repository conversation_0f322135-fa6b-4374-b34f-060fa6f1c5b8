/* ========================================================================
   Component: layout
 ========================================================================== */
body, .wrapper .section-container {
  background: none;
}

.wrapper .aside-container {
  background-color: #fff;
}

/* ========================================================================
   Component: top-navbar
 ========================================================================== */
.topnavbar {
  background-color: #37bc9b;
  background-image: linear-gradient(to right, #37bc9b 0%, rgb(87.8888888889, 206.1111111111, 176.7777777778) 100%);
  background-repeat: repeat-x;
}
@media (min-width: 992px) {
  .topnavbar .navbar-nav > .nav-item.show > .nav-link, .topnavbar .navbar-nav > .nav-item.show > .nav-link:hover, .topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
    box-shadow: 0 -3px 0 rgb(48.0740740741, 164.3259259259, 135.4814814815) inset;
  }
}
.topnavbar .navbar-nav > .nav-item > .navbar-text {
  color: #fff;
}
.topnavbar .navbar-nav > .nav-item > .nav-link,
.topnavbar .navbar-nav > .nav-item.show > .nav-link {
  color: #fff;
}
.topnavbar .navbar-nav > .nav-item > .nav-link:hover, .topnavbar .navbar-nav > .nav-item > .nav-link:focus,
.topnavbar .navbar-nav > .nav-item.show > .nav-link:hover,
.topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
  color: rgb(31.9135802469, 109.0864197531, 89.9382716049);
}
.topnavbar .dropdown-item.active, .topnavbar .dropdown-item:active {
  background-color: #37bc9b;
}

/* ========================================================================
   Component: sidebar
 ========================================================================== */
.sidebar {
  background-color: #fff;
}
.sidebar .nav-heading {
  color: #000;
}

.sidebar-nav > li > a, .sidebar-nav > li > .nav-item {
  color: #000;
}
.sidebar-nav > li > a:focus, .sidebar-nav > li > a:hover, .sidebar-nav > li > .nav-item:focus, .sidebar-nav > li > .nav-item:hover {
  color: #37bc9b;
}
.sidebar-nav > li > a > em, .sidebar-nav > li > .nav-item > em {
  color: inherits;
}
.sidebar-nav > li.active, .sidebar-nav > li.active > a, .sidebar-nav > li.active > .nav-item, .sidebar-nav > li.active .sidebar-nav, .sidebar-nav > li.open, .sidebar-nav > li.open > a, .sidebar-nav > li.open > .nav-item, .sidebar-nav > li.open .sidebar-nav {
  background-color: rgb(252.45, 252.45, 252.45);
  color: #37bc9b;
}
.sidebar-nav > li.active > .nav-item > em, .sidebar-nav > li.active > a > em, .sidebar-nav > li.open > .nav-item > em, .sidebar-nav > li.open > a > em {
  color: #37bc9b;
}
.sidebar-nav > li.active {
  border-left-color: #37bc9b;
}

.sidebar-subnav {
  background-color: #fff;
}
.sidebar-subnav > .sidebar-subnav-header {
  color: #000;
}
.sidebar-subnav > li > a, .sidebar-subnav > li > .nav-item {
  color: #000;
}
.sidebar-subnav > li > a:focus, .sidebar-subnav > li > a:hover, .sidebar-subnav > li > .nav-item:focus, .sidebar-subnav > li > .nav-item:hover {
  color: #37bc9b;
}
.sidebar-subnav > li.active > a, .sidebar-subnav > li.active > .nav-item {
  color: #37bc9b;
}
.sidebar-subnav > li.active > a:after, .sidebar-subnav > li.active > .nav-item:after {
  border-color: #37bc9b;
  background-color: #37bc9b;
}

/* ========================================================================
   Component: offsidebar
 ========================================================================== */
.offsidebar {
  border-left: 1px solid greyscale(#cccccc);
  background-color: #fff;
  color: #000;
}