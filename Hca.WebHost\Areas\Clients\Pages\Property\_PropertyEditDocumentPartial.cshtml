﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel

@if (Model.Document.QrImageUrl.IsPopulated())
{
    <div class="card card-default">
        <div class="card-header">
            <small class="text-muted">QR CODE</small>
        </div>
        <div class="card-body">
            <img src="@Model.Document.QrImageUrl" width="200" />
            <br/>
            Document URL: <a target="_blank" href="@Model.Document.QrDocumentUrl">@Model.Document.QrDocumentUrl</a>
        </div>
    </div>
}

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">EDIT PROPERTY DOCUMENT</small>
    </div>
    <form method="post">
        <div class="card-body">
            <div class="form-group row mb-2">
                <label class="col-md-2 col-form-label mb-2">Document Date</label>
                <div class="col-xl-6 col-10">
                    <div class="input-group date" id="documentDatePicker">
                        <input class="form-control" type="text" asp-for="Document.DocumentDate" />
                        <span class="input-group-append input-group-addon">
                            <span class="input-group-text fas fa-calendar-alt"></span>
                        </span>
                    </div>
                </div>
            </div>
            <div class="form-group row mb-2">
                <label class="col-md-2 col-form-label mb-2">Next Inspection Date</label>
                <div class="col-xl-6 col-10">
                    <div class="input-group date" id="nextInspectionDatePicker">
                        <input class="form-control" type="text" asp-for="Document.NextInspectionDate" />
                        <span class="input-group-append input-group-addon">
                            <span class="input-group-text fas fa-calendar-alt"></span>
                        </span>
                    </div>
                </div>
            </div>
            <select row-label="Document Type"
                    asp-for="Document.PropertyDocumentType"
                    asp-items="Html.GetEnumSelectList<Hca.Lib.Features.Clients.PropertyDocumentType>()"></select>
            <input type="text" asp-for="Document.CompanyName" row-label="Company Name" />
            <textarea asp-for="Document.Notes" row-label="Notes" rows="6" />
            <input type="hidden" asp-for="Document.Id" />
        </div>
        <save-cancel-footer CancelUrl="@Urls.ClientPropertyDocuments(Model.Client.Id, Model.Property.Id)"></save-cancel-footer>
    </form>
</div>
