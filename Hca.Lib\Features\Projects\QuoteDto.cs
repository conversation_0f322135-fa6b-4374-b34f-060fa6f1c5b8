﻿using System;
using Dapper;

namespace Hca.Lib.Features.Projects
{
    [Table("tblQuotes")]
    public class QuoteDto
    {
        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid ClientId { get; set; }

        public Guid? ContactId { get; set; }

        [Required]
        public string QuoteNumber { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid? InspectionTypeId { get; set; }

        public double BaseFee { get; set; }

        public string Notes { get; set; }
    }

    public enum QuoteState
    {
        New = 1
    }
}
