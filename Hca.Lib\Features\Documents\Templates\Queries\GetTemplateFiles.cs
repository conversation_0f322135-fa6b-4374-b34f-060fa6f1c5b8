﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateFiles : IQueryMany<TemplateFileDto>
    {
        public GetTemplateFiles(Guid templateId)
        {
            TemplateId = templateId;
        }

        public Guid TemplateId { get; }
    }

    public class GetTemplateFilesHandler : DapperRequestHandler<GetTemplateFiles, DtoSet<TemplateFileDto>>
    {
        public GetTemplateFilesHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<TemplateFileDto>> OnHandleAsync(IDbHelper db, GetTemplateFiles request)
        {
            return DtoSet.From(await db.GetListAsync<TemplateFileDto>(
                "WHERE TemplateId = @TemplateId " +
                "ORDER BY DisplayOrder ASC",
                new { request.TemplateId }));
        }
    }
}
