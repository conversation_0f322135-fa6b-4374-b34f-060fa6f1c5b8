﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Documents.Commands
{
    public class PublishDocument : ICommand
    {
        public PublishDocument(Guid documentId, string folderName, string fileName, byte[] document, string contentType)
        {
            DocumentId = documentId;
            FolderName = folderName;
            FileName = fileName;
            Document = document;
            ContentType = contentType;
        }

        public Guid DocumentId { get; }
        public string FolderName { get; }
        public string FileName { get;  }
        public byte[] Document { get; }
        public string ContentType { get; }
    }

    public class PublishDocumentHandler : DapperRequestHandler<PublishDocument, CommandResult>
    {
        private readonly IMediator _mediator;

        public PublishDocumentHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, PublishDocument request)
        {
            var uploadResult = await _mediator.Send(new UploadBlob(
                    request.FolderName,
                    request.FileName,
                    request.Document,
                    request.ContentType
                ));

            if (uploadResult.IsSuccess)
            {
                var documentDto = await db.GetAsync<DocumentDto>(request.DocumentId);
                documentDto.DocumentStatus = DocumentStatus.Published;
                documentDto.Folder = request.FolderName;
                documentDto.FileName = request.FileName;
                await db.UpdateAsync(documentDto);
            }

            return CommandResult.Success();
        }
    }
}
