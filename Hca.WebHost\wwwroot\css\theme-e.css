/* ========================================================================
   Component: layout
 ========================================================================== */
body, .wrapper .section-container {
  background: none;
}

.wrapper .aside-container {
  background-color: #3a3f51;
}

/* ========================================================================
   Component: top-navbar
 ========================================================================== */
.topnavbar {
  background-color: rgb(22.512195122, 150.6585365854, 190.487804878);
  background-image: linear-gradient(to right, rgb(22.512195122, 150.6585365854, 190.487804878) 0%, #23b7e5 100%);
  background-repeat: repeat-x;
}
@media (min-width: 992px) {
  .topnavbar .navbar-nav > .nav-item.show > .nav-link, .topnavbar .navbar-nav > .nav-item.show > .nav-link:hover, .topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
    box-shadow: 0 -3px 0 rgb(19.2780487805, 129.0146341463, 163.1219512195) inset;
  }
}
.topnavbar .navbar-nav > .nav-item > .navbar-text {
  color: #fff;
}
.topnavbar .navbar-nav > .nav-item > .nav-link,
.topnavbar .navbar-nav > .nav-item.show > .nav-link {
  color: #fff;
}
.topnavbar .navbar-nav > .nav-item > .nav-link:hover, .topnavbar .navbar-nav > .nav-item > .nav-link:focus,
.topnavbar .navbar-nav > .nav-item.show > .nav-link:hover,
.topnavbar .navbar-nav > .nav-item.show > .nav-link:focus {
  color: rgb(11.7317073171, 78.512195122, 99.2682926829);
}
.topnavbar .dropdown-item.active, .topnavbar .dropdown-item:active {
  background-color: rgb(22.512195122, 150.6585365854, 190.487804878);
}

/* ========================================================================
   Component: sidebar
 ========================================================================== */
.sidebar {
  background-color: #3a3f51;
}
.sidebar .nav-heading {
  color: #000;
}

.sidebar-nav > li > a, .sidebar-nav > li > .nav-item {
  color: #e1e2e3;
}
.sidebar-nav > li > a:focus, .sidebar-nav > li > a:hover, .sidebar-nav > li > .nav-item:focus, .sidebar-nav > li > .nav-item:hover {
  color: rgb(22.512195122, 150.6585365854, 190.487804878);
}
.sidebar-nav > li > a > em, .sidebar-nav > li > .nav-item > em {
  color: inherits;
}
.sidebar-nav > li.active, .sidebar-nav > li.active > a, .sidebar-nav > li.active > .nav-item, .sidebar-nav > li.active .sidebar-nav, .sidebar-nav > li.open, .sidebar-nav > li.open > a, .sidebar-nav > li.open > .nav-item, .sidebar-nav > li.open .sidebar-nav {
  background-color: rgb(55.871942446, 60.6884892086, 78.028057554);
  color: rgb(22.512195122, 150.6585365854, 190.487804878);
}
.sidebar-nav > li.active > .nav-item > em, .sidebar-nav > li.active > a > em, .sidebar-nav > li.open > .nav-item > em, .sidebar-nav > li.open > a > em {
  color: rgb(22.512195122, 150.6585365854, 190.487804878);
}
.sidebar-nav > li.active {
  border-left-color: rgb(22.512195122, 150.6585365854, 190.487804878);
}

.sidebar-subnav {
  background-color: #3a3f51;
}
.sidebar-subnav > .sidebar-subnav-header {
  color: #e1e2e3;
}
.sidebar-subnav > li > a, .sidebar-subnav > li > .nav-item {
  color: #e1e2e3;
}
.sidebar-subnav > li > a:focus, .sidebar-subnav > li > a:hover, .sidebar-subnav > li > .nav-item:focus, .sidebar-subnav > li > .nav-item:hover {
  color: rgb(22.512195122, 150.6585365854, 190.487804878);
}
.sidebar-subnav > li.active > a, .sidebar-subnav > li.active > .nav-item {
  color: rgb(22.512195122, 150.6585365854, 190.487804878);
}
.sidebar-subnav > li.active > a:after, .sidebar-subnav > li.active > .nav-item:after {
  border-color: rgb(22.512195122, 150.6585365854, 190.487804878);
  background-color: rgb(22.512195122, 150.6585365854, 190.487804878);
}

/* ========================================================================
   Component: offsidebar
 ========================================================================== */
.offsidebar {
  border-left: 1px solid greyscale(#cccccc);
  background-color: #fff;
  color: #000;
}