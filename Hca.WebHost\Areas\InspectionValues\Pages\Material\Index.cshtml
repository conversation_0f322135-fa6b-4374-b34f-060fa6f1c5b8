﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Material.Index
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Material Name" }
        },
        Header = "Materials",
        UrlPath = Urls.Material,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />