﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using Dapper;

    [Table(TableNames.Sites)]
    public class SiteDto
    {
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public Guid ClientId { get; set; }

        public Guid? AddressId { get; set; }

        public string SiteName { get; set; }

        public DateTime? Archived { get; set; }

        public DateTime? Deleted { get; set; }

        public string ArchiveReason { get; set; }
    }
}
