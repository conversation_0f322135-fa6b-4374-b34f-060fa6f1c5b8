﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using MediatR;

namespace Hca.Lib.Features.Users.Queries
{
    public class GetCurrentUser : IMessage<UserDto>
    {
    }

    public class GetCurrentUserHandler : IRequestHandler<GetCurrentUser, UserDto>
    {
        private readonly IMediator _mediator;

        public GetCurrentUserHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<UserDto> Handle(GetCurrentUser request, CancellationToken cancellationToken) =>
            _mediator.Send(new GetUser(request.CurrentUserId), cancellationToken);
    }
}
