﻿using System.Threading.Tasks;
using Hca.Lib.Identity;
using Hca.WebHost.Pipeline;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;

namespace Hca.WebHost.Areas.Identity.Pages.Account
{
    [IgnoreAntiforgeryToken]
    [AllowAnonymous]
    public class LogoutModel : PageModel
    {
        private readonly ViewManager _viewManager;
        private readonly SignInManager<HcaUser> _signInManager;
        private readonly ILogger<LogoutModel> _logger;

        public LogoutModel(
            SignInManager<HcaUser> signInManager, 
            ILogger<LogoutModel> logger, 
            ViewManager viewManager)
        {
            _signInManager = signInManager;
            _logger = logger;
            _viewManager = viewManager;
        }

        public async Task<IActionResult> OnPost(string returnUrl = null)
        {
            await HttpContext.SignOutAsync();
            await _signInManager.SignOutAsync();

            if (returnUrl != null)
            {
                return LocalRedirect(returnUrl);
            }
            else
            {
                // This needs to be a redirect so that the browser performs a new
                // request and the identity for the user gets updated.
                return Redirect(string.IsNullOrEmpty(_viewManager.ClientPageName) ? "/" : $"/clients/{_viewManager.ClientPageName}");
            }
        }
    }
}
