﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Commands
{
    public class CreateProject : ICommand<Guid>
    {
        public CreateProject(
            string projectNumber,
            Guid clientId,
            Guid propertyId,
            Guid inspectionTypeId)
        {
            ProjectNumber = projectNumber;
            ClientId = clientId;
            PropertyId = propertyId;
            InspectionTypeId = inspectionTypeId;
        }

        public string ProjectNumber { get; }
        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public Guid InspectionTypeId { get; }
    }

    public class CreateProjectHandler : DapperRequestHandler<CreateProject, CommandResult<Guid>>
    {
        private readonly IMediator _mediator;

        public CreateProjectHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult<Guid>> OnHandleAsync(IDbHelper db, CreateProject request)
        {
            var project = new ProjectDto
            {
                Id = Guid.NewGuid(),
                ClientId = request.ClientId,
                ProjectNumber = request.ProjectNumber
            };
            await db.InsertAsync(project);

            var inspectionCreateResult =
                await _mediator.Send(new CreateInspection(
                    Guid.NewGuid(),
                    request.InspectionTypeId,
                    project.Id,
                    request.PropertyId));

            if (!inspectionCreateResult.IsSuccess)
            {
                return CommandResult<Guid>.Fail(inspectionCreateResult.Reason);
            }

            return CommandResult<Guid>.Success(project.Id);
        }
    }
}
