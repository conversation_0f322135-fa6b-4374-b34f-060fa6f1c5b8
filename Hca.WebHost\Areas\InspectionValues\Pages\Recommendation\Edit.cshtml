﻿@page "/InspectionValues/Recommendation/{id:guid}"
@model Hca.WebHost.Areas.InspectionValues.Pages.Recommendation.Edit
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Recommendation Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Recommendation",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />