﻿@page "/clients/search"
@model Hca.WebHost.Areas.Clients.Pages.ClientSearchModel
@{
    Layout = null;
}
<div class="card card-default">
    <div class="card-body">
        <div class="table-responsive">
            <partial name="Index\_SearchResultsTablePartial" model="Model.SearchResults.Items" />
            <br />
            <form method="get"><input asp-for="ShowArchivedClients" onclick="this.form.submit()" /> Show Archived Clients</form>
            @if (Model.ShowArchivedClients)
            {
                <br />
                @if (Model.ArchivedResults.Items.Any())
                {
                    <partial name="Index\_SearchResultsTablePartial" model="Model.ArchivedResults.Items" />
                }
                else
                {
                    <p>No archived clients</p>
                }
            }
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex">
            <nav class="ml-auto">
                <ul class="pagination pagination-sm">
                    @for (int i = 1; i <= Model.SearchResults.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.SearchResults.CurrentPage ? " active" : "")">
                            <a class="page-link" href="#" onclick="SearchClients(@i, 20); return false;">@i</a>
                        </li>
                    }
                </ul>
            </nav>
        </div>
    </div>
</div>