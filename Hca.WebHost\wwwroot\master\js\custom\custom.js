// Custom Code
// -----------------------------------

(function (w) {
    'use strict';

    $(initCustom);

    var materialType = $('#ddlMaterialProductType');
    var materialDamage = $('#ddlMaterialDamage');
    var materialSurface = $('#ddlMaterialSurface');
    var materialAsbestosType = $('#ddlMaterialAsbestosType');
    var priorityActivity = $('#ddlPriorityActivity');
    var priorityDisturbance = $('#ddlPriorityDisturbance');
    var priorityOccupants = $('#ddlPriorityOccupants');
    var priorityMaintenance = $('#ddlPriorityMaintenance');
    var totalRiskScore = $('#txtTotalRiskScore');
    var totalRiskLevel = $('#txtRiskLevel');
    var accessibility = $('#ddlAccessibility');

    var bgVeryLow = "bg-success-dark";
    var bgLow = "bg-success";
    var bgMedium = "bg-warning";
    var bgHigh = "bg-danger-dark";

    function inputRiskScore(input) {
        var value = input[0].value;
        return $.isNumeric(value) ? parseInt(value) : 0;
    }

    function totalRiskCount() {
        var totalScore =
            inputRiskScore(materialType) +
            inputRiskScore(materialDamage) +
            inputRiskScore(materialSurface) +
            inputRiskScore(materialAsbestosType) +
            inputRiskScore(priorityActivity) +
            inputRiskScore(priorityDisturbance) +
            inputRiskScore(priorityMaintenance) +
            inputRiskScore(priorityOccupants);
        var risk = "Very Low";
        var riskClass = bgVeryLow;

        if (totalScore >= 9 && totalScore <= 13) {
            risk = "Low";
            riskClass = bgLow;
        } else if (totalScore >= 14 && totalScore <= 19) {
            risk = "Medium";
            riskClass = bgMedium;
        } else if (totalScore >= 20) {
            risk = "High";
            riskClass = bgHigh;
        }

        totalRiskScore.val(totalScore);
        totalRiskLevel.val(risk);
        totalRiskLevel.removeClass(bgVeryLow);
        totalRiskLevel.removeClass(bgLow);
        totalRiskLevel.removeClass(bgMedium);
        totalRiskLevel.removeClass(bgHigh);
        totalRiskLevel.addClass(riskClass);
    }

    function colourAccessibility() {
        accessibility.removeClass(bgLow);
        accessibility.removeClass(bgMedium);
        accessibility.removeClass(bgHigh);

        if (accessibility[0].value === "3") {
            accessibility.addClass(bgLow);
        } else if (accessibility[0].value === "2") {
            accessibility.addClass(bgMedium);
        } else if (accessibility[0].value === "1") {
            accessibility.addClass(bgHigh);
        }
    }

    function confirmDialog(e) {
        const { target, nativeEvent } = e;
        const clonedNativeEvent = new MouseEvent('click', nativeEvent);

        if (target.realClick) {
            target.realClick = false;
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        swal({
            title: 'Are you sure?',
            text: $(target).attr('data-confirm-dialog'),
            icon: 'warning',
            buttons: {
                cancel: true,
                confirm: {
                    text: 'Confirm',
                    value: true,
                    visible: true,
                    className: "bg-danger",
                    closeModal: false
                }
            }
        }).then(function (isConfirm) {
            if (isConfirm === true) {
                target.realClick = true;
                target.dispatchEvent(clonedNativeEvent);
            }
        });
    }

    function archiveDialog(e) {
        const { target, nativeEvent } = e;
        const clonedNativeEvent = new MouseEvent('click', nativeEvent);
        const inputName = $(target).attr('data-archive-field'); // Get the name of the input field

        if (target.realClick) {
            target.realClick = false;
            return;
        }

        e.preventDefault();
        e.stopPropagation();

        swal({
            text: 'To archive please provide a reason',
            title: $(target).attr('data-archive-dialog'),
            content: "input",
            icon: 'warning',
            buttons: {
                cancel: true,
                confirm: {
                    text: 'Confirm',
                    value: true,
                    visible: true,
                    className: "bg-danger",
                    closeModal: false
                }
            }
        }).then(function (value) {
            if (!value) {
                swal("You need to write something!", "", "error");
                return false;
            } else {
                const form = target.closest('form');
                let hiddenInput = form.querySelector(`input[type="hidden"][name="${inputName}"]`);

                // Create hidden input if it does not exist
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = inputName;
                    form.appendChild(hiddenInput);
                }

                // Set the value of the hidden input field
                hiddenInput.value = value;
                target.realClick = true;
                target.dispatchEvent(clonedNativeEvent);
            }
        });
    }

    function padZero(str, len) {
        len = len || 2;
        var zeros = new Array(len).join('0');
        return (zeros + str).slice(-len);
    }

    function invertColor(hex, bw) {
        if (hex.indexOf('#') === 0) {
            hex = hex.slice(1);
        }
        // convert 3-digit hex to 6-digits.
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }
        if (hex.length !== 6) {
            throw new Error('Invalid HEX color.');
        }
        var r = parseInt(hex.slice(0, 2), 16),
            g = parseInt(hex.slice(2, 4), 16),
            b = parseInt(hex.slice(4, 6), 16);
        if (bw) {
            // http://stackoverflow.com/a/3943023/112731
            return (r * 0.299 + g * 0.587 + b * 0.114) > 186
                ? '#000000'
                : '#FFFFFF';
        }
        // invert color components
        r = (255 - r).toString(16);
        g = (255 - g).toString(16);
        b = (255 - b).toString(16);
        // pad each with zeros and return
        return "#" + padZero(r) + padZero(g) + padZero(b);
    }

    function sortUpdate(e) {
        var items = $.map(e.detail.destination.items, function (from, index) {
            return {
                priority: index,
                id: $(from).data('sortId'),
            };
        });
        var sortUrl = $(e.detail.item).data('sortUrl');

        // todo: spinner
        $.post(sortUrl, { values: items });
    }

    function setFloorPlanColourEnabled(trueOrFalse) {
        var floorPlanColour = $('#ddlFloorColour');
        floorPlanColour.prop("disabled", !trueOrFalse);
    }

    function setMaterialEnabled(trueOrFalse) {
        $('#ddlMaterial').prop("disabled", !trueOrFalse);
        $('#ddlMaterialColour').prop("disabled", !trueOrFalse);
        $('#ddlQuantity').prop("disabled", !trueOrFalse);
        $('#txtQuantity').prop("disabled", !trueOrFalse);
        $('#PostModel_Quantity').prop("disabled", !trueOrFalse);
    }

    function initUirForm() {
        $('#chkAsbestosPresumed').change(function () {
            setFloorPlanColourEnabled($(this).is(':checked'));
        });
        setFloorPlanColourEnabled(false);

        $('#ddlFloorColour').change(
            function () {
                var color = $($('option:selected', this)[0]).data('hexColour');
                $(this).css('background-color', color);
                $(this).css('color', invertColor(color, true));
            }
        );

        $('#ddlMaterialColour').change(
            function () {
                var color = $($('option:selected', this)[0]).data('hexColour');
                $(this).css('background-color', color);
                $(this).css('color', invertColor(color, true));
            }
        );

        $('#ddlStatus').change(
            function () {
                var status = $($('option:selected', this)[0]).val();
                setMaterialEnabled(status !== 'NA');
            }
        );
        setMaterialEnabled(false);

        materialType.change(totalRiskCount);
        materialDamage.change(totalRiskCount);
        materialSurface.change(totalRiskCount);
        materialAsbestosType.change(totalRiskCount);
        priorityActivity.change(totalRiskCount);
        priorityDisturbance.change(totalRiskCount);
        priorityMaintenance.change(totalRiskCount);
        priorityOccupants.change(totalRiskCount);

        accessibility.change(colourAccessibility);
    }

    function initSortable() {
        if (typeof sortable === 'undefined') return;

        if ($('.sortable').length === 0) return;

        sortable('.sortable', {
            forcePlaceholderSize: true,
            items: 'tr',
            placeholder: '<tr class="box-placeholder p0 m0"><td colspan="10"></td></tr>',
        })[0].addEventListener('sortupdate', sortUpdate);
    }

    function initConfirmDialog() {
        $('[data-confirm-dialog]').click(confirmDialog);
        $('[data-archive-dialog]').click(archiveDialog);
    }

    function initCustom() {
        $(initConfirmDialog);
        w.initConfirmDialog = initConfirmDialog;

        $(initSortable);

        $(initUirForm);
    }

})(window);