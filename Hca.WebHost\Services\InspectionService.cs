﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using Hca.Lib.Features.Clients.Services;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Commands;
using Hca.Lib.Features.Inspections.Queries;
using Hca.Lib.Features.Inspections.Queries.Models;
using MediatR;
using static Hca.WebHost.Areas.Inspections.Pages.Sample.NewModel;

namespace Hca.WebHost.Services
{
    public class InspectionService
    {
        private readonly IMediator _mediator;

        public InspectionService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<Guid> AddSample(Guid inspectionId, PostSampleModel model, CancellationToken cancellationToken)
        {
            var sampleId = Guid.NewGuid();

            await _mediator.Send(new AddSampleToInspection(
                inspectionId,
                sampleId,
                model.InspectionRecord,
                model.AdditionalComments,
                model.Status,
                model.FloorPlanColour,
                model.Floor,
                model.FloorActual,
                model.FloorPlanReference,
                model.RoomUsage,
                model.Location,
                model.Material,
                model.MaterialActual,
                model.MaterialColour,
                model.Quantity,
                model.QuantityUnit,
                model.ProductType,
                model.ExtentOfDamage,
                model.SurfaceTreatment,
                model.AsbestosType,
                model.OccupantActivity,
                model.LikelihoodOfDisturbance,
                model.NumberOfOccupants,
                model.TypeOfMaintenance,
                model.Accessibility,
                model.ManagementRecommendation,
                model.Priority), cancellationToken);

            return sampleId;
        }

        public Task<InspectionQueryModel> GetAsync(Guid inspectionId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetInspection(inspectionId), cancellationToken);

        public Task<IEnumerable<InspectionQueryModel>> GetForProjectAsync(Guid projectId, CancellationToken cancellationToken) =>
            _mediator.Send(new FindInspections(projectId), cancellationToken).ContinueWith(r => r.Result.Items);

        public Task AddSampleImage(Guid inspectionId, Guid sampleId, string filename, Stream image)
        {
            using var ms = new MemoryStream();
            image.CopyTo(ms);
            return _mediator.Send(new UploadSampleImage(inspectionId, sampleId, filename, ms.ToArray()));
        }

        public Task<IEnumerable<InspectionReportDto>> GetInspectionReports(Guid inspectionId, CancellationToken cancellationToken) =>
            _mediator.Send(new FindInspectionReports(inspectionId), cancellationToken).ContinueWith(r => r.Result.Items);

        public Task<Guid?> GetDefaultInspectionReportTemplate(Guid inspectionTypeId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetInspectionDefaultTemplate(inspectionTypeId), cancellationToken);

        public Task SetDefaultInspectionReportTemplate(Guid inspectionTypeId, Guid templateId, CancellationToken cancellationToken) =>
            _mediator.Send(new SetInspectionDefaultTemplate(inspectionTypeId, templateId), cancellationToken);

        public Task CreateInspectionAsync(Guid inspectionId, Guid inspectionTypeId, Guid propertyId, Guid projectId, CancellationToken cancellationToken) =>
            _mediator.Send(new CreateInspection(inspectionId, inspectionTypeId, projectId, propertyId), cancellationToken);
    }
}
