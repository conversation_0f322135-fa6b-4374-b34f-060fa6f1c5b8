﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Projects;
using Hca.Lib.Features.Users;

namespace Hca.Lib.Features.Clients.Commands
{
    public class DeleteClient : ICommand
    {
        public DeleteClient(Guid clientId, DateTime? deleted = null)
        {
            ClientId = clientId;
            Deleted = deleted ?? DateTime.UtcNow;
        }

        public Guid ClientId { get; }
        public DateTime? Deleted { get; }
    }

    public class DeleteClientHandler : DapperRequestHandler<DeleteClient, CommandResult>
    {
        public DeleteClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteClient request)
        {
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Clients} SET {nameof(ClientDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}", request);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET {nameof(SiteDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(SiteDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(SiteDto.Deleted)} IS NULL", request);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(PropertyDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(PropertyDto.Deleted)} IS NULL", request);

            return CommandResult.Success();
        }
    }
}
