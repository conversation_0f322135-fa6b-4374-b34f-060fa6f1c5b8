﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries;

public class GetClientContact : IQuery<ContactDto>
{
    public GetClientContact(
        Guid clientId,
        Guid contactId)
    {
        ClientId = clientId;
        ContactId = contactId;
    }

    public Guid ClientId { get; }
    public Guid ContactId { get; }
}

public class GetClientContactHandler : DapperRequestHandler<GetClientContact, ContactDto>
{
    public GetClientContactHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<ContactDto> OnHandleAsync(IDbHelper db, GetClientContact request)
    {
        var sql = $"WHERE {nameof(ContactDto.Id)}=@{nameof(GetClientContact.ContactId)} AND {nameof(ContactDto.ClientId)}=@{nameof(GetClientContact.ClientId)}";
        var dto = await db.GetAsync<ContactDto>(sql, request);

        return dto;
    }
}
