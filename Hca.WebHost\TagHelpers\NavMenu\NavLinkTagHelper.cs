﻿using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers.NavMenu
{
    [HtmlTargetElement("nav-link")]
    public class NavLink : TagHelper
    {
        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("text")]
        public string Text { get; set; }

        [HtmlAttributeName("type")]
        public NavButtonType Type { get; set; } = NavButtonType.Edit;

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "li";
            output.Attributes.Add("class", "nav-item");

            output.PreContent.SetHtmlContent(
                $"<a class=\"nav-link d-flex btn {Type.ClassName()}\" style=\"width: 100%; margin: 5px 0;\" href=\"{Action}\">");

            output.Content.SetContent(Text ?? "link");

            output.PostContent.SetHtmlContent("</a>");
        }
    }
}
