﻿using System;
using Dapper;

namespace Hca.Lib.Features.Templates
{
    [Table("tblTemplateFiles")]
    public class TemplateFileDto
    {
        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid TemplateId { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string FileName { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public int DisplayOrder { get; set; }

        [IgnoreInsert]
        [IgnoreUpdate]
        public DateTime Created { get; set; }
    }
}
