﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Documents.Queries;
using Hca.Lib.Features.Inspections.Publishing.Commands;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

public class PublishModel : HcaPageModel
{
    public PublishModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public Guid InspectionId { get; private set; }
    public IEnumerable<DocumentDto> Reports { get; private set; }

    [BindProperty]
    [Description("Send to email address")]
    public string SendToEmailAddress { get; set; }

    [BindProperty]
    public Guid? ReportId { get; set; }

    public async Task OnGetAsync(Guid inspectionId, CancellationToken cancellationToken)
    {
        InspectionId = inspectionId;
        Reports = (await _mediator.Send(new FindDocuments(inspectionId), cancellationToken)).Items;
        ReportId = Reports.SingleOrDefault()?.Id;
    }

    public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        // todo: this won't work as it doesn't refer to a specific document
        await _mediator.Send(new PublishInspectionReport(Guid.Empty, ReportId.Value, SendToEmailAddress), cancellationToken);
        return RedirectToPage("Inspections");
    }
}
