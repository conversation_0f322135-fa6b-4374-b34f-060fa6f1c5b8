﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientPropertyDocumentDeleteModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientPropertyDocumentDeleteModel(
        ClientService clientService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnPostAsync(
        Guid propertyId,
        string id,
        CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(Guid.Parse(id), ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        var deleteResult = await _clientService.DeletePropertyDocumentAsync(Guid.Parse(id), cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.ClientPropertyDocuments(Guid.Empty, propertyId));
        }

        ErrorMessage = deleteResult.Reason;

        return Page();
    }
}
