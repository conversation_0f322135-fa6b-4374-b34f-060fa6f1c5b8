﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Projects.Commands;
using Hca.Lib.Features.Projects.Queries;
using Hca.Lib.Features.Projects.Queries.Models;
using MediatR;

namespace Hca.WebHost.Services
{
    public class ProjectService
    {
        private readonly IMediator _mediator;

        public ProjectService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<IEnumerable<ProjectQueryModel>> FindProjects(CancellationToken cancellationToken) =>
            _mediator.Send(new FindProjects(), cancellationToken).ContinueWith(t => t.Result.Items);

        public Task<ProjectQueryModel> GetProjectAsync(Guid id, CancellationToken cancellationToken) =>
            _mediator.Send(new GetProject(id), cancellationToken);

        public Task<(Guid ProjectId, string ErrorMessage)> AddProjectAsync(
            string projectNumber,
            Guid clientId,
            Guid propertyId,
            Guid inspectionTypeId,
            CancellationToken cancellationToken) =>
            _mediator.Send(new CreateProject(
                projectNumber,
                clientId,
                propertyId,
                inspectionTypeId),
                cancellationToken)
            .ContinueWith(r => (r.Result.Value, r.Result.Reason));

        public Task<string> GetNextProjectNumber() =>
            _mediator.Send(new GetNextProjectNumber()).ContinueWith(r => r.Result.GetValue());

        public Task DeleteProject(Guid projectId, CancellationToken cancellationToken) =>
            _mediator.Send(new DeleteProject(projectId), cancellationToken);
    }
}
