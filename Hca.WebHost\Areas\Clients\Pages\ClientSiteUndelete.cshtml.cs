﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSiteUndeleteModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSiteUndeleteModel(
        ClientService clientService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        var siteId = Guid.Parse(id);

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        var deleteResult = await _clientService.UndeleteSiteAsync(clientId, siteId, cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.ClientSites(clientId));
        }

        ErrorMessage = deleteResult.Reason;
        return Page();
    }
}
