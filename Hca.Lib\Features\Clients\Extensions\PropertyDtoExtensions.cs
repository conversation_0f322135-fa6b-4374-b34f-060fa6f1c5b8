﻿using Hca.Lib.Features.Clients.Queries.Models;
using System.Collections.Generic;
using System.Linq;

namespace Hca.Lib.Features.Clients
{
    public static class PropertyDtoExtensions
    {
        public static string GetDisplayText(this PropertyDtoExtended property) => GetDisplayText(property, property.Address);

        public static string GetDisplayText(this PropertyDtoExtended property, AddressDto addressDto)
        {
            var resultParts = new List<string>
            {
                property.Custom,
                property.PropertyCode,
                property.Unit,
                property.SiteName,
                addressDto?.StreetName,
                addressDto?.Town,
                addressDto?.City,
                addressDto?.County,
                addressDto?.Country,
                addressDto?.Postcode,
            };

            return string.Join(", ", resultParts.Where(p => !p.IsNullOrWhiteSpace()).Select(p => p.Trim()));
        }

        public static string GetDisplayText(this DeletedPropertyModel property)
        {
            var resultParts = new List<string>
            {
                property.Custom,
                property.PropertyCode,
                property.Unit,
                property.SiteName,
                property.Address?.StreetName,
                property.Address?.Town,
                property.Address?.City,
                property.Address?.County,
                property.Address?.Country,
                property.Address?.Postcode,
            };

            return string.Join(", ", resultParts.Where(p => !p.IsNullOrWhiteSpace()).Select(p => p.Trim()));
        }
    }
}
