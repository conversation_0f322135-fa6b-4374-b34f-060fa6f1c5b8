﻿using System;
using System.Collections.Generic;

namespace Hca.Lib.Core
{
    public class AggregateMessageRouter : IAggregateMessageRouterWithConfig
    {
        readonly Dictionary<Type, Action<AggregateMessage>> _handlers = new();

        public void ConfigureRoute(Type message, Action<AggregateMessage> handler)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            _handlers.Add(message, handler);
        }

        public void ConfigureRoute<TMessage>(Action<TMessage> handler) where TMessage : AggregateMessage
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            _handlers.Add(typeof(TMessage), message => handler((TMessage)message));
        }

        public void Route(AggregateMessage message)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));

            if (_handlers.TryGetValue(message.GetType(), out Action<AggregateMessage> handler))
            {
                handler(message);
            }
        }
    }
}
