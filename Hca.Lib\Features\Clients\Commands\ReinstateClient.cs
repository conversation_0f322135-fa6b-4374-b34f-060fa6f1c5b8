﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class ReinstateClient : ICommand
    {
        public Guid ClientId { get; }

        public ReinstateClient(Guid clientId)
        {
            ClientId = clientId;
        }
    }

    public class ReinstateClientHandler : DapperRequestHandler<ReinstateClient, CommandResult>
    {
        public ReinstateClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, ReinstateClient request)
        {
            var archivedDate = await db.ExecuteScalarAsync<DateTime?>(
                $"SELECT {nameof(ClientDto.Archived)} " +
                $"FROM {TableNames.Clients} " +
                $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}",
                request);

            // restore the client
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Clients} SET " +
                $"{nameof(ClientDto.Archived)}=NULL, " +
                $"{nameof(ClientDto.ArchiveReason)}=NULL " +
                $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}", request);

            // restore the sites
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET " +
                $"{nameof(SiteDto.Archived)}=NULL, " +
                $"{nameof(SiteDto.ArchiveReason)}=NULL " +
                $"WHERE {nameof(SiteDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(SiteDto.Archived)}=@{nameof(archivedDate)}", 
                new { request.ClientId, archivedDate });

            // restore the properties
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET " +
                $"{nameof(PropertyDto.Archived)}=NULL, " +
                $"{nameof(PropertyDto.ArchiveReason)}=NULL " +
                $"WHERE {nameof(PropertyDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(PropertyDto.Archived)}=@{nameof(archivedDate)}",
                new { request.ClientId, archivedDate });

            return CommandResult.Success();
        }
    }
}
