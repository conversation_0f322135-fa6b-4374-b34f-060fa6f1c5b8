﻿using System;
using Dapper;

namespace Hca.Lib.Features.Clients
{
    [Table(TableNames.FloorPlans)]
    public class FloorPlanDto
    {
        public Guid Id { get; set; }

        public Guid PropertyId { get; set; }

        public string ContainerName { get; set; }

        public string BlobName { get; set; }

        public string ThumbnailName { get; set; }

        public string Notes { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid? FloorId { get; set; }

        public DateTime Created { get; set; }

        [IgnoreInsert]
        [IgnoreUpdate]
        [IgnoreSelect]
        public string FloorName { get; set; }
    }
}
