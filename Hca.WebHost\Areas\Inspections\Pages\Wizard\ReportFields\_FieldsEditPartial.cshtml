﻿@model Hca.WebHost.Areas.Inspections.Pages.Wizard.SectionModel
@using Hca.Lib.Features.Documents.Fields
@{
    Layout = null;
}
@foreach (var field in Model.Fields)
{
    var controlName = field.Value.FieldType switch
    {
        FieldType.Choice => "ReportFields/_ChoiceFieldPartial",
        FieldType.Text => "ReportFields/_TextFieldPartial",
        _ => throw new NotImplementedException(),
    };
    var formHandler = field.Value.FieldType switch
    {
        FieldType.Choice => "choices",
        FieldType.Text => "text",
        _ => throw new NotImplementedException(),
    };
    <form method="post" action="/reports/@(Model.Section.DocumentId)/sections/@(Model.Section.Id)/fields/@(field.DocumentFieldId)/@formHandler">
        <partial name="@controlName" model="@field" />

        <input name="fieldType" type="hidden" value="@field.Value.FieldType" />
    </form>

    <hr />
}