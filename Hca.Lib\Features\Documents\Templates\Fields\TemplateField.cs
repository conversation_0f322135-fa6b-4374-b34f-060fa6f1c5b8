﻿using System;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Documents.Templates.Fields
{
    public interface ITemplateField<out T> where T : Field
    {
        Guid TemplateFieldId { get; set; }
        T Value { get; }
    }

    public abstract class TemplateField<T> : ITemplateField<T> where T : Field
    {
        internal TemplateField(Guid templateFieldId)
        {
            TemplateFieldId = templateFieldId;
        }

        public Guid TemplateFieldId { get; set; }

        public T Value { get; set; }
    }
}
