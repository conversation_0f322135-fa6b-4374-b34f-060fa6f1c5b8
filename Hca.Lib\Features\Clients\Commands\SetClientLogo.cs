﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class SetClientLogo : ICommand
    {
        public SetClientLogo(
            Guid clientId,
            string logo)
        {
            ClientId = clientId;
            Logo = logo;
        }

        public string Logo { get; }
        public Guid ClientId { get; }
    }

    public class SetClientLogoHandler : DapperRequestHandler<SetClientLogo, CommandResult>
    {
        public SetClientLogoHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetClientLogo request)
        {
            var dto = await db.GetAsync<ClientDto>(request.ClientId);
            dto.LogoUrl = request.Logo;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
