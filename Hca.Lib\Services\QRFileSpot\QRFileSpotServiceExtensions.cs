﻿using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Refit;
using System;
using System.Net.Http.Headers;

namespace Hca.Lib.Services.QRFileSpot;

public static class QRFileSpotServiceExtensions
{
    public static IServiceCollection AddQrFileSpot(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment webHostEnvironment)
    {
        var config = configuration.GetSection("QrFileSpot").Get<QrFileSpotConfig>();

        if (webHostEnvironment.IsDevelopment())
        {
            QrFileSpotConfig.WebUri = "https://localhost:7057";
        }
        else if (webHostEnvironment.IsStaging())
        {
            QrFileSpotConfig.WebUri = "https://staging.example.com";
        }
        else if (webHostEnvironment.IsProduction())
        {
            QrFileSpotConfig.WebUri = "https://files.hcaqrlink.com";
        }
        else
        {
            QrFileSpotConfig.WebUri = "https://default.example.com";
        }

        services
            .AddSingleton<IQrFileSpotTokenService>(sp =>
            {
                var oauthService = sp.GetRequiredService<IQrFileSpotOAuthService>();
                return new QrFileSpotTokenService(
                            config.ClientId,
                            config.ClientSecret,
                            oauthService);
            })
            .AddRefitClient<IQrFileSpotOAuthService>()
                .ConfigureHttpClient(c =>
                {
                    c.BaseAddress = new Uri(config.ServiceUri);
                });

        services
            .AddTransient<TokenDelegatingHandler>()
            .AddRefitClient<IQrFileSpotService>()
            .ConfigureHttpClient((sp, c) =>
            {
                var tokenService = sp.GetRequiredService<IQrFileSpotTokenService>();
                c.BaseAddress = new Uri(config.ServiceUri); // "https://9vqg6.wiremockapi.cloud/");
                c.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", tokenService.GetTokenAsync().Result);
            })
            .AddHttpMessageHandler<TokenDelegatingHandler>();

        return services;
    }
}
