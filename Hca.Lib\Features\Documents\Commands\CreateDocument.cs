﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;

namespace Hca.Lib.Features.Documents.Commands
{
    public class CreateDocument : ICommand
    {
        public CreateDocument(
            Guid documentId,
            TemplateObject documentTemplate)
        {
            DocumentId = documentId;
            DocumentTemplate = documentTemplate;
        }

        public Guid DocumentId { get; }
        public TemplateObject DocumentTemplate { get; }
    }

    public class CreateDocumentHandler : DapperRequestHandler<CreateDocument, CommandResult>
    {
        public CreateDocumentHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateDocument request)
        {
            await db.InsertAsync(new DocumentDto
            {
                Id = request.DocumentId,
                TemplateCode = request.DocumentTemplate.TemplateCode,
                DocumentStatus = DocumentStatus.InProgress,
                Created = DateTime.UtcNow,
            });

            foreach(var templateSection in request.DocumentTemplate.Sections)
            {
                var sectionId = Guid.NewGuid();

                await db.InsertAsync(new DocumentSectionDto 
                {
                    SectionTitle = templateSection.SectionTitle,
                    SectionOrder = templateSection.SectionOrder,
                    SectionComplete = false,
                    DocumentId = request.DocumentId,
                    Id = sectionId,
                    SectionCode = templateSection.SectionCode,
                });

                foreach(var templateField in templateSection.Fields)
                {
                    await db.InsertAsync(
                        CreateFieldDtoFromTemplateField(sectionId, (dynamic)templateField)
                    );
                }
            }

            return CommandResult.Success();
        }

        //private DocumentFieldDto CreateFieldDtoFromTemplateField(Guid sectionId, TemplateField field) =>
        //    throw new NotImplementedException("noop");

        private static DocumentFieldDto CreateFieldDtoFromTemplateField<T>(Guid sectionId, TemplateField<T> field) where T : Field =>
            new()
            {
                DocumentSectionId = sectionId,
                FieldContent = field.Value.FieldContent,
                FieldOrder = field.Value.FieldOrder,
                FieldType = field.Value.FieldType,
                IsComplete = false,
                IsOptional = field.Value.IsOptional,
                Id = Guid.NewGuid(),
            };
    }
}
