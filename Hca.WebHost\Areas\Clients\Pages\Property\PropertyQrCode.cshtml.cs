using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Commands;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Clients.Pages.Property;

public class PropertyQrCodeModel : HcaPageModel
{
    public PropertyQrCodeModel(
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
    }

    public PropertyDto Property { get; set; }

    [BindProperty] public Guid DocumentId { get; set; }

    public async Task<IActionResult> OnGetDocumentsAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Property = await _mediator.Send(new GetProperty(propertyId), cancellationToken);
        var documents = await _mediator.Send(new GetPropertyDocuments(Property.ClientId, propertyId, true), cancellationToken);
        return new OkObjectResult(documents.Items);
    }

    public async Task<IActionResult> OnPostAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        Property = await _mediator.Send(new GetProperty(propertyId), cancellationToken);
        await _mediator.Send(new SetPropertyQrCodeContent(Property.ClientId, Property.Id, DocumentId, ""), cancellationToken);
        Property = await _mediator.Send(new GetProperty(propertyId), cancellationToken);

        return Page();
    }
}
