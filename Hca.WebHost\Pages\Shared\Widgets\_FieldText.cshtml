﻿@model (Guid Id, Hca.Lib.Features.Documents.Fields.TextField Value)
@{
    var height = Math.Max(Model.Value.Placeholder.Split(Environment.NewLine).Length, 10) * 18;
}

@if (!string.IsNullOrWhiteSpace(Model.Value.Hint))
{
    <p><strong>HINT</strong>&nbsp;&nbsp;@Model.Value.Hint</p>
}

<link href="~/Vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />

<script src="~/Vendor/x-editable/dist/bootstrap3-editable/js/bootstrap-editable.js"></script>

<div id="divActual-@(Model.Id)"
     class="form-control textarea"
     style="height: @(height)px; white-space: pre-wrap;"
     contenteditable="true">
    @Html.Raw(Model.Value.Actual ?? Model.Value.Placeholder)
</div>
<input type="hidden" name="actual" id="hidActual-@(Model.Id)" value="@(Model.Value.Actual ?? Model.Value.Placeholder)" />

@*<textarea class="form-control" name="actual" rows="3">@(Model.Actual ?? Model.Placeholder)</textarea>*@

<script>
    (() => {
        $('#divActual-@(Model.Id)').on("input", function () {
            $('#hidActual-@(Model.Id)').val(this.innerText);
        });
    })();
</script>