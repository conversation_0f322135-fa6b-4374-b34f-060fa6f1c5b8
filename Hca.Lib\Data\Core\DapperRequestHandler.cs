﻿using System.Threading;
using System.Threading.Tasks;
using MediatR;

namespace Hca.Lib.Data.Core
{
    public abstract class DapperRequestHandler<TReq, TResp> : IRequestHandler<TReq, TResp> where TReq : IRequest<TResp>
    {
        private readonly IDbHelper _dbHelper;

        public DapperRequestHandler(IDbHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        public async Task<TResp> Handle(TReq request, CancellationToken cancellationToken)
        {
            return await OnHandleAsync(_dbHelper, request);
        }

        public abstract Task<TResp> OnHandleAsync(IDbHelper db, TReq request);
    }
}
