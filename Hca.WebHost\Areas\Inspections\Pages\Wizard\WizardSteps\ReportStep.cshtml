﻿@page "/inspections/{inspectionId:guid}/reportstep"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.ReportModel
@{
    Layout = null;
}

<link href="~/Vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />

<script src="~/Vendor/x-editable/dist/bootstrap3-editable/js/bootstrap-editable.js"></script>

<div class="row">
    <div class="col-lg-4">
        <div class="card card-default">
            <div class="card-header">Report Navigator</div>

            <div class="card-body align-items-center justify-content-center">
                <div id="divNavigator" class="list-group">
                </div>
            </div>

            <div class="card-footer">
                <div class="form-group">
                    <label>Report Template</label>
                    <a id="lnkReportTemplate" href="#" data-type="select">&nbsp;</a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-8">
        <div class="card card-default">
            <div class="card-header" id="divReportHeader"></div>

            <div class="card-body container-fluid" id="divReportEditor"></div>

            <div class="card-footer">
                <button class="btn btn-primary" id="btnSubmitSection">Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    (() => {

        // on each section completion call back an event method that refreshes the report navigator
        // and loads the next incomplete section
        $('#btnSubmitSection').click((e) => {

            $('#divReportEditor form').each(function () {
                var postdata = new FormData($(this)[0]);;
                var formurl = $(this).attr("action");

                $.ajax({
                    async: false,
                    url: formurl,
                    type: 'POST',
                    data: postdata,
                    processData: false,
                    contentType: false,
                    success: function () {
                        // ???
                    },
                    error: function () {
                        alert('fail');
                    }
                });
            });

            if (currentSectionIndex < sections.length - 1) {
                gotoSection(currentSectionIndex + 1);
            } else {
                loadSections();
            }

            e.preventDefault();
            return false;
        });

        // report navigator can also call back to the same method to call a specific section
        var currentSectionIndex = 0;
        var sections = [];

        const loadCurrentSection = () => {
            // editor (question answering) needs to be called per question and update dynamically
            // so report question content needs to come from JS call
            // each answered question to fire an event, captured here in order to refresh both the navigator and editor sections
            // load each section html from a call so that it can control whether it considers itself complete with its own callbacks
            $('#divReportEditor').load('/reports/@Model.Report.Id/sections/' + sections[currentSectionIndex].id + '/edit');
        };

        const gotoSection = (gotoSectionIndex) => {
            currentSectionIndex = gotoSectionIndex;
            loadSections();
        };

        const drawNavigator = () => {
            // can/should this be pushed out to a .load()?
            var $navigator = $('div#divNavigator');
            $navigator.empty();

            enableNextStep();

            for (var i = 0; i < sections.length; i++) {
                var className = "list-group-item list-group-item-action";

                if (sections[i].sectionComplete) {
                    className += " list-group-item-success";
                } else {
                    disableNextStep();
                }

                if (i === currentSectionIndex) {
                    className += " active";
                    $('#divReportHeader').text(sections[i].sectionTitle); // draw the report header
                }

                $navigator.append(
                    $("<a>", { "class": className, "data-section": i })
                        .append(sections[i].sectionTitle)
                        .click((e) => {
                            gotoSection($(e.currentTarget).data("section"));
                        }));
            }
       };

        // load the report (ex-template) sections to draw the navigator and
        const loadSections = async () => {
            let [sectionsData, err] = await to($.ajax({
                type: 'GET',
                url: '/reports/@Model.Report.Id/sections',
            }));

            if (sectionsData && sectionsData.length > 0) {
                sections = sectionsData; // save the sections data for use elsewhere
                drawNavigator(sections); // draw the side menu
                loadCurrentSection(); // load the current section into the editor
            }
        };

        // report template chooser
        $('#lnkReportTemplate').editable({
            mode: 'inline',
            value: '@Model.Report.Id',
            source: [
                @foreach(var template in Model.ReportTemplates)
                {
                    <text>{value: '@template.Id', text: '@template.TemplateName'},</text>
                }
            ],
            success: async (response, newValue) => {
                // choose the template, which creates a copy of its sections and fields onto the report
                let [result, err] = await to($.ajax({
                    type: 'POST',
                    url: '/reports/@Model.Report.Id/reportTemplate',
                    dataType: 'json',
                    contentType: 'application/json; charset=utf-8',
                    data: JSON.stringify(newValue)
                }));

                // can now load the sections as a template has been chosen
                loadSections();
            }
        });

        // can only load the sections if they have already been created because a template was chosen
        @if (Model.Report.Id != Guid.Empty)
            {
            <text>
         loadSections();
            </text>
            }
        })();
</script>