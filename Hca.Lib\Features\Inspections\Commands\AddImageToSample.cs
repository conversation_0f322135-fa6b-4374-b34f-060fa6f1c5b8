﻿using System;
using System.Threading.Tasks;
using System.Transactions;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class AddImageToSample : InspectionCommand
    {
        public AddImageToSample(
            Guid inspectionId,
            Guid sampleId,
            Uri imageUri) : base(inspectionId)
        {
            SampleId = sampleId;
            ImageUri = imageUri;
        }

        public Guid SampleId { get; }
        public Uri ImageUri { get; }
    }

    public class AddImageToSampleHandler : DapperRequestHandler<AddImageToSample, CommandResult>
    {
        public AddImageToSampleHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddImageToSample request)
        {
            using var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
            var sample = await db.GetAsync<InspectionSampleDto>(request.SampleId);
            sample.ImageCount += 1;
            await db.UpdateAsync(sample);
            await db.InsertAsync(new InspectionSampleImageDto
            {
                Id = Guid.NewGuid(),
                ImageUrl = request.ImageUri.ToString(),
                InspectionSampleId = request.SampleId
            });
            scope.Complete();
            return CommandResult.Success();
        }
    }
}
