﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Commands
{
    public class AddQuoteField : ICommand
    {
        public AddQuoteField(
            Guid quoteId,
            string templateCode,
            string sectionCode,
            string fieldCode,
            string fieldContent)
        {
            QuoteId = quoteId;
            TemplateCode = templateCode;
            SectionCode = sectionCode;
            FieldCode = fieldCode;
            FieldContent = fieldContent;
        }

        public Guid QuoteId { get; }
        public string TemplateCode { get; }
        public string SectionCode { get; }
        public string FieldCode { get; }
        public string FieldContent { get; }
    }

    public class AddQuoteFieldHandler : DapperRequestHandler<AddQuoteField, CommandResult>
    {
        public AddQuoteFieldHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<CommandResult> OnHandleAsync(IDbHelper db, AddQuoteField request) =>
            db.InsertAsync(new QuoteFieldDto
            {
                Id = Guid.NewGuid(),
                QuoteId = request.QuoteId,
                TemplateCode = request.TemplateCode,
                SectionCode = request.SectionCode,
                FieldCode = request.FieldCode,
                FieldContent = request.FieldContent,
            })
            .ContinueWith(_ => CommandResult.Success());
    }
}
