﻿using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using MediatR;

namespace Hca.Lib.Azure.Core
{
    public abstract class BlobNotificationHandler<TReq> : INotificationHandler<TReq> where TReq : INotification
    {
        private readonly BlobServiceClient _client;
        private readonly string _containerName;

        public BlobNotificationHandler(BlobServiceClient blobClient, string containerName)
        {
            _containerName = containerName;
            _client = blobClient;
        }

        public async Task Handle(TReq request, CancellationToken cancellationToken)
        {
            var containerClient = _client.GetBlobContainerClient(_containerName);
            await OnHandleAsync(containerClient, request, cancellationToken);
        }

        public abstract Task OnHandleAsync(BlobContainerClient client, TReq request, CancellationToken cancellationToken);
    }
}