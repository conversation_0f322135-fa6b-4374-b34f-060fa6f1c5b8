﻿using System.IO;
using System.Net.Mail;
using System.Threading;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Drawing.ChartDrawing;
using Hca.Lib.Core;
using MediatR;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace Hca.Lib.Services
{
    public class SendEmail : ICommand
    {
        public SendEmail(
            string to,
            string subject,
            string htmlContent,
            string from = "<EMAIL>")
        {
            To = to;
            Subject = subject;
            HtmlContent = htmlContent;
            From = from;
        }

        public string To { get; }
        public string Subject { get; }
        public string HtmlContent { get; }
        public string From { get; }
    }

    public class EmailServiceHandler : IRequestHandler<SendEmail, CommandResult>
    {
        private readonly SendGridClient _sendGridClient;

        public EmailServiceHandler(SendGridClient sendGridClient)
        {
            _sendGridClient = sendGridClient;
        }

        public async Task<CommandResult> Handle(SendEmail request, CancellationToken cancellationToken)
        {
            var response = await _sendGridClient.SendEmailAsync(MailHelper.CreateSingleEmail(
                    new EmailAddress(request.From),
                    new EmailAddress(request.To),
                    request.Subject,
                    request.HtmlContent,
                    GetPlainTextBody(request.HtmlContent)),
                cancellationToken);

            return response.IsSuccessStatusCode
                ? CommandResult.Success()
                : CommandResult.Fail(await response.Body.ReadAsStringAsync());
        }

        private string GetPlainTextBody(string htmlBody) =>
            new StreamReader(AlternateView.CreateAlternateViewFromString(htmlBody).ContentStream).ReadToEnd();
    }
}
