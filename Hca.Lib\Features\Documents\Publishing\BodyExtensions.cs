﻿using System.Collections.Generic;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class BodyExtensions
    {
        public static IEnumerable<Text> FindTag(this Body body, string tag)
        {
            var result = new Text[3];

            foreach (var text in body.Descendants<Text>())
            {
                if (text.Text == "{{")
                {
                    result[0] = text;
                    continue;
                }

                if (text.Text == tag)
                {
                    result[1] = text;
                    continue;
                }
                else if (result[1] == null)
                {
                    result[0] = null;
                    continue;
                }

                if (text.Text == "}}"
                    && result[0] != null
                    && result[1] != null)
                {
                    result[2] = text;
                    return result;
                }
            }

            return null;
        }
    }
}
