﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.FloorPlanColours.NewModel
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@using Hca.WebHost.Areas.InspectionValues.Pages.Widgets
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
{
            { nameof( ValueDto.DisplayText), "Colour Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
            { nameof( ValueDto.Value), "Colour" },
        },
        Header = "Floor Plan Colour",
        Value = Model.Value,
        InspectionValueType = InspectionValueType.Colour,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />

@section Styles {
    <environment names="Development">
        <link href="~/vendor/bootstrap-colorpicker/dist/css/bootstrap-colorpicker.css" rel="stylesheet" />
    </environment>
    <environment names="Staging,Production">
        <link href="~/vendor/bootstrap-colorpicker/dist/css/bootstrap-colorpicker.css" rel="stylesheet" />
    </environment>
}

@section Scripts {

    <environment names="Development">
        <script src="~/vendor/bootstrap-colorpicker/dist/js/bootstrap-colorpicker.js"></script>
    </environment>
    <environment names="Staging,Production">
        <script src="~/vendor/bootstrap-colorpicker/dist/js/bootstrap-colorpicker.js"></script>
    </environment>

    <script>$('#colourPicker').colorpicker({ format: 'hex' });</script>

}