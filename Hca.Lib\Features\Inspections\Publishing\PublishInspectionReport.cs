﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Documents.Commands;
using Hca.Lib.Features.Documents.Queries;
using Hca.Lib.Features.Reports.Publishing;
using MediatR;

namespace Hca.Lib.Features.Inspections.Publishing.Commands
{
    public class PublishInspectionReport : ICommand
    {
        public PublishInspectionReport(
            Guid documentId,
            Guid inspectionId,
            string emailAddress)
        {
            DocumentId = documentId;
            InspectionId = inspectionId;
            EmailAddress = emailAddress;
        }

        public Guid DocumentId { get; }
        public Guid InspectionId { get; }
        public string EmailAddress { get; }
    }

    public class PublishInspectionReportHandler : DapperRequestHandler<PublishInspectionReport, CommandResult>
    {
        private readonly IMediator _mediator;
        private readonly ReportBuilder _documentBuilder;

        public PublishInspectionReportHandler(
            IDb<PERSON>elper dbHelper,
            IMediator mediator,
            ReportBuilder documentBuilder) : base(dbHelper)
        {
            _mediator = mediator;
            _documentBuilder = documentBuilder;
        }

        public async override Task<CommandResult> OnHandleAsync(IDbHelper db, PublishInspectionReport request)
        {
            var documentModel = await _mediator.Send(new GetDocumentModel(request.DocumentId));

            var inspection = await db.GetAsync<InspectionDto>(request.InspectionId);
            var inspectionSamples = await db.GetListAsync<InspectionSampleDto>(
                "WHERE InspectionId = @InspectionId",
                request);
            var inspectionSampleImages = await db.QueryAsync<InspectionSampleImageDto>(
                "SELECT tblInspectionSampleImages.* FROM tblInspectionSampleImages " +
                "JOIN tblInspectionSamples ON InspectionSampleId = tblInspectionSamples.Id " +
                "WHERE InspectionId = @InspectionId",
                request);

            var location = await db.GetAsync<PropertyDtoExtended>(inspection.PropertyId);
            var client = await db.GetAsync<ClientDto>(location.ClientId);

            if (!location.AddressId.HasValue) throw new ApplicationException("Location does not have an address");
            var address = await db.GetAsync<AddressDto>(location.AddressId.Value);

            var inspectionModel = new ReportModel
            {
                DocumentModel = documentModel,
                ReportId = Guid.NewGuid(),
                Samples = inspectionSamples.Select(s =>
                    (s, inspectionSampleImages.Where(i => i.InspectionSampleId == s.Id))),
            };

            var file = await _documentBuilder.BuildReportAsync(inspectionModel, CancellationToken.None);

            var uploadResult = await _mediator.Send(new PublishDocument(
                request.DocumentId,
                    StorageConstants.ReportsContainerName,
                    $"{request.DocumentId}/document_{DateTime.UtcNow:yyyy_MM_dd_HH_mm_ss}.docx",
                    file,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ));

            return CommandResult.Success();
        }
    }
}
