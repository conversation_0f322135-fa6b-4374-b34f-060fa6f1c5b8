﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.FloorPlanColours.IndexModel
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
{
            { nameof( ValueDto.DisplayText), "Colour Name" }
        },
        Header = "Floor Plan Colours",
        UrlPath = Urls.FloorPlanColours,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />