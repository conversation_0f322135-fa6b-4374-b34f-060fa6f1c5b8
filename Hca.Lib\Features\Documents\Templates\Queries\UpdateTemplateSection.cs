﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Queries
{
    public class UpdateTemplateSection : ICommand
    {
        public UpdateTemplateSection(Guid templateId, Guid id, string sectionCode, string sectionTitle)
        {
            TemplateId = templateId;
            Id = id;
            SectionCode = sectionCode;
            SectionTitle = sectionTitle;
        }

        public Guid TemplateId { get; }
        public Guid Id { get; }
        public string SectionCode { get; }
        public string SectionTitle { get; }
    }

    public class UpdateTemplateSectionHandler : DapperRequestHandler<UpdateTemplateSection, CommandResult>
    {
        public UpdateTemplateSectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateTemplateSection request)
        {
            var section = await db.GetAsync<TemplateSectionDto>(request.Id);
            section.SectionCode = request.SectionCode;
            section.SectionTitle = request.SectionTitle;
            await db.UpdateAsync(section);

            return CommandResult.Success();
        }
    }
}
