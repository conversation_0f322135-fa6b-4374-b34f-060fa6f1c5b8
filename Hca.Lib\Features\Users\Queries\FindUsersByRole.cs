﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Queries
{
    public class FindUsersByRole : IQueryMany<UserDto>
    {
        public FindUsersByRole(params UserRole[] roles)
        {
            if (!roles?.Any() ?? true)
            {
                throw new ArgumentNullException(nameof(roles));
            }

            Roles = roles;
        }

        public UserRole[] Roles{ get; }
    }

    public class FindUsersByRoleHandler : DapperRequestHandler<FindUsersByRole, DtoSet<UserDto>>
    {
        public FindUsersByRoleHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<UserDto>> OnHandleAsync(IDbHelper db, FindUsersByRole request)
        {
            var whereClause = "WHERE " +
                string.Join(" OR ", request.Roles.Select(r => "Role = " + (int)r));
            var users = await db.GetListAsync<UserDto>(whereClause);
            return DtoSet.From(users);
        }
    }
}
