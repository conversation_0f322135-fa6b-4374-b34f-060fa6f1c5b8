﻿@model Hca.Lib.Features.Clients.AddressDto
@{
    var readOnly = (bool)(ViewData["readOnly"] ?? false);
}

<input type="hidden" asp-for="Id" />
<input type="hidden" asp-for="Lat" />
<input type="hidden" asp-for="Lon" />
<input type="text" asp-for="StreetName" row-label="Street Name" read-only="readOnly" label-width="3" content-width="9" />
<input type="text" asp-for="Town" row-label="Town" read-only="readOnly" label-width="3" content-width="9" />
<input type="text" asp-for="City" row-label="City" read-only="readOnly" label-width="3" content-width="9" />
<input type="text" asp-for="County" row-label="County" read-only="readOnly" label-width="3" content-width="9" />
<input type="text" asp-for="Country" row-label="Country" read-only="readOnly" label-width="3" content-width="9" />
<input type="text" asp-for="Postcode" row-label="Postcode" read-only="readOnly" label-width="3" content-width="9" />