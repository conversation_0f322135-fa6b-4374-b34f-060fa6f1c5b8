﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Projects.Commands
{
    public class CreateQuote : ICommand
    {
        public CreateQuote(Guid clientId, Guid? contactId, string quoteNumber, Guid inspectionTypeId, string notes, double baseFee)
            : this(clientId, contactId, Guid.NewGuid(), quoteNumber, inspectionTypeId, notes, baseFee) { }

        public CreateQuote(Guid clientId, Guid? contactId, Guid quoteId, string quoteNumber, Guid inspectionTypeId, string notes, double baseFee)
        {
            QuoteId = quoteId;
            ClientId = clientId;
            ContactId = contactId;
            Notes = notes;
            BaseFee = baseFee;
            QuoteNumber = quoteNumber;
            InspectionTypeId = inspectionTypeId;
        }

        public Guid QuoteId { get; }
        public Guid ClientId { get; }
        public Guid? ContactId { get; }
        public string Notes { get; }
        public double BaseFee { get; }
        public string QuoteNumber { get; }
        public Guid InspectionTypeId { get; }
    }

    public class CreateQuoteHandler : DapperRequestHandler<CreateQuote, CommandResult>
    {
        public CreateQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateQuote request)
        {
            await db.InsertAsync(new QuoteDto {
                Id = request.QuoteId,
                ClientId = request.ClientId,
                ContactId = request.ContactId,
                Notes = request.Notes,
                QuoteNumber = request.QuoteNumber,
                InspectionTypeId = request.InspectionTypeId,
                BaseFee = request.BaseFee,
            });

            return CommandResult.Success();
        }
    }
}
