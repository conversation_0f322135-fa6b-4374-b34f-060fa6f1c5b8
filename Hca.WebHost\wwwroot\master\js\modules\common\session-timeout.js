// Session Timeout Module
// -----------------------------------

(function(w, $) {
    'use strict';

    var SessionTimeout = {
        // Configuration
        config: {
            inactivityTimeout: 10 * 60 * 1000, // 10 minutes in milliseconds = 10 * 60 * 1000
            warningTimeout: 1 * 60 * 1000,     // 1 minute warning in milliseconds
            checkInterval: 30 * 1000,          // Check every 30 seconds
            extendUrl: '/api/session/extend'   // Endpoint to extend session
        },

        // State
        state: {
            lastActivity: Date.now(),
            warningShown: false,
            countdownInterval: null,
            checkInterval: null,
            isActive: false
        },

        // Initialize the session timeout functionality
        init: function() {
            if (this.state.isActive) return;
            
            this.state.isActive = true;
            this.state.lastActivity = Date.now();
            
            // Bind activity events
            this.bindActivityEvents();
            
            // Start monitoring
            this.startMonitoring();
            
            console.log('Session timeout initialized');
        },

        // Bind events that indicate user activity
        bindActivityEvents: function() {
            var self = this;
            var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
            
            events.forEach(function(event) {
                document.addEventListener(event, function() {
                    self.updateActivity();
                }, true);
            });
        },

        // Update last activity timestamp
        updateActivity: function() {
            // Don't update activity if warning dialog is currently shown
            // User must explicitly choose to stay logged in or logout
            if (this.state.warningShown) {
                return;
            }
            
            this.state.lastActivity = Date.now();
        },

        // Start monitoring for inactivity
        startMonitoring: function() {
            var self = this;
            
            this.state.checkInterval = setInterval(function() {
                self.checkInactivity();
            }, this.config.checkInterval);
        },

        // Check if user has been inactive
        checkInactivity: function() {
            var now = Date.now();
            var timeSinceActivity = now - this.state.lastActivity;
            
            // If we've exceeded the inactivity timeout, log out
            if (timeSinceActivity >= this.config.inactivityTimeout + this.config.warningTimeout) {
                this.logout();
                return;
            }
            
            // If we've exceeded the inactivity timeout but not warning timeout, show warning
            if (timeSinceActivity >= this.config.inactivityTimeout && !this.state.warningShown) {
                this.showWarning();
            }
        },

        // Show the timeout warning dialog
        showWarning: function() {
            var self = this;
            this.state.warningShown = true;
            
            // Calculate remaining time
            var remainingTime = this.config.warningTimeout;
            var remainingSeconds = Math.floor(remainingTime / 1000);
            
            // Create the warning dialog
            swal({
                title: 'Session Timeout Warning',
                text: 'Your session will expire in ' + remainingSeconds + ' seconds due to inactivity.',
                icon: 'warning',
                buttons: {
                    logout: {
                        text: 'Logout Now',
                        value: 'logout',
                        className: 'btn-secondary'
                    },
                    extend: {
                        text: 'Stay Logged In',
                        value: 'extend',
                        className: 'btn-primary'
                    }
                },
                closeOnClickOutside: false,
                closeOnEsc: false,
                content: {
                    element: 'div',
                    attributes: {
                        innerHTML: '<div id="session-countdown" class="text-center mt-3"><strong>Time remaining: <span id="countdown-timer">' + remainingSeconds + '</span> seconds</strong></div>'
                    }
                }
            }).then(function(value) {
                if (value === 'extend') {
                    self.extendSession();
                } else if (value === 'logout') {
                    self.logout();
                }
            });
            
            // Start countdown
            this.startCountdown(remainingSeconds);
        },

        // Start the countdown timer
        startCountdown: function(seconds) {
            var self = this;
            var remaining = seconds;
            
            this.state.countdownInterval = setInterval(function() {
                remaining--;
                
                var timerElement = document.getElementById('countdown-timer');
                if (timerElement) {
                    timerElement.textContent = remaining;
                }
                
                // If countdown reaches zero, logout
                if (remaining <= 0) {
                    clearInterval(self.state.countdownInterval);
                    swal.close();
                    self.logout();
                }
            }, 1000);
        },

        // Hide the warning dialog
        hideWarning: function() {
            if (this.state.warningShown) {
                this.state.warningShown = false;
                
                if (this.state.countdownInterval) {
                    clearInterval(this.state.countdownInterval);
                    this.state.countdownInterval = null;
                }
                
                swal.close();
            }
        },

        // Extend the session
        extendSession: function() {
            var self = this;
            
            // Make AJAX call to extend session
            $.ajax({
                url: this.config.extendUrl,
                type: 'POST',
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function() {
                    // Reset activity timer
                    self.state.lastActivity = Date.now();
                    self.hideWarning();
                },
                error: function() {
                    // If extend fails, logout immediately
                    self.logout();
                }
            });
        },

        // Logout the user
        logout: function() {
            this.cleanup();
            
            // Create a form and submit it to logout immediately
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = '/Identity/Account/Logout';
            
            // Add anti-forgery token
            var token = document.querySelector('input[name="__RequestVerificationToken"]');
            if (token) {
                var hiddenToken = document.createElement('input');
                hiddenToken.type = 'hidden';
                hiddenToken.name = '__RequestVerificationToken';
                hiddenToken.value = token.value;
                form.appendChild(hiddenToken);
            }
            
            // Add return URL to redirect to login after logout
            var returnUrl = document.createElement('input');
            returnUrl.type = 'hidden';
            returnUrl.name = 'returnUrl';
            returnUrl.value = '/Identity/Account/Login';
            form.appendChild(returnUrl);
            
            document.body.appendChild(form);
            form.submit();
        },

        // Cleanup intervals and state
        cleanup: function() {
            if (this.state.checkInterval) {
                clearInterval(this.state.checkInterval);
                this.state.checkInterval = null;
            }
            
            if (this.state.countdownInterval) {
                clearInterval(this.state.countdownInterval);
                this.state.countdownInterval = null;
            }
            
            this.state.isActive = false;
            this.state.warningShown = false;
        },

        // Destroy the session timeout (for cleanup)
        destroy: function() {
            this.cleanup();
        }
    };

    // Auto-initialize when DOM is ready (only if user is authenticated)
    $(document).ready(function() {
        // Check if user is authenticated by looking for authentication indicators
        if ($('body').hasClass('authenticated') || $('.topnavbar-wrapper').length > 0) {
            SessionTimeout.init();
        }
    });

    // Expose to global scope
    w.SessionTimeout = SessionTimeout;

})(window, jQuery);