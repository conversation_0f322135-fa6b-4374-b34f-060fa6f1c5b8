{"configurations": [{"name": "C#: Hca.WebHost", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}\\Hca.WebHost\\Hca.WebHost.csproj", "launchConfigurationId": "TargetFramework=;Hca.WebHost"}, {"name": "C#: Hca.WebHost (Hot Reload)", "type": "dotnet", "request": "launch", "projectPath": "${workspaceFolder}\\Hca.WebHost\\Hca.WebHost.csproj", "launchConfigurationId": "TargetFramework=;Hca.WebHost", "preLaunchTask": "watch"}]}