﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Commands;
using Hca.Lib.Features.Documents.Templates.Queries;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using MediatR;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class CreateInspection : ICommand
    {
        public CreateInspection(
            Guid inspectionId,
            Guid inspectionTypeId,
            Guid projectId,
            Guid propertyId)
        {
            InspectionId = inspectionId;
            InspectionTypeId = inspectionTypeId;
            ProjectId = projectId;
            PropertyId = propertyId;
        }

        public Guid InspectionId { get; }
        public Guid InspectionTypeId { get; }
        public Guid ProjectId { get; }
        public Guid PropertyId { get; }
    }

    public class CreateInspectionHandler : DapperRequestHandler<CreateInspection, CommandResult>
    {
        private readonly IMediator _mediator;

        public CreateInspectionHandler(IMediator mediator, IDbHelper dbHelper) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateInspection request)
        {
            // this may not end up being a relational field eventually but seems tidy enough for now
            var defaultTemplateId = await _mediator.Send(new GetInspectionDefaultTemplate(request.InspectionTypeId));

            if (!defaultTemplateId.HasValue)
            {
                return CommandResult.Fail("No default report template defined for that inspection type");
            }

            var template = await _mediator.Send(new GetTemplateObject(defaultTemplateId.Value));
            var documentId = Guid.NewGuid();
            await _mediator.Send(new CreateDocument(documentId, template));

            await db.InsertAsync(new InspectionDto
            {
                Id = request.InspectionId,
                InspectionTypeId = request.InspectionTypeId,
                PropertyId = request.PropertyId,
                ProjectId = request.ProjectId,
            });

            await db.InsertAsync(new InspectionReportDto
            {
                Id = Guid.NewGuid(),
                InspectionId = request.InspectionId,
                DocumentId = documentId,
            });

            return CommandResult.Success();
        }
    }
}
