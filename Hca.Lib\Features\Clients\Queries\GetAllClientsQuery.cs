﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetAllClientsQuery : IQueryMany<ClientDto>
    { 
        public bool IsArchived { get; }
        public bool IsDeleted { get; }

        public GetAllClientsQuery(bool isArchived = false, bool isDeleted = false)
        {
            IsArchived = isArchived;
            IsDeleted = isDeleted;
        }
    }

    public class GetAllClientsQueryHandler : DapperRequestHandler<GetAllClientsQuery, DtoSet<ClientDto>>
    {
        public GetAllClientsQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<ClientDto>> OnHandleAsync(IDbHelper db, GetAllClientsQuery request)
        {
            return DtoSet.From(await db.GetListAsync<ClientDto>(
                $"WHERE Archived IS {(request.IsArchived ? "NOT NULL" : "NULL")} " +
                $"AND Deleted IS {(request.IsDeleted ? "NOT NULL" : "NULL")}", 
                request));
        }
    }
}
