﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections;
using Hca.WebHost.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Hca.WebHost.Areas.InspectionValues.Models
{
    public abstract class InspectionValueCreatePage : PageModel
    {
        private readonly ValueListsService _inspectionValuesService;

        public InspectionValueCreatePage(ValueListsService inspectionValuesService)
        {
            _inspectionValuesService = inspectionValuesService;
        }

        public abstract ValueListType ValueListType { get; }
        public abstract string IndexUrl { get; }

        [BindProperty]
        public ValueDto Value { get; set; }

        public void OnGet()
        {
            Value = new ValueDto();
        }

        public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
        {
            Value.Id = Guid.NewGuid();
            Value.ValueListType = ValueListType;
            await _inspectionValuesService.CreateAsync(Value, cancellationToken);
            return Redirect(IndexUrl);
        }
    }
}
