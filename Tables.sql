﻿    

drop table tblquotedocuments
drop table tblinspectiondocuments
drop table tbldocumentFields
drop table tbldocumentsections
drop table tbldocuments
drop table tblinspectionsampleimages
drop table tblinspectionsamples
drop table tblinspections
drop table tblprojects
drop table tblquotefields
drop table tblquotes
drop table tblquoteproperties
drop table tblUsers
drop table tblcontacts
drop table tblFloorPlans
drop table tblPropertyDocuments
drop table tblProperties
drop table tblSitePlans
drop table tblSites
drop table tblclients
drop table tbladdresses
drop table tblTemplateFields
drop table tbltemplatesections
drop table tbltemplatefiles
drop table tblvaluelists
drop table tbltemplates
drop table tblconfig
GO

drop view newGuid
drop function RandomString
GO

CREATE VIEW newGuid
AS
SELECT newId() id
GO

create function dbo.RandomString(@length int)
returns nvarchar(max) -- Get a list of tables and views in the current database
begin
    declare @id UNIQUEIDENTIFIER 
    select @id = id from newGuid
    declare @result nvarchar(max) = CONVERT(nvarchar,LEFT(REPLACE(@id,'-',''),@length))
    return @result
end
GO

create table tblClients (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Clients_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Clients_Created] DEFAULT (getdate()) NOT NULL,
    AddressId UNIQUEIDENTIFIER NULL,
    ClientName NVARCHAR(MAX) NOT NULL,
    LogoUrl NVARCHAR(MAX),
    ClientType INT DEFAULT(2) NOT NULL,
    EmergencyContactDetails NVARCHAR(MAX) NULL,
    EscalationProcedure NVARCHAR(MAX) NULL,
    KpisAndSlas NVARCHAR(MAX) NULL,
    InvoiceEmailAddress NVARCHAR(MAX) NULL,
    AccountQueriesEmailAddress NVARCHAR(MAX) NULL,
    GeneralRequirements NVARCHAR(MAX) NULL,
    BuildingName NVARCHAR(MAX) NULL,
    BuildingNumber NVARCHAR(MAX) NULL,
    Unit NVARCHAR(MAX) NULL,
    Floor NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Clients] PRIMARY KEY CLUSTERED ([Id] ASC),
)

create table tblUsers (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Users_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Users_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NULL,
    Email NVARCHAR(MAX) NOT NULL,
    FirstName NVARCHAR(MAX) NULL,
    LastName NVARCHAR(MAX) NULL,
    Password NVARCHAR(MAX) NOT NULL,
    Role NVARCHAR(MAX) NOT NULL,
    Position NVARCHAR(MAX) NULL,
    SignatureFileName NVARCHAR(MAX) NULL,
    ProfileImageFileName NVARCHAR(MAX) NULL,
    Permissions NVARCHAR(MAX) NULL,
    CONSTRAINT [FK_Users_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id])
)

create table tblAddresses (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Addresses_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Addresses_Created] DEFAULT (getdate()) NOT NULL,
    StreetName NVARCHAR(MAX) NULL,
    Town NVARCHAR(MAX) NULL,
    City NVARCHAR(MAX) NULL,
    County NVARCHAR(MAX) NULL,
    Country NVARCHAR(MAX) NULL,
    Postcode NVARCHAR(MAX) NULL,
    Lat DECIMAL(8,6) NULL,
    Lon DECIMAL(9,6) NULL,
    CONSTRAINT [PK_Addresses] PRIMARY KEY CLUSTERED ([Id] ASC),
)

create table tblContacts (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Contacts_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Contacts_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    AddressId UNIQUEIDENTIFIER NULL,
    FirstName NVARCHAR(MAX) NOT NULL,
    LastName NVARCHAR(MAX) NOT NULL,
    MobilePhone NVARCHAR(MAX) NULL,
    OfficePhone NVARCHAR(MAX) NULL,
    Email NVARCHAR(MAX) NULL,
    Notes NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Contacts] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Contacts_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Contacts_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id])
)

create table tblSites (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Sites_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Sites_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    AddressId UNIQUEIDENTIFIER NULL,
    SiteName NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Sites] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Sites_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Sites_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id])
)

create table tblSitePlans (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_SitePlans_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_SitePlans_Created] DEFAULT (getdate()) NOT NULL,
    SiteId UNIQUEIDENTIFIER NOT NULL,
    ContainerName NVARCHAR(MAX) NOT NULL,
    BlobName NVARCHAR(MAX) NOT NULL,
    ThumbnailName NVARCHAR(MAX) NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_SitePlans] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_SitePlans_Sites] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[tblSites] ([Id])
)

create table tblProperties (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Properties_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Properties_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    AddressId UNIQUEIDENTIFIER NULL,
    SiteId UNIQUEIDENTIFIER NULL,
    PropertyCode NVARCHAR(MAX) NULL,
    Notes NVARCHAR(MAX) NULL,
    ImageContainerName NVARCHAR(MAX) NULL,
    ImageBlobName NVARCHAR(MAX) NULL,
    GroupName NVARCHAR(MAX) NULL,
    BuildingName NVARCHAR(MAX) NULL,
    BuildingNumber NVARCHAR(MAX) NULL,
    Unit NVARCHAR(MAX) NULL,
    Floor NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Properties] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Properties_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Properties_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id]),
    CONSTRAINT [FK_Properties_Sites] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[tblSites] ([Id])
)

create table tblPropertyDocuments (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_PropertyDocuments_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_PropertyDocuments_Created] DEFAULT (getdate()) NOT NULL,
    PropertyId UNIQUEIDENTIFIER NOT NULL,
    ContainerName NVARCHAR(MAX) NOT NULL,
    BlobName NVARCHAR(MAX) NOT NULL,
    DocumentDate DATETIME NULL,
    DocumentType NVARCHAR(MAX) NULL,
    CompanyName NVARCHAR(MAX) NULL,
    Notes NVARCHAR(MAX) NULL,
    AnnualInspectionRequired BIT NOT NULL DEFAULT (0),
    NextInspection DATETIME NULL,
    CONSTRAINT [PK_PropertyDocuments] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_PropertyDocuments_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id])
)

create table tblTemplates (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Templates_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Templates_Created] DEFAULT (getdate()) NOT NULL,
    TemplateType INT NOT NULL,
    TemplateCode NVARCHAR(MAX) DEFAULT (dbo.RandomString(8)) NOT NULL,
    TemplateName NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_Templates] PRIMARY KEY CLUSTERED ([Id] ASC)
)

create table tblValueLists (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_ValueLists_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_ValueLists_Created] DEFAULT (getdate()) NOT NULL,
    ValueListType INT NOT NULL,
    DisplayText NVARCHAR(MAX) NOT NULL,
    HintText NVARCHAR(MAX) NULL,
    Value NVARCHAR(MAX) NULL,
    Priority INT NULL,
    InspectionTypeDefaultTemplateId UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_ValueLists] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_InspectionType_DefaultTemplate] FOREIGN KEY ([InspectionTypeDefaultTemplateId]) REFERENCES [dbo].[tblTemplates] ([Id])
)

create table tblFloorPlans (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_FloorPlans_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_FloorPlans_Created] DEFAULT (getdate()) NOT NULL,
    PropertyId UNIQUEIDENTIFIER NOT NULL,
    ContainerName NVARCHAR(MAX) NOT NULL,
    BlobName NVARCHAR(MAX) NOT NULL,
    ThumbnailName NVARCHAR(MAX) NOT NULL,
    FloorId UNIQUEIDENTIFIER NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_FloorPlans] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_FloorPlans_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id]),
    CONSTRAINT [FK_FloorPlans_FloorId] FOREIGN KEY ([FloorId]) REFERENCES [dbo].[tblValueLists] ([Id])
)

create table tblTemplateFiles (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_TemplateFiles_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_TemplateFiles_Created] DEFAULT (getdate()) NOT NULL,
    TemplateId UNIQUEIDENTIFIER NOT NULL,
    FileName NVARCHAR(MAX) NULL, 
    DisplayOrder INT DEFAULT (0) NOT NULL,
    CONSTRAINT [PK_TemplateFiles] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TemplateFiles_Templates] FOREIGN KEY ([TemplateId]) REFERENCES [dbo].[tblTemplates] ([Id])
)

create table tblTemplateSections (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_TemplateSections_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_TemplateSections_Created] DEFAULT (getdate()) NOT NULL,
    TemplateId UNIQUEIDENTIFIER NOT NULL,
    SectionCode NVARCHAR(MAX) DEFAULT (dbo.RandomString(8)) NOT NULL,
    SectionTitle NVARCHAR(MAX) NULL,
    SectionOrder INT DEFAULT (0) NOT NULL, 
    CONSTRAINT [PK_TemplateSections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TemplateSections_Templates] FOREIGN KEY ([TemplateId]) REFERENCES [dbo].[tblTemplates] ([Id])
)

create table tblTemplateFields (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_TemplateFields_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_TemplateFields_Created] DEFAULT (getdate()) NOT NULL,
    TemplateSectionId UNIQUEIDENTIFIER NOT NULL,
    FieldType INT NOT NULL,
    FieldContent NVARCHAR(MAX) NULL,
    FieldOrder INT DEFAULT (0) NOT NULL,
    Optional BIT DEFAULT(0) NOT NULL,
    CONSTRAINT [PK_TemplateFields] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_TemplateFields_TemplateSections] FOREIGN KEY ([TemplateSectionId]) REFERENCES [dbo].[tblTemplateSections] ([Id])
)

create table tblDocuments (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Documents_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Documents_Created] DEFAULT (getdate()) NOT NULL,
    TemplateCode NVARCHAR(MAX) NULL,
    DocumentStatus INT NOT NULL,
    Folder NVARCHAR(MAX) NULL,
    FileName NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Documents] PRIMARY KEY CLUSTERED ([Id] ASC)
)

create table tblDocumentSections (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_DocumentSections_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_DocumentSections_Created] DEFAULT (getdate()) NOT NULL,
    DocumentId UNIQUEIDENTIFIER NOT NULL,
    SectionType INT NOT NULL,
    SectionTitle NVARCHAR(MAX) NULL,
    SectionContent NVARCHAR(MAX) NULL,
    SectionOrder INT DEFAULT (0) NOT NULL,
    SectionComplete BIT DEFAULT (0) NOT NULL,
    SectionCode NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_DocumentSections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_DocumentSections_Documents] FOREIGN KEY ([DocumentId]) REFERENCES [dbo].[tblDocuments] ([Id])
)

create table tblDocumentFields (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_DocumentFields_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_DocumentFields_Created] DEFAULT (getdate()) NOT NULL,
    DocumentSectionId UNIQUEIDENTIFIER NOT NULL,
    FieldType INT NOT NULL,
    FieldContent NVARCHAR(MAX) NULL,
    FieldOrder INT DEFAULT (0) NOT NULL, 
    IsOptional BIT DEFAULT(0) NOT NULL,
    IsComplete BIT DEFAULT (0) NOT NULL,
    CONSTRAINT [PK_DocumentFields] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_DocumentFields_DocumentSections] FOREIGN KEY ([DocumentSectionId]) REFERENCES [dbo].[tblDocumentSections] ([Id])
)

create table tblQuotes (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Quotes_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Quotes_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    ContactId UNIQUEIDENTIFIER NULL,
    QuoteNumber NVARCHAR(16) NOT NULL,
    InspectionTypeId UNIQUEIDENTIFIER NOT NULL,
    BaseFee DECIMAL DEFAULT(0) NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_Quotes] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Quotes_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Quotes_InspectionTypes] FOREIGN KEY ([InspectionTypeId]) REFERENCES [dbo].[tblValueLists] ([Id])
)

create table tblQuoteFields (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_QuoteFields_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_QuoteFields_Created] DEFAULT (getdate()) NOT NULL,
    QuoteId UNIQUEIDENTIFIER NOT NULL,
    TemplateCode NVARCHAR(MAX) NOT NULL,
    SectionCode NVARCHAR(MAX) NOT NULL,
    FieldCode NVARCHAR(MAX) NOT NULL,
    FieldContent NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_QuoteFields] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_QuoteFields_Quotes] FOREIGN KEY ([QuoteId]) REFERENCES [dbo].[tblQuotes] ([Id])
)

create table tblQuoteProperties (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_QuoteProperties_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_QuoteProperties_Created] DEFAULT (getdate()) NOT NULL,
    QuoteId UNIQUEIDENTIFIER NOT NULL,
    PropertyId UNIQUEIDENTIFIER NOT NULL,
    ProposedFee DECIMAL NOT NULL,
    Notes NVARCHAR(MAX) NULL,
    CONSTRAINT [PK_QuoteProperties] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_QuoteProperties_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id])
)

create table tblQuoteDocuments (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_QuoteDocuments_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_QuoteDocuments_Created] DEFAULT (getdate()) NOT NULL,
    QuoteId UNIQUEIDENTIFIER NOT NULL,
    DocumentId UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_QuoteDocuments] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [PK_QuoteDocuments_Inspections] FOREIGN KEY ([QuoteId]) REFERENCES [dbo].[tblQuotes] ([Id]),
    CONSTRAINT [PK_QuoteDocuments_Documents] FOREIGN KEY ([DocumentId]) REFERENCES [dbo].[tblDocuments] ([Id])
)

create table tblProjects (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Projects_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Projects_Created] DEFAULT (getdate()) NOT NULL,
    ClientId UNIQUEIDENTIFIER NOT NULL,
    QuoteId UNIQUEIDENTIFIER NULL,
    ProjectNumber NVARCHAR(16) NOT NULL,
    CONSTRAINT [PK_Projects] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Projects_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Projects_Quotes] FOREIGN KEY ([QuoteId]) REFERENCES [dbo].[tblQuotes] ([Id])
)

create table tblInspections (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_Inspections_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_Inspections_Created] DEFAULT (getdate()) NOT NULL,
    PropertyId UNIQUEIDENTIFIER NOT NULL,
    InspectionTypeId UNIQUEIDENTIFIER NOT NULL,
    ProjectId UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_Inspections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Inspections_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id]),
    CONSTRAINT [FK_Inspections_InspectionTypes] FOREIGN KEY ([InspectionTypeId]) REFERENCES [dbo].[tblValueLists] ([Id]),
    CONSTRAINT [FK_Inspections_Projects] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[tblProjects] ([Id])
)

create table tblInspectionSamples (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_InspectionSamples_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_InspectionSamples_Created] DEFAULT (getdate()) NOT NULL,
    InspectionId UNIQUEIDENTIFIER NOT NULL,
    SampleReference NVARCHAR(MAX) NOT NULL,
    SampleNotes NVARCHAR(MAX) NULL,
    ImageCount INT NOT NULL DEFAULT(0),
    DefaultImageUrl NVARCHAR(MAX) NULL,
    Status NVARCHAR(MAX) NULL,
    FloorPlanColour UNIQUEIDENTIFIER NULL,
    Floor UNIQUEIDENTIFIER NULL,
    FloorActual NVARCHAR(MAX) NULL,
    FloorPlanReference NVARCHAR(MAX) NULL,
    RoomUsage NVARCHAR(MAX) NULL,
    Location NVARCHAR(MAX) NULL,
    Material UNIQUEIDENTIFIER NULL,
    MaterialActual NVARCHAR(MAX) NULL,
    MaterialColour NVARCHAR(MAX) NULL,
    Quantity NVARCHAR(MAX) NULL,
    QuantityUnit UNIQUEIDENTIFIER NULL,
    ProductType TINYINT NULL,
    ExtentOfDamage TINYINT NULL,
    SurfaceTreatment TINYINT NULL,
    AsbestosType TINYINT NULL,
    OccupantActivity TINYINT NULL,
    LikelihoodOfDisturbance TINYINT NULL,
    NumberOfOccupants TINYINT NULL,
    TypeOfMaintenance TINYINT NULL,
    Accessibility TINYINT NULL,
    ManagementRecommendation UNIQUEIDENTIFIER NULL,
    Priority UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_InspectionSamples] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [PK_InspectionSamples_Inspections] FOREIGN KEY ([InspectionId]) REFERENCES [dbo].[tblInspections] ([Id])
)

create table tblInspectionSampleImages (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_InspectionSampleImages_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_InspectionSampleImages_Created] DEFAULT (getdate()) NOT NULL,
    InspectionSampleId UNIQUEIDENTIFIER NOT NULL,
    ImageUrl NVARCHAR(MAX) NOT NULL,
    CONSTRAINT [PK_InspectionSampleImages] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [PK_InspectionSample.Images_InspectionSamples] FOREIGN KEY ([InspectionSampleId]) REFERENCES [dbo].[tblInspectionSamples] ([Id])
)

create table tblInspectionDocuments (
    Id UNIQUEIDENTIFIER CONSTRAINT [DF_InspectionDocuments_Id] DEFAULT (newid()) NOT NULL,
    Created DATETIME CONSTRAINT [DF_InspectionDocuments_Created] DEFAULT (getdate()) NOT NULL,
    InspectionId UNIQUEIDENTIFIER NOT NULL,
    DocumentId UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT [PK_InspectionDocuments] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [PK_InspectionDocuments_Inspections] FOREIGN KEY ([InspectionId]) REFERENCES [dbo].[tblInspections] ([Id]),
    CONSTRAINT [PK_InspectionDocuments_Documents] FOREIGN KEY ([DocumentId]) REFERENCES [dbo].[tblDocuments] ([Id])
)

create table tblConfig (
    ConfigName VARCHAR(MAX) NOT NULL,
    IntValue INT NULL,
    StringValue NVARCHAR(MAX) NULL
)

IF NOT EXISTS (
    SELECT NULL FROM tblConfig WHERE ConfigName = 'QuoteNumber'
)
INSERT INTO tblConfig (ConfigName, IntValue, StringValue) VALUES ('QuoteNumber', 1, '{0:D8}')

IF NOT EXISTS (
    SELECT NULL FROM tblConfig WHERE ConfigName = 'ProjectNumber'
)
INSERT INTO tblConfig (ConfigName, IntValue, StringValue) VALUES ('ProjectNumber', 1, '{0:D8}')
