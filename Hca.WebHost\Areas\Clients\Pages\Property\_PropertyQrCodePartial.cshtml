﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyQrCodeModel
@using Hca.Lib.Services.QRFileSpot

<div id="divPropertyQrCodePanel" class="collapse show">
    <div class="card card-default">
        <div class="card-body">
            <small class="text-muted" style="display: block;">PROPERTY QR CODE</small>
            <div id="divPropertyQrCode" style="text-align: center;">
                @if (Model.Property?.QrCodeId?.IsPopulated() ?? false)
                {
                    <img class="img-fluid"
                         style="max-height: 394px;"
                         src="@QrFileSpotConfig.GetDocumentQrCodeImageUrl(Model.Property.QrCodeId)"
                         alt="QR Code"
                         onclick="shareImage(this.src)">

                    <a class="nav-link d-flex btn btn-info"
                       style="width: 100%; margin: 5px 0;"
                       href="#"
                       id="btnAssignPropertyQrCode">
                        Edit Property QR Code Content
                    </a>
                }
                else
                {
                    <a class="nav-link d-flex btn btn-success"
                       style="width: 100%; margin: 5px 0;"
                       href="#"
                       id="btnAssignPropertyQrCode">
                        Assign Property QR Code Content
                    </a>
                }
            </div>

            <div id="divPropertyQrCodeContent" style="display: none;">
                <form id="documentForm" action="@Urls.ClientPropertyQrCode(Model.Property.ClientId, Model.Property.Id)" method="post">
                    Select a document that has a QR code:<br />
                    <select id="selPropertyDocuments" name="documentId" style="max-width: 100%; display: block;">
                    </select><br />
                    <button type="submit" class="btn btn-success">Confirm</button>
                    <button type="button" id="btnCancel" class="btn">Cancel</button>
                    @Html.AntiForgeryToken()
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    $(() => {
        let divQrCodePanel = $('#divPropertyQrCodePanel');
        let divPropertyQrCode = $('#divPropertyQrCode');
        let divPropertyQrCodeContent = $('#divPropertyQrCodeContent');
        let selPropertyDocuments = $('#selPropertyDocuments');

        $('#btnAssignPropertyQrCode').click((e) => {
            e.preventDefault();

            divPropertyQrCode.hide();
            divPropertyQrCodeContent.show();

            selPropertyDocuments.empty();

            $.ajax({
                method: "GET",
                url: '@Urls.ClientPropertyQrCodeGetDocuments(Model.Property.ClientId, Model.Property.Id)'
            })
                .then((response) => {
                    response.forEach(doc => {
                        let docDate = new Date(doc.documentDate),
                            month = docDate.getMonth() + 1,
                            day = docDate.getDate(),
                            year = docDate.getFullYear();
                        let newOption = new Option(`${day}/${month}/${year} - ` + doc.documentType.split(/(?=[A-Z])/).join(' '), doc.id);
                        selPropertyDocuments.append(newOption);
                    });
                })
                .fail((error) => {
                    console.error("Error loading documents:", error);
                });
        });

        $('#documentForm').submit(function (event) {
            event.preventDefault();

            var selectedDocumentId = selPropertyDocuments.val();
            var token = $('input[name="__RequestVerificationToken"]', $(this)).val();

            $.ajax({
                type: "POST",
                url: $(this).attr('action'),
                data: {
                    documentId: selectedDocumentId,
                    __RequestVerificationToken: token,
                },
                success: function (response) {
                    divQrCodePanel.replaceWith(response);
                },
                error: function (error) {
                    console.error("Error submitting document:", error);
                }
            });
        });

        $('#btnCancel').click((e) => {
            e.preventDefault();

            divPropertyQrCodeContent.hide();
            divPropertyQrCode.show();
        });
    })
</script>