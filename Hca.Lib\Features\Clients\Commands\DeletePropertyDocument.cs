﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using Hca.Lib.Services.QRFileSpot;
using Microsoft.Extensions.Logging;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class DeletePropertyDocument : ICommand
    {
        public DeletePropertyDocument(Guid documentId)
        {
            DocumentId = documentId;
        }

        public Guid DocumentId { get; }
    }

    public class DeletePropertyDocumentHandler : DapperRequestHandler<DeletePropertyDocument, CommandResult>
    {
        private readonly IQrFileSpotService _qrFileSpotService;
        private readonly ILogger<DeletePropertyDocumentHandler> _logger;
        private readonly PropertyCountsService _propertyCountsService;

        public DeletePropertyDocumentHandler(
            IDbHelper dbHelper,
            IQrFileSpotService qrFileSpotService,
            ILogger<DeletePropertyDocumentHandler> logger,
            PropertyCountsService propertyCountsService) : base(dbHelper)
        {
            _qrFileSpotService = qrFileSpotService;
            _logger = logger;
            _propertyCountsService = propertyCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeletePropertyDocument request)
        {
            var document = await db.GetAsync<PropertyDocumentDto>(request.DocumentId);
            if (document != null && !string.IsNullOrWhiteSpace(document.QrCodeId))
            {
                try
                {
                    await _qrFileSpotService.DeleteDocumentAsync(document.QrCodeId, CancellationToken.None);
                }
                catch(Exception ex)
                {
                    _logger.LogError(ex, "Failed to delete QR Code {QrCodeId}", document.QrCodeId);
                }
            }

            await db.DeleteAsync<PropertyDocumentDto>(request.DocumentId);

            _propertyCountsService.ClearPropertyCountsAsync(document.PropertyId);
            return CommandResult.Success();
        }
    }
}
