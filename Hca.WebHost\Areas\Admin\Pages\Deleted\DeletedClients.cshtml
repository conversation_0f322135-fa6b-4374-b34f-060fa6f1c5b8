﻿@page "/deletedclients"
@model Hca.WebHost.Areas.Admin.Pages.DeletedClientsModel
@{
    ViewData["Title"] = "Deleted Clients";
}
<div class="card card-default">
    <div class="card-header">
        <h5>DELETED CLIENTS</h5>
    </div>
    <div id="divUsers" class="card-body">
        <table class="table table-striped table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>Client Name</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var client in Model.Clients.Items)
                {
                    <tr>
                        <td>@client.ClientName</td>
                        <td class="d-flex">
                            <div class="ml-auto">
                                <anchor action="@Urls.Client(client.Id)" text="view" style=""></anchor>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>

        @if (Model.Clients.TotalPages > 1)
        {
            <nav>
                <ul class="pagination">
                    @if (Model.Clients.CurrentPage > 1)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Clients.CurrentPage - 1)">Previous</a>
                        </li>
                    }

                    @for (int i = 1; i <= Model.Clients.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.Clients.CurrentPage ? "active" : "")">
                            <a class="page-link" href="?pageNum=@i">@i</a>
                        </li>
                    }

                    @if (Model.Clients.CurrentPage < Model.Clients.TotalPages)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Clients.CurrentPage + 1)">Next</a>
                        </li>
                    }
                </ul>
            </nav>
        }
    </div>
</div>

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([{ url: '@Urls.DeletedClients', text: 'Deleted Clients' }]);
        });</script>
}