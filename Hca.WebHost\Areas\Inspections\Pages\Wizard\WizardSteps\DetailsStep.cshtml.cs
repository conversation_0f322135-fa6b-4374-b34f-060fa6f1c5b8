﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections.Queries;
using Hca.Lib.Features.Inspections.Queries.Models;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

public class DetailsModel : HcaPageModel
{
    public DetailsModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task OnGetAsync(Guid inspectionId)
    {
        Inspection = await _mediator.Send(new GetInspection(inspectionId)) ??
            new InspectionQueryModel { Id = inspectionId,  };
    }

    public InspectionQueryModel Inspection { get; private set; }
}
