﻿using System;
using System.Collections.Generic;

namespace Hca.Lib.Features.Documents.Fields
{
    public class DocumentChoiceField : DocumentField<ChoiceField>
    {
        internal DocumentChoiceField(Guid documentFieldId, int fieldOrder, bool isOptional, string serialisedContent) : base(documentFieldId) {
            Value = new ChoiceField(fieldOrder, isOptional, serialisedContent);
        }

        protected DocumentChoiceField(
            IEnumerable<Choice> choices,
            int? maxChoices = null,
            int? minChoices = null,
            string hint = null) : base(Guid.NewGuid())
        {
            Value = new ChoiceField(choices, maxChoices, minChoices, hint);
        }

        protected DocumentChoiceField(
            Guid documentFieldId,
            IEnumerable<Choice> choices,
            int? maxChoices = null,
            int? minChoices = null,
            string hint = null) : base(documentFieldId)
        {
            Value = new ChoiceField(choices, maxChoices, minChoices, hint);
        }
    }
}
