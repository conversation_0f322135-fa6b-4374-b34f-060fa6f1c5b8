﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Commands
{
    public class CreateUser : ICommand
    {
        public CreateUser(
            Guid userId,
            string email,
            string password,
            UserRole role)
        {
            UserId = userId;
            Email = email;
            Password = password;
            Role = role;
        }

        public CreateUser(
            string email,
            string password,
            UserRole role) : this(
                Guid.NewGuid(),
                email,
                password,
                role) { }

        public string Email { get; }
        public string Password { get; }
        public UserRole Role { get; }
        public Guid UserId { get; }
    }

    public class CreateUserHandler : DapperRequestHandler<CreateUser, CommandResult>
    {
        public CreateUserHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateUser request)
        {
            await db.InsertAsync(new UserDto
            {
                Id = request.UserId,
                Email = request.Email,
                Password = request.Password,
                Role = request.Role,
            });

            return CommandResult.Success();
        }
    }
}
