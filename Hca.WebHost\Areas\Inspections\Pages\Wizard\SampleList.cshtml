﻿@page "/inspections/{inspectionId:guid}/samples"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.SampleListModel
@{
    Layout = null;
}

@if (Model.Samples.Any())
{
    <table class="table table-striped table-bordered table-hover" id="tblSamples">
        <thead class="thead-dark">
            <tr>
                <th width="75%"></th>
                <th><em class="fa fa-camera fa-fw"></em></th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            @foreach (var sample in Model.Samples)
            {
                <tr data-row-id="@sample.Id">
                    <td>@sample.SampleReference</td>
                    <td>@sample.ImageCount</td>
                    @if (Model.Selected.HasValue && Model.Selected == sample.Id)
                    {
                        <td>
                            <em class="fa fa-edit fa-fw"></em>
                        </td>
                    }
                    else
                    {
                        <td></td>
                    }
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <text>No samples found</text>
}