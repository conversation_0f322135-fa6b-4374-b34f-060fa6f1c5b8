﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using Microsoft.Azure.Services.AppAuthentication;

namespace Hca.Lib.Data
{
    public class DbHelper : IDbHelper
    {
        private readonly DbConfig _config;

        public DbHelper(DbConfig config)
        {
            _config = config;
        }

        public async Task<IDbConnection> CreateConnectionAsync()
        {
            var conn = new SqlConnection(_config.ConnectionString);

            if (conn.ConnectionString.ToLower().Contains("database.windows.net")
                && !conn.ConnectionString.ToLower().Contains("password")
                && !conn.ConnectionString.ToLower().Contains("user id")
                && !conn.ConnectionString.ToLower().Contains("userid")
                && !conn.ConnectionString.ToLower().Contains("uid")
                && !conn.ConnectionString.ToLower().Contains("pwd"))
            {
                conn.AccessToken =
                    await new AzureServiceTokenProvider(_config.AzureIdentityConnectionString)
                        .GetAccessTokenAsync("https://database.windows.net/");
            }

            return conn;
        }

        public Task InsertAsync<T>(T dto)
        {
            var type = dto.GetType();

            if (type.GetProperty("Id") != null)
            {
                return SingleOperationAsync(db => db.InsertAsync<Guid, T>(dto));
            }

            var keyProperty = type.GetProperties().SingleOrDefault(t => t.GetCustomAttributes(typeof(KeyAttribute), true).Any());

            if (keyProperty != null)
            {
                switch (keyProperty.PropertyType)
                {
                    case Type strType when strType == typeof(string): return SingleOperationAsync(db => db.InsertAsync<string, T>(dto));
                    case Type intType when intType == typeof(int): return SingleOperationAsync(db => db.InsertAsync<int, T>(dto));
                    case Type guidType when guidType == typeof(Guid): return SingleOperationAsync(db => db.InsertAsync<Guid, T>(dto));
                };
            }

            // this was the default before trying to sense the type
            return SingleOperationAsync(db => db.InsertAsync<Guid, T>(dto));
        }

        public Task UpdateAsync<T>(T dto) =>
            SingleOperationAsync(db => db.UpdateAsync(dto));

        public Task<int> ExecuteAsync(string sql, object param = null) =>
             SingleOperationAsync(db => db.ExecuteAsync(sql, param));

        public Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null) =>
             SingleOperationAsync(db => db.QueryAsync<T>(sql, param));

        public Task<T> ExecuteScalarAsync<T>(string sql, object param = null) =>
             SingleOperationAsync(db => db.ExecuteScalarAsync<T>(sql, param));

        public Task<T> GetAsync<T>(Guid key) =>
             SingleOperationAsync(db => db.GetAsync<T>(key));

        public Task<T> GetAsync<T>(string key) =>
             SingleOperationAsync(db => db.GetAsync<T>(key));

        public Task<T> GetAsync<T>(string whereClause = null, object param = null) =>
             SingleOperationAsync(db => db.GetListAsync<T>(whereClause, param)).ContinueWith(r => r.Result.SingleOrDefault());

        public Task<T> QuerySingleOrDefaultAsync<T>(string sql, object param = null) =>
             SingleOperationAsync(db => db.QuerySingleOrDefaultAsync<T>(sql, param));

        public Task<int> DeleteAsync<T>(Guid id) =>
            SingleOperationAsync(db => db.DeleteAsync<T>(id));

        public Task<IEnumerable<T>> GetListAsync<T>(string whereClause = null, object param = null) =>
            SingleOperationAsync(db => db.GetListAsync<T>(whereClause, param));

        public Task DeleteAsync<T>(string whereClause, object param = null) =>
            SingleOperationAsync(db => db.DeleteListAsync<T>(whereClause, param));

        public Task<int> CountAsync<T>(string whereClause = null, object param = null) =>
            SingleOperationAsync(db => db.RecordCountAsync<T>(whereClause, param));

        public async Task<(IEnumerable<T> Items, int Total)> QueryPageAsync<T>(string query, int page, int pageSize, object param = null)
        {
            dynamic dynParam = (param ?? new { }).ToDynamic();
            dynParam.Offset = (page - 1) * pageSize;
            dynParam.PageSize = pageSize;

            using var db = await CreateConnectionAsync();
            var multi = await db.QueryMultipleAsync(query, (object)dynParam);

            return (await multi.ReadAsync<T>(), await multi.ReadFirstAsync<int>());
        }

        private async Task<T> SingleOperationAsync<T>(Func<IDbConnection, Task<T>> op)
        {
            T result = default;

            using (var db = await CreateConnectionAsync())
            {
                result = await op(db);
            }

            return result;
        }

        public Task<IEnumerable<T>> QueryAsync<T, TChild>(string sql, Func<T, TChild, T> map, string splitOn = "Id", object param = null) =>
            SingleOperationAsync(db => db.QueryAsync(sql, map, param, splitOn: splitOn));
    }
}
