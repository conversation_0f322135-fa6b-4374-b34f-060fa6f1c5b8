﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Material;

public class Index : InspectionValueListPage
{
    public Index(
        ValueListsService inspectionValuesService,
        IMediator mediator,
        ViewManager viewManager) : base(inspectionValuesService, viewManager, mediator)
    {
    }

    public override ValueListType ValueListType => ValueListType.Material;
}
