﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents.Commands;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard.ReportFields;

[IgnoreAntiforgeryToken(Order = 1001)]
public class TextFieldPostModel : HcaPageModel
{
    public TextFieldPostModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {

    }

    public async Task<IActionResult> OnPostAsync(
        Guid reportId,
        Guid sectionId,
        Guid fieldId,
        CancellationToken cancellationToken)
    {
        await _mediator.Send(new SetDocumentTextField(
                reportId,
                sectionId,
                fieldId,
                Input.Actual),
            cancellationToken);

        return new OkResult();
    }

    [BindProperty]
    public TextPost Input { get; set; }
}

public class TextPost
{
    public string Actual { get; set; }
}
