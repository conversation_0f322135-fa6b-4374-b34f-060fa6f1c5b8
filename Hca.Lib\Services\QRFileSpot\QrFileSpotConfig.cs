﻿namespace Hca.Lib.Services.QRFileSpot;

public class QrFileSpotConfig
{
    public string ServiceUri { get; set; }

    public string ClientId { get; set; }

    public string ClientSecret { get; set; } 

    public static string WebUri {  get; set; }

    public static string GetDocumentQrCodeImageUrl(string qrCodeId) => qrCodeId.IsPopulated() ? $"{WebUri}/qr/{qrCodeId}.png" : null;

    public static string GetDocumentQrCodeDocumentUrl(string qrCodeId) => qrCodeId.IsPopulated() ? $"{WebUri}/doc/{qrCodeId}" : null;
}
