﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class EnableClientContactLogin : ICommand
    {
        public EnableClientContactLogin(
            Guid contactId,
            string newPassword)
        {
            ContactId = contactId;
            NewPassword = newPassword;
        }

        public Guid ContactId { get; }
        public string NewPassword { get; }
    }

    public class EnableClientContactLoginHandler : DapperRequestHandler<EnableClientContactLogin, CommandResult>
    {
        public EnableClientContactLoginHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EnableClientContactLogin request)
        {
            var contact = await db.GetAsync<ContactDto>(request.ContactId);
            contact.Password = request.NewPassword;
            await db.UpdateAsync(contact);
            return CommandResult.Success();
        }
    }
}
