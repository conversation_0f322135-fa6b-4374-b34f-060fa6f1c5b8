﻿using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.TagHelpers;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("textarea", Attributes = "asp-for, row-label")]
    public class FormRowTextAreaTagHelper : TextAreaTagHelper
    {
        private readonly HttpContext _httpContext;

        public FormRowTextAreaTagHelper(
            IHtmlGenerator generator,
            IHttpContextAccessor httpContextAccessor) : base(generator)
        {
            _httpContext = httpContextAccessor.HttpContext;
        }

        [HtmlAttributeName("row-label")]
        public string HcaLabel { get; set; }

        [HtmlAttributeName("asp-id")]
        public string Id { get; set; }

        [HtmlAttributeName("asp-style")]
        public string Style { get; set; }

        [HtmlAttributeName("asp-single-row")]
        public bool SingleRow { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            base.Process(context, output);

            var preContent =
                $"<div class=\"form-group {(SingleRow ? "row" : "")}\" id=\"{Id}\" style=\"{Style}\">" +
                $"<label class=\"{(SingleRow ? "col-xl-2 col-form-label" : "text-bold")}\">{WebUtility.HtmlEncode(HcaLabel)}</label>";

            if (SingleRow) preContent += $"<div class=\"col-xl-10 input-group\">";

            var postContent = SingleRow ? "</div></div>" : "</div>";

            if (!_httpContext.IsNewMode() && !_httpContext.IsEditMode())
            {
                var value = output.Content.GetContent();
                output.Reinitialize("label", TagMode.StartTagAndEndTag);
                output.Attributes.SetAttribute("class", "form-control");
                output.Attributes.SetAttribute("style", "height: auto;");
                output.Content.SetHtmlContent(value.Replace("&#xD;&#xA;", "<br />"));
                output.PreElement.SetHtmlContent(preContent);
                output.PostElement.SetHtmlContent(postContent);
                return;
            }

            output.Attributes.Add("class", "form-control");

            output.PreElement.SetHtmlContent(preContent);

            output.PostElement.SetHtmlContent(postContent);

            output.TagMode = TagMode.StartTagAndEndTag;
        }
    }
}
