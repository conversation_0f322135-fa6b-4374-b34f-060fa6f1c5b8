﻿@page "/InspectionValues/floors/{id:guid}"
@model Hca.WebHost.Areas.InspectionValues.Pages.FloorModel
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Floor Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Floors",
        Value = Model.Value,
    };
}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />