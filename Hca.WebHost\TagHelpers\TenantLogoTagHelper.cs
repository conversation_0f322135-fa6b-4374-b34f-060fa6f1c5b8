﻿using Hca.WebHost.Pipeline;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System.Threading.Tasks;

namespace Hca.WebHost.TagHelpers
{
    public class TenantLogoTagHelper : TagHelper
    {
        private readonly HttpContext _httpContext;
        private readonly ViewManager _viewManager;

        public TenantLogoTagHelper(
            IHttpContextAccessor httpContextAccessor,
            ViewManager viewManager)
        {
            _httpContext = httpContextAccessor.HttpContext;
            _viewManager = viewManager;
        }

        [HtmlAttributeName("class")]
        public string Class { get; set; } = "block-center rounded";

        [HtmlAttributeName("default-logo-url")]
        public string DefaultLogoUrl { get; set; } = "hca_logo.png";


        [HtmlAttributeName("style")]
        public string Style{ get; set; } = "width: 290px;";

        public override async Task ProcessAsync(TagHelperContext context, TagHelperOutput output)
        {
            var tenant = await _viewManager.GetCurrentClientAsync();
            var imageUrl = tenant?.LogoUrl ?? $"{_httpContext.Request.Scheme}://{_httpContext.Request.Host}/images/{DefaultLogoUrl}";
            var altText = $"{tenant?.ClientName ?? "Trustist"} Logo";

            output.TagName = "span";
            output.TagMode = TagMode.StartTagAndEndTag;
            output.Content.SetHtmlContent($"<img class=\"{Class}\" style=\"{Style}\" src=\"{imageUrl}\" alt=\"{altText}\">");
        }
    }
}
