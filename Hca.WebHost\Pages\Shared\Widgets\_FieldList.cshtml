﻿@model (Guid DocumentId, Hca.Lib.Features.Documents.Fields.DocumentSection Section)
@using Hca.Lib.Features.Documents.Fields
@{
    Layout = null;
}
@foreach (var field in Model.Section.DocumentFields)
{
    var controlName = field.Value.FieldType switch
    {
        FieldType.Choice => "_FieldChoice",
        FieldType.Text => "_FieldText",
        _ => throw new NotImplementedException(),
    };

    <form method="post"
          action="/documents/@(Model.DocumentId)/sections/@(Model.Section.DocumentSectionId)/fields/@(field.DocumentFieldId)/@field.Value.FieldType.ToFieldTypeString()">
        <partial name="@controlName" model="@field" />

        <input name="fieldType" type="hidden" value="@field.Value.FieldType" />
    </form>

    <hr />
}