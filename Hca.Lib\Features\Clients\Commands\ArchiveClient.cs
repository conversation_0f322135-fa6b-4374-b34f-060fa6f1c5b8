﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands;

public class ArchiveClient : ICommand
{
    public Guid ClientId { get; }
    public string ArchiveReason { get; }
    public DateTime? Archived { get; }

    public ArchiveClient(Guid clientId, string archiveReason, DateTime? archived = null)
    {
        ClientId = clientId;
        ArchiveReason = archiveReason;
        Archived = archived ?? DateTime.UtcNow;
    }
}

public class ArchiveClientHandler : DapperRequestHandler<ArchiveClient, CommandResult>
{
    public ArchiveClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, ArchiveClient request)
    {
        await db.ExecuteAsync(
            $"UPDATE {TableNames.Clients} SET " +
            $"{nameof(ClientDto.Archived)}=@{nameof(request.Archived)}," +
            $"{nameof(ClientDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
            $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}", request);

        await db.ExecuteAsync(
            $"UPDATE {TableNames.Sites} SET " +
            $"{nameof(SiteDto.Archived)}=@{nameof(request.Archived)}, " +
            $"{nameof(SiteDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
            $"WHERE {nameof(SiteDto.ClientId)}=@{nameof(request.ClientId)} " +
            $"AND {nameof(SiteDto.Archived)} IS NULL", request);

        await db.ExecuteAsync(
            $"UPDATE {TableNames.Properties} SET " +
            $"{nameof(PropertyDto.Archived)}=@{nameof(request.Archived)}, " +
            $"{nameof(PropertyDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
            $"WHERE {nameof(PropertyDto.ClientId)}=@{nameof(request.ClientId)} " +
            $"AND {nameof(PropertyDto.Archived)} IS NULL", request);

        return CommandResult.Success();
    }
}
