﻿using System.Web;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers.Forms
{
    [HtmlTargetElement("save-cancel-footer")]
    public class SaveCancelFooterTagHelper : TagHelper
    {
        private readonly HttpContext _httpContext;

        public SaveCancelFooterTagHelper(IHttpContextAccessor httpContextAccessor)
        {
            _httpContext = httpContextAccessor.HttpContext;
        }

        [HtmlAttributeName("CancelUrl")]
        public string CancelUrl { get; set; }

        [HtmlAttributeName("EditUrl")]
        public string EditUrl { get; set; }

        [HtmlAttributeName("EditButton")]
        public bool EditButton { get; set; }

        [HtmlAttributeName("asp-id")]
        public string Id { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            if (_httpContext.IsEditMode() || _httpContext.IsNewMode())
            {
                WriteFooter(output,
                    $"<a href=\"{GetCancelUrl()}\" class=\"btn\">Cancel</a>" +
                    "<input type=\"submit\" class=\"btn btn-success\" value=\"Save\" />");
                return;
            }

            if (EditButton)
            {
                WriteFooter(output, $"<a href=\"{GetEditUrl()}\" class=\"btn btn-info\">Edit</a>");
                return;
            }

            output.TagName = "span";
            output.Attributes.SetAttribute("style", "display: none");
        }

        private string GetEditUrl()
        {
            var request = _httpContext.Request;
            var querystring = HttpUtility.ParseQueryString(request.QueryString.ToString());
            querystring.Add(Constants.QuerystringModeKey, Constants.EditMode);

            return EditUrl ??
                string.Concat(
                        request.Scheme,
                        "://",
                        request.Host.ToUriComponent(),
                        request.PathBase.ToUriComponent(),
                        request.Path.ToUriComponent(),
                        $"?{querystring}");
        }

        private string GetCancelUrl()
        {
            var request = _httpContext.Request;
            var querystring = HttpUtility.ParseQueryString(request.QueryString.ToString());
            querystring.Remove(Constants.QuerystringModeKey);

            return CancelUrl ??
                string.Concat(
                        request.Scheme,
                        "://",
                        request.Host.ToUriComponent(),
                        request.PathBase.ToUriComponent(),
                        request.Path.ToUriComponent(),
                        $"?{querystring}");
        }

        private void WriteFooter(TagHelperOutput output, string content)
        {
            output.TagName = "div";
            output.Attributes.Add("id", Id);
            output.Attributes.Add("class", "card-footer");
            output.Content.SetHtmlContent(content);
        }
    }
}
