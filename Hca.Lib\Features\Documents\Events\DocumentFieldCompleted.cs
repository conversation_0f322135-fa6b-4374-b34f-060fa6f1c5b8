﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Documents.Events
{
    public class DocumentFieldCompleted : IEvent
    {
        public DocumentFieldCompleted(
            Guid documentId,
            Guid sectionId,
            Guid fieldId)
        {
            DocumentId = documentId;
            SectionId = sectionId;
            FieldId = fieldId;
        }

        public Guid DocumentId { get; }
        public Guid SectionId { get; }
        public Guid FieldId { get; }
    }

    public class DocumentFieldUpdated_UpdateSectionCompleted : DapperEventHandler<DocumentFieldCompleted>
    {
        private readonly IMediator _mediator;
        public DocumentFieldUpdated_UpdateSectionCompleted(IMediator mediator, IDbHelper dbHelper) : base(dbHelper) { _mediator = mediator; }

        public override async Task OnHandleAsync(IDbHelper db, DocumentFieldCompleted request)
        {
            var fields = await db.GetListAsync<DocumentFieldDto>("WHERE DocumentSectionId = @SectionId", request);

            if (fields.Any(f => !f.IsComplete)) return;

            var section = await db.GetAsync<DocumentSectionDto>(request.SectionId);
            section.SectionComplete = true;
            await db.UpdateAsync(section);

            await _mediator.Publish(new DocumentSectionCompleted(request.DocumentId, request.SectionId));
        }
    }
}
