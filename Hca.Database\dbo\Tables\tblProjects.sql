﻿CREATE TABLE [dbo].[tblProjects] (
    [Id]            UNIQUEIDENTIFIER CONSTRAINT [DF_Projects_Id] DEFAULT (newid()) NOT NULL,
    [Created]       DATETIME         CONSTRAINT [DF_Projects_Created] DEFAULT (getdate()) NOT NULL,
    [ClientId]      UNIQUEIDENTIFIER NOT NULL,
    [QuoteId]       UNIQUEIDENTIFIER NULL,
    [ProjectNumber] NVARCHAR (16)    NOT NULL,
    CONSTRAINT [PK_Projects] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Projects_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Projects_Quotes] FOREIGN KEY ([QuoteId]) REFERENCES [dbo].[tblQuotes] ([Id])
);

