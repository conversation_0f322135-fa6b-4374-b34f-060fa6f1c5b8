﻿using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Features.Users.Queries;
using MediatR;

// OPEN XML RESOURCES
// https://github.com/OfficeDev/Open-XML-SDK
// https://github.com/EricWhiteDev/Open-Xml-PowerTools
// http://www.ericwhite.com/blog/forums/topic/merging-manipulated-word-docs/
// https://docs.microsoft.com/en-us/office/open-xml/how-to-insert-a-table-into-a-word-processing-document
// https://stackoverflow.com/questions/18150273/insert-a-table-into-a-middle-of-word-processing-document-open-xml-sdk
// https://stackoverflow.com/questions/21373991/openxml-images-added-with-addimagepart-not-showing-up-in-document-xml-rels
// https://stackoverflow.com/questions/41877119/c-sharp-wordprocessingdocument-insert-an-image-in-a-cell

namespace Hca.Lib.Features.Documents.Publishing
{
    public class DocumentCompiler
    {
        private readonly IMediator _mediator;

        public DocumentCompiler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<byte[]> BuildDocument(DocumentModel model)
        {
            var doc = DocumentCombiner.Combine(model.TemplateFiles)
                .ConvertTemplateToDocument();

            if (model.AdditionalTransforms?.Any() ?? false)
            {
                foreach (var transform in model.AdditionalTransforms)
                {
                    doc = transform(doc);
                }
            }

            doc.AddSections(model.Sections)
                .ReplaceTags(model.Tags);

            var user = await _mediator.Send(new GetCurrentUser());
            if (user != null)
            {
                doc.ReplaceTags(TagFactory.CreateStandardTags(user));

                if (!string.IsNullOrWhiteSpace(user.SignatureFileName))
                {
                    var signature = await _mediator.Send(new GetUserSignatureFile(user.Id));
                    doc.AddSignature(signature, user.SignatureFileName);
                }
            }

            return doc.AsWmlDocument.DocumentByteArray;
        }
    }
}
