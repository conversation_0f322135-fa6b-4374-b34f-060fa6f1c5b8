﻿CREATE TABLE [dbo].[tblInspectionSamples] (
    [Id]                       UNIQUEIDENTIFIER CONSTRAINT [DF_InspectionSamples_Id] DEFAULT (newid()) NOT NULL,
    [Created]                  DATETIME         CONSTRAINT [DF_InspectionSamples_Created] DEFAULT (getdate()) NOT NULL,
    [InspectionId]             UNIQUEIDENTIFIER NOT NULL,
    [SampleReference]          NVARCHAR (MAX)   NOT NULL,
    [SampleNotes]              NVARCHAR (MAX)   NULL,
    [ImageCount]               INT              DEFAULT ((0)) NOT NULL,
    [DefaultImageUrl]          NVARCHAR (MAX)   NULL,
    [Status]                   NVARCHAR (MAX)   NULL,
    [FloorPlanColour]          UNIQUEIDENTIFIER NULL,
    [Floor]                    UNIQUEIDENTIFIER NULL,
    [FloorActual]              NVARCHAR (MAX)   NULL,
    [FloorPlanReference]       NVARCHAR (MAX)   NULL,
    [RoomUsage]                NVARCHAR (MAX)   NULL,
    [Location]                 NVARCHAR (MAX)   NULL,
    [Material]                 UNIQUEIDENTIFIER NULL,
    [MaterialActual]           NVARCHAR (MAX)   NULL,
    [MaterialColour]           NVARCHAR (MAX)   NULL,
    [Quantity]                 NVARCHAR (MAX)   NULL,
    [QuantityUnit]             UNIQUEIDENTIFIER NULL,
    [ProductType]              TINYINT          NULL,
    [ExtentOfDamage]           TINYINT          NULL,
    [SurfaceTreatment]         TINYINT          NULL,
    [AsbestosType]             TINYINT          NULL,
    [OccupantActivity]         TINYINT          NULL,
    [LikelihoodOfDisturbance]  TINYINT          NULL,
    [NumberOfOccupants]        TINYINT          NULL,
    [TypeOfMaintenance]        TINYINT          NULL,
    [Accessibility]            TINYINT          NULL,
    [ManagementRecommendation] UNIQUEIDENTIFIER NULL,
    [Priority]                 UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_InspectionSamples] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [PK_InspectionSamples_Inspections] FOREIGN KEY ([InspectionId]) REFERENCES [dbo].[tblInspections] ([Id])
);

