﻿using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Models;
using LazyCache;
using MediatR;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Services;

public class PropertyCountsService
{
    private readonly IMediator _mediator;
    private readonly IAppCache _appCache;

    public PropertyCountsService(IAppCache appCache, IMediator mediator)
    {
        _appCache = appCache ?? throw new ArgumentNullException(nameof(appCache));
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    }

    public async Task<PropertyCountsModel> GetPropertyChildCountsAsync(Guid clientId, Guid propertyId)
    {
        return await _appCache.GetOrAddAsync($"PropertyChildCounts_{propertyId}", async () =>
        {
            var propertyCounts = await _mediator.Send(new GetPropertyCounts(clientId, propertyId));
            return propertyCounts;
        }, DateTimeOffset.UtcNow.AddMinutes(10));
    }

    public void ClearPropertyCountsAsync(Guid propertyId)
    {
        _appCache.Remove($"PropertyChildCounts_{propertyId}");
    }
}
