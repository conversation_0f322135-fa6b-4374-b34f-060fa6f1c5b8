﻿using System;

namespace Hca.Lib.Features.Documents
{
    public enum DocumentStatus
    {
        InProgress = 20,
        ReadyForPublishing = 40,
        Published = 60,
        Archived = 80
    }

    public static class DocumentStatusExtensions
    {
        public static string DisplayText(this DocumentStatus status)
        {
            return status switch
            {
                DocumentStatus.Archived => "Archived",
                DocumentStatus.InProgress => "In Progress",
                DocumentStatus.ReadyForPublishing => " Ready For Publishing",
                DocumentStatus.Published => "Published",
                _ => throw new NotImplementedException()
            };
        }
    }
}
