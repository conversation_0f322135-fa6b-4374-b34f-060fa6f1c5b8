﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Queries
{

    public class GetQuote : IQuery<QuoteDto>
    {
        public GetQuote(Guid quoteId)
        {
            QuoteId = quoteId;
        }

        public Guid QuoteId { get; }
    }

    public class GetQuoteHandler : DapperRequestHandler<GetQuote, QuoteDto>
    {
        public GetQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<QuoteDto> OnHandleAsync(IDbHelper db, GetQuote request)
        {
            return await db.GetAsync<QuoteDto>(request.QuoteId);
        }
    }
}
