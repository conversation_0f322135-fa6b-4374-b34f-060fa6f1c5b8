﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Config.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Queries
{
    public class GetNextQuoteNumber : IQuery<QuoteNumber> { }

    public class GetNextQuoteNumberHandler : IRequestHandler<GetNextQuoteNumber, QuoteNumber>
    {
        private readonly IMediator _mediator;

        public GetNextQuoteNumberHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<QuoteNumber> Handle(GetNextQuoteNumber request, CancellationToken cancellationToken) =>
            _mediator.Send(new GetIncrementConfig("QuoteNumber")).ContinueWith(c => new QuoteNumber(c.Result));
    }
}
