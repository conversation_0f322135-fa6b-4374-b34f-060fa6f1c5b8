﻿@page "/clients/{clientId:guid}/contacts/{id}"
@model Hca.WebHost.Areas.Clients.Pages.ClientContactModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;

    var isNew = Model.Id.ToLower() == "new";
    var breadcrumbName = isNew ? "New" : $"{Model.Contact.FirstName} {Model.Contact.LastName}";
    var heading = isNew ? "Add New Contact" : "Edit Contact";
}

<div class="row" id="divNewContact">
    <div class="col">
        @if (Model.ErrorMessage.IsPopulated())
        {
            <div class="card card-default">
                <div class="card-header d-flex align-items-center">
                    <div class="d-flex justify-content-center col">
                        <div class="h4 m-0 text-center">Error saving user</div>
                    </div>
                </div>
                <div class="card-body">
                    <p>Could not save user</p>
                    <p>@Model.ErrorMessage</p>
                    <p><a class="btn btn-info" href="@Urls.ClientContacts(Model.Client.Id)">Continue</a></p>
                </div>
            </div>
        }
        else if (Model.NewPassword.IsNullOrEmpty())
        {
            <form method="post">
                <div class="card card-default">
                    <div class="card-header d-flex align-items-center">
                        <div class="d-flex justify-content-center col">
                            <div class="h4 m-0 text-center">@heading</div>
                        </div>
                    </div>
                    <div class="card-body">
                        <input type="hidden" asp-for="Contact.Id" />
                        <input type="hidden" asp-for="Contact.AddressId" />
                        <input type="hidden" asp-for="Contact.Password" />
                        <input type="text" asp-for="Contact.FirstName" row-label="First Name" />
                        <input type="text" asp-for="Contact.LastName" row-label="Last Name" />
                        <input type="text" asp-for="Contact.Email" row-label="Email" />
                        <input type="text" asp-for="Contact.Position" row-label="Position" />
                        <input type="text" asp-for="Contact.OfficePhone" row-label="Office Phone" />
                        <input type="text" asp-for="Contact.MobilePhone" row-label="Mobile Phone" />
                        <input type="checkbox" asp-for="CanLogin" row-label="Can Login?" />
                    </div>
                    <save-cancel-footer EditButton="true"></save-cancel-footer>
                </div>
            </form>
        }
        else
        {
            <div class="card card-default">
                <div class="card-header d-flex align-items-center">
                    <div class="d-flex justify-content-center col">
                        <div class="h4 m-0 text-center">New Password</div>
                    </div>
                </div>
                <div class="card-body">
                    <p>Password set to @Model.NewPassword</p>
                    <p>If you want to send this to the user copy it now as it will never be shown again.</p>
                    <p><a class="btn btn-info" href="@Urls.ClientContact(Model.Client.Id, Model.Contact.Id)">Continue</a></p>
                </div>
            </div>
        }
    </div>
</div>        

@section scripts{
    <script>
        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientContacts(Model.Client.Id)', text: 'Contacts' },
                { text: '@Model.Contact.FirstName @Model.Contact.LastName' }
            ]);
        });

    </script>
}
