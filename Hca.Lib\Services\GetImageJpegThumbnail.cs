﻿using System.IO;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Processing;

namespace Hca.Lib.Services
{
    public class GetImageJpegThumbnail : IRequest<byte[]>
    {
        public GetImageJpegThumbnail(
            byte[] image,
            int width,
            int height)
        {
            Image = image;
            Width = width;
            Height = height;
        }

        public GetImageJpegThumbnail(
            Stream imageStream,
            int width,
            int height)
        {
            ImageStream = imageStream;
            Width = width;
            Height = height;
        }

        public Stream ImageStream { get; }
        public byte[] Image { get; }
        public int Width { get; }
        public int Height { get; }
    }

    public class GetImageThumbnailHandler : IRequestHandler<GetImageJpegThumbnail, byte[]>
    {
        public async Task<byte[]> Handle(GetImageJpegThumbnail request, CancellationToken cancellationToken)
        {
            // exit if the stream is not an image that is able to be processed by any of the available decoders
            if (request.ImageStream == null) return null;

            try
            {
                var format = Image.DetectFormat(request.ImageStream);
                if (format == null) return null;
            }
            catch (UnknownImageFormatException) 
            {
                return null;
            }

            using var imageStream = request.ImageStream != null
                ? await Image.LoadAsync(request.ImageStream, cancellationToken)
                : Image.Load(request.Image);

            if (request.Width < 0 || request.Width > imageStream.Width ||
                request.Height < 0 || request.Height > imageStream.Height)
            {
                return ImageToStream(imageStream);
            }

            return GenerateThumbnailByWidthHeight(imageStream, request.Width, request.Height);
        }

        private static byte[] GenerateThumbnailByWidthHeight(Image image, int w, int h)
        {
            image.Mutate(x => x.Resize(w, h));
            return ImageToStream(image);
        }

        private static byte[] ImageToStream(Image image)
        {
            using var returnImageStream = new MemoryStream();
            image.Save(returnImageStream, new JpegEncoder());
            return returnImageStream.ToArray();
        }
    }
}
