using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Hca.Lib.Identity;
using System.Threading.Tasks;

namespace Hca.WebHost.Controllers
{
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    public class SessionController : ControllerBase
    {
        private readonly SignInManager<HcaUser> _signInManager;

        public SessionController(SignInManager<HcaUser> signInManager)
        {
            _signInManager = signInManager;
        }

        /// <summary>
        /// Extends the current user session by refreshing the authentication cookie
        /// </summary>
        /// <returns>Success response if session was extended</returns>
        [HttpPost("extend")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExtendSession()
        {
            try
            {
                // Get the current user
                var user = await _signInManager.UserManager.GetUserAsync(User);
                if (user == null)
                {
                    return Unauthorized(new { message = "User not found" });
                }

                // Refresh the sign-in to extend the cookie expiration
                await _signInManager.RefreshSignInAsync(user);

                return Ok(new { 
                    message = "Session extended successfully",
                    timestamp = System.DateTime.UtcNow
                });
            }
            catch (System.Exception ex)
            {
                // Log the exception (you might want to use your logging framework here)
                return StatusCode(500, new { message = "Failed to extend session" });
            }
        }

        /// <summary>
        /// Gets the current session status and remaining time
        /// </summary>
        /// <returns>Session information</returns>
        [HttpGet("status")]
        public IActionResult GetSessionStatus()
        {
            try
            {
                if (!User.Identity.IsAuthenticated)
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                // Get authentication properties to check expiration
                var authResult = HttpContext.AuthenticateAsync().Result;
                var expiresUtc = authResult.Properties?.ExpiresUtc;

                return Ok(new
                {
                    isAuthenticated = true,
                    expiresUtc = expiresUtc,
                    serverTime = System.DateTime.UtcNow
                });
            }
            catch (System.Exception ex)
            {
                return StatusCode(500, new { message = "Failed to get session status" });
            }
        }
    }
}