﻿using System.Net;

namespace Hca.WebHost;

public static class StringExtensions
{
    public static string HtmlEncode(this string value, bool preserveNewLines = true)
    {
        var encoded = value == null ? null : WebUtility.HtmlEncode(value);
        return preserveNewLines ? encoded.Replace("\n", "<br/>") : encoded;
    }

    public static bool IsNullOrEmpty(this string value) => string.IsNullOrEmpty(value);

    public static bool IsNullOrWhiteSpace(this string value) => string.IsNullOrWhiteSpace(value);

    public static bool IsPopulated(this string value) => !value.IsNullOrWhiteSpace();

    public static bool IsEmpty(this string value) => value.IsNullOrWhiteSpace();
}
