﻿@page "/support"
@model Hca.WebHost.Areas.Clients.Pages.Support.IndexModel
@{
    ViewData["Title"] = "Support";
}
<div class="row">
    <div class="col-xl-3 col-lg-1"></div>
    <div class="col-xl-6 col-lg-10">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">NEED HELP?</small>
            </div>
            <div class="card-body">
                @if (Model.IsAdminUser)
                {
                    <div>
                        <p>As an admin user, you can't send messages to support.</p>
                    </div>
                }
                else if (Model.SendEmailSuccess)
                {
                    <div>
                        Thank you for your message.  We will be in touch soon.
                    </div>
                }
                else
                {
                    <form method="post">
                        <div class="form-group">
                            <label for="message">Message:</label>
                            <textarea asp-for="Message" class="form-control" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send</button>
                        @Html.AntiForgeryToken()
                    </form>
                }
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-1"></div>
</div>