﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;

namespace Hca.Lib.Features.Templates.Commands
{
    public class AddChoiceFieldToTemplateSection : TemplateCommand
    {
        public AddChoiceFieldToTemplateSection(
            Guid templateId,
            Guid sectionId,
            TemplateChoiceField choiceField) : base(templateId)
        {
            SectionId = sectionId;
            ChoiceField = choiceField;
        }

        public Guid SectionId { get; }
        public TemplateChoiceField ChoiceField { get; }
    }

    public class AddChoiceFieldToTemplateSectionHandler : DapperRequestHandler<AddChoiceFieldToTemplateSection, CommandResult>
    {
        public AddChoiceFieldToTemplateSectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddChoiceFieldToTemplateSection request)
        {
            if (request.ChoiceField.Value.FieldOrder == 0)
            {
                var sectionFieldCount = await db.ExecuteScalarAsync<int>(
                    "SELECT COUNT(1) FROM tblTemplateFields WHERE TemplateSectionId=@SectionId",
                    new { request.SectionId });

                request.ChoiceField.Value.FieldOrder = sectionFieldCount + 1;
            }

            await db.InsertAsync(new TemplateFieldDto
            {
                Id = request.ChoiceField.TemplateFieldId,
                TemplateSectionId = request.SectionId,
                FieldOrder = request.ChoiceField.Value.FieldOrder,
                FieldType = FieldType.Choice,
                Optional = request.ChoiceField.Value.IsOptional,
                FieldContent = request.ChoiceField.Value.FieldContent,
            });

            return CommandResult.Success();
        }
    }
}
