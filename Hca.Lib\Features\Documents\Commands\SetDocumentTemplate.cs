﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Commands
{
    public class SetDocumentTemplate : ICommand
    {
        public SetDocumentTemplate(
            Guid documentId,
            Guid templateId)
        {
            DocumentId = documentId;
            TemplateId = templateId;
        }

        public Guid DocumentId { get; }
        public Guid TemplateId { get; }
    }

    public class SetDocumentTemplateHandler : DapperRequestHandler<SetDocumentTemplate, CommandResult>
    {
        public SetDocumentTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetDocumentTemplate request)
        {
            var sql = @"
DECLARE @ImportSections TABLE (
    Id UNIQUEIDENTIFIER,
    SectionType INT,
    SectionTitle NVARCHAR(MAX),
    SectionContent NVARCHAR(MAX),
    SectionOrder INT,
    DocumentSectionId UNIQUEIDENTIFIER
)
DECLARE @ImportFields TABLE (
    Id UNIQUEIDENTIFIER,
    SectionId UNIQUEIDENTIFIER,
    FieldType INT,
    FieldContent NVARCHAR(MAX),
    FieldOrder INT,
    DocumentSectionId UNIQUEIDENTIFIER
)

INSERT INTO @ImportSections
SELECT Id, SectionType, SectionTitle, SectionContent, SectionOrder, NEWID()
FROM tblTemplateSections
WHERE TemplateId=@TemplateId;

INSERT INTO @ImportFields
SELECT Id, TemplateSectionId, FieldType, FieldContent, FieldOrder, NULL
FROM tblTemplateFields
WHERE TemplateSectionId IN (
    SELECT Id FROM @ImportSections
)

UPDATE @ImportFields 
SET DocumentSectionId = (
    SELECT impS.DocumentSectionId
    FROM @ImportSections impS
    WHERE SectionId = impS.Id
)

DELETE FROM tblDocumentFields WHERE DocumentSectionId IN (
    SELECT Id FROM tblDocumentSections
    WHERE DocumentId = @DocumentId
)

DELETE FROM tblDocumentSections WHERE DocumentId = @DocumentId

INSERT INTO tblDocumentSections (Id, DocumentId, SectionType, SectionTitle, SectionContent, SectionOrder)
SELECT DocumentSectionId, @DocumentId, SectionType, SectionTitle, SectionContent, SectionOrder
FROM @ImportSections

INSERT INTO tblDocumentFields (DocumentSectionId, FieldType, FieldContent, FieldOrder)
SELECT DocumentSectionId, FieldType, FieldContent, FieldOrder
FROM @ImportFields

UPDATE tblDocuments SET TemplateId = @TemplateId WHERE Id = @DocumentId";

            await db.ExecuteAsync(sql, request);

            return CommandResult.Success();
        }
    }
}
