﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections.Queries;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetSampleImagesBySampleId : IQueryMany<InspectionSampleImageDto>
    {
        public GetSampleImagesBySampleId(Guid inspectionId, Guid inspectionSampleId)
        {
            InspectionId = inspectionId;
            InspectionSampleId = inspectionSampleId;
        }

        public Guid InspectionId { get; }
        public Guid InspectionSampleId { get; }
    }

    public class GetSampleImagesBySampleIdHandler : DapperRequestHandler<GetSampleImagesBySampleId, DtoSet<InspectionSampleImageDto>>
    {
        public GetSampleImagesBySampleIdHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<InspectionSampleImageDto>> OnHandleAsync(IDbHelper db, GetSampleImagesBySampleId request)
        {
            return DtoSet.From(await db.GetListAsync<InspectionSampleImageDto>(
                "WHERE InspectionSampleId=@InspectionSampleId",
                new { request.InspectionSampleId }));
        }
    }
}
