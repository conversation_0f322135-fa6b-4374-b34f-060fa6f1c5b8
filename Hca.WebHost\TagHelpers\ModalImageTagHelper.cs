﻿using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("modalImage")]
    public class ModalImageTagHelper : TagHelper
    {
        [HtmlAttributeName("imageUrl")]
        public string ImageUrl { get; set; }

        [HtmlAttributeName("altText")]
        public string AltText { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "span";
            output.TagMode = TagMode.StartTagAndEndTag;
            output.Attributes.Add("data-toggle", "modal");
            output.Attributes.Add("data-target", "#imageModal");
            output.Attributes.Add("style", "cursor:pointer;");
            output.Attributes.Add("onclick", $"$('#imageModal img').attr('src', '{ImageUrl}')");

            output.Content.SetHtmlContent($"<img class=\"img-fluid\" style=\"max-height: 394px;\" src=\"{ImageUrl}\" alt=\"{AltText}\">");
        }
    }
}
