﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class SetInspectionSampleReference : InspectionCommand
    {
        public SetInspectionSampleReference(
            Guid inspectionId,
            Guid sampleId,
            string sampleReference) : base(inspectionId)
        {
            SampleId = sampleId;
            SampleReference = sampleReference;
        }

        public Guid SampleId { get; }
        public string SampleReference { get; }
    }

    public class SetInspectionReferenceTemplateHandler : Da<PERSON>RequestHandler<SetInspectionSampleReference, CommandResult>
    {
        public SetInspectionReferenceTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetInspectionSampleReference request)
        {
            var sample = await db.GetAsync<InspectionSampleDto>(request.SampleId);
            sample.SampleReference = request.SampleReference;
            await db.UpdateAsync(sample);

            return CommandResult.Success();
        }
    }
}
