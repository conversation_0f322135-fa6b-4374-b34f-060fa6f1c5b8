﻿using System;

namespace Hca.WebHost.TagHelpers.NavMenu
{
    public enum NavButtonType
    {
        Action,
        Edit,
        Delete,
        Archive,
    }

    public static class NavButtonExtensions
    {
        public static string ClassName(this NavButtonType type) =>
            type switch
            {
                NavButtonType.Action => "btn-success",
                NavButtonType.Delete => "btn-danger",
                NavButtonType.Edit => "btn-info",
                NavButtonType.Archive => "btn-dark",
                _ => throw new ArgumentException("Unknown nav button type"),
            };
    }
}
