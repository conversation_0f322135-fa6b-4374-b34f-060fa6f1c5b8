﻿using System.Collections.Generic;
using System.Linq;
using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Reports.Publishing
{
    public static class ReportSampleDocumentExtensions
    {
        public static CompiledDocument AddSamples(
            this CompiledDocument doc,
            IEnumerable<(InspectionSampleDto Sample, byte[] Image)> samples)
        {
            using var wordDoc = doc.AsWordProcessingDocument;
            var mainPart = wordDoc.MainDocumentPart;

            var tagToReplace = mainPart.FindFirstTextElement("{{samples}}");
            if (tagToReplace == null) return doc;

            var insertAfter = tagToReplace.Parent.Parent;

            if (samples != null)
            {
                foreach (var element in samples
                    .SelectMany(s => CreateSamplePage(mainPart, s.<PERSON>, s.Image)))
                {
                    insertAfter = insertAfter.InsertAfterSelf(element);
                }
            }
            // else replace with a no samples found message

            mainPart.Document.Body.RemoveChild(tagToReplace.Parent.Parent);

            mainPart.Document.Save();

            return doc;
        }

        private static OpenXmlElement[] CreateSamplePage(
            MainDocumentPart mainDocumentPart,
            InspectionSampleDto sample,
            byte[] image)
        {
            return new[] {
                CreateSampleHeaderTable(sample),
                CreateSampleImageTable(mainDocumentPart, image, sample)
            };
        }

        private static Table CreateSampleHeaderTable(InspectionSampleDto sample)
        {
            var table = OpenXmlFactory.CreateTable();
            table.AppendRow()
                .AppendCell("Sample Number")
                .AppendCell(sample.SampleReference)
                .AppendCell("Floor Plan Colour")
                .AppendCell("COLOUR STUFF");
            return table;
        }

        private static Table CreateSampleImageTable(MainDocumentPart mainDocumentPart, byte[] image, InspectionSampleDto sample)
        {
            var table = OpenXmlFactory.CreateTable();
            var row = table.AppendRow();

            if (image == null)
            {
                row.AppendCell("No image found for this sample");
                return table;
            }

            row.AppendImageCell(mainDocumentPart.CreateImage(image, sample.DefaultImageUrl));

            return table;
        }
    }
}
