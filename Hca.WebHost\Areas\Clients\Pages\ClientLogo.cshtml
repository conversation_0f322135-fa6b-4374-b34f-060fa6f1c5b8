﻿@page "/clients/{clientId:guid}/logo"
@model Hca.WebHost.Areas.Clients.Pages.ClientLogoModel
@{
    Layout = "_LayoutClient";
    ViewData["Title"] = "Upload Logo - " + Model.Client.ClientName;
    ViewData["Client"] = Model.Client;
}

<form method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col">
            <div class="card card-default">
                <div class="card-body">
                    <div class="py-4">
                        <div class="form-group row">
                            <label class="col-xl-2 col-form-label" for="LogoUrl">Client Logo</label>
                            <div class="col-xl-10">
                                <input class="form-control filestyle"
                                       type="file"
                                       data-classbutton="btn btn-secondary"
                                       data-classinput="form-control inline"
                                       data-icon="&lt;span class='fa fa-upload mr'&gt;&lt;/span&gt;"
                                       asp-for="Logo" />
                            </div>
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="@Urls.Client(Model.Client.Id)">cancel</a>&nbsp;&nbsp;&nbsp;
                        <input type="submit" class="btn btn-success" value="Save" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

@section scripts{
    <script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>
    <script>

        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Upload Client Logo' }
            ]);
        });

    </script>
}
