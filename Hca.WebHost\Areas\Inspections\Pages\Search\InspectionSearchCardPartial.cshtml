﻿@page "/inspections/search"
@model Hca.WebHost.Areas.Inspections.Pages.Search.InspectionSearchCardPartialModel
@{
    Layout = null;
}
<div class="card card-default">
    <div class="card-header">
        @Model.Title
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <partial name="_InspectionSearchTablePartial" model="Model.Inspections.Items" />
        </div>
    </div>
    <div class="card-footer">
        <div class="d-flex">
            <button class="btn btn-sm btn-secondary" id="btnClearClientSearch">Clear</button>
            <nav class="ml-auto">
                <ul class="pagination pagination-sm">
                    @for (int i = 1; i <= Model.Inspections.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.Inspections.CurrentPage ? " active" : "")">
                            <a class="page-link" href="#">@i</a>
                        </li>
                    }
                </ul>
            </nav>
        </div>
    </div>
</div>