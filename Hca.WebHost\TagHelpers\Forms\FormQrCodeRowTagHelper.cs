﻿using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System;
using System.Linq;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("qrcode")]
    public class FormQrCodeRowTagHelper : TagHelper
    {
        private readonly IHtmlGenerator _html;

        public FormQrCodeRowTagHelper(IHtmlGenerator html) : base()
        {
            _html = html;
        }

        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("image-url")]
        public string ImageUrl { get; set; }

        [ViewContext]
        [HtmlAttributeNotBound]
        public ViewContext ViewContext { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            var targetId = "qr" + Guid.NewGuid().ToString().Split('-').First();   
            output.TagMode = TagMode.StartTagAndEndTag;
            output.Attributes.Add("style", "width: 120px; text-align: center;");

            if (string.IsNullOrWhiteSpace(ImageUrl))
            {
                output.TagName = "form";
                output.Attributes.Add("hx-post", Action);
                output.Attributes.Add("hx-swap", "outerHTML");
                output.Attributes.Add("hx-target", $"#{targetId}"); 
                output.Attributes.Add("id", targetId);
                output.Attributes.Add("style", "display: inline;");

                output.PreContent.SetHtmlContent(
                    _html.GenerateAntiforgery(ViewContext).ToHtmlString() +
                    $"<button class=\"btn btn-sm mr-2 text-nowrap w-100\" alt=\"Create QR Code\" " +
                    $"data-confirm-dialog=\"This will create a QR Code for this document, do you wish to proceed?\">Generate QR Code");

                output.PostContent.SetHtmlContent("</button>");
            }
            else
            {
                output.TagName = "span";
                output.Attributes.Add("data-toggle", "modal");
                output.Attributes.Add("data-target", "#imageModal");
                output.Attributes.Add("style", "cursor:pointer;");
                output.Attributes.Add("onclick", $"$('#imageModal img').attr('src', '{ImageUrl}'); event.preventDefault();");

                output.Content.SetHtmlContent($"<img class=\"img-fluid\" style=\"max-height: 394px;\" src=\"{ImageUrl}\" alt=\"QR Code\">");
            }
        }
    }
}
