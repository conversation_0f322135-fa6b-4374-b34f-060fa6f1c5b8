﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using MediatR;

namespace Hca.WebHost.Pipeline
{
    public class MediatorIdentityPipeline<TReq, TResp> : IPipelineBehavior<TReq, TResp> where TReq : IRequest<TResp>
    {
        private readonly ViewManager _viewManager;

        public MediatorIdentityPipeline(ViewManager viewManager)
        {
            _viewManager = viewManager;
        }

        public Task<TResp> Handle(TReq request, CancellationToken cancellationToken, RequestHandlerDelegate<TResp> next)
        {
            if (request is IMessage<TResp> message)
            {
                message.CurrentUserId = _viewManager.UserId;
            }

            return next.Invoke();
        }
    }
}
