﻿using System;
using Dapper;

namespace Hca.Lib.Features.Inspections
{
    [Table("tblInspectionSamples")]
    public class InspectionSampleDto
    {
        public Guid Id { get; set; }

        public Guid InspectionId { get; set; }

        public string SampleReference { get; set; }

        public string SampleNotes { get; set; }

        public int ImageCount { get; set; }

        public string DefaultImageUrl { get; set; }

        public string Status { get; set; }
        public Guid? FloorPlanColour { get; set; }
        public Guid Floor { get; set; }
        public string FloorActual { get; set; }
        public string FloorPlanReference { get; set; }
        public string RoomUsage { get; set; }
        public string Location { get; set; }
        public Guid? Material { get; set; }
        public string MaterialActual { get; set; }
        public string MaterialColour { get; set; }
        public string Quantity { get; set; }
        public Guid? QuantityUnit { get; set; }
        public int? ProductType { get; set; }
        public int? ExtentOfDamage { get; set; }
        public int? SurfaceTreatment { get; set; }
        public int? AsbestosType { get; set; }
        public int? OccupantActivity { get; set; }
        public int? LikelihoodOfDisturbance { get; set; }
        public int? NumberOfOccupants { get; set; }
        public int? TypeOfMaintenance { get; set; }
        public int? Accessibility { get; set; }
        public Guid? ManagementRecommendation { get; set; }
        public Guid? Priority { get; set; }
    }
}
