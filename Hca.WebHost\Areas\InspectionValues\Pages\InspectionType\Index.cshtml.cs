﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Templates;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.InspectionValues.Pages.InspectionType;

public class Index : InspectionValueListPage
{
    private readonly InspectionService _inspectionService;
    private readonly TemplateService _templateService;

    public Index(
            TemplateService templateService,
            InspectionService inspectionService,
            ValueListsService inspectionValuesService,
            IMediator mediator,
            ViewManager viewManager) : base(inspectionValuesService, viewManager, mediator)
    {
        _inspectionService = inspectionService;
        _templateService = templateService;
    }

    public override ValueListType ValueListType => ValueListType.InspectionType;

    public IEnumerable<TemplateDto> Templates { get; private set; }

    public override async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
    {
        Templates = (await _templateService.GetTemplatesAsync(TemplateType.Report, cancellationToken)).Items;

        return await base.OnGetAsync(cancellationToken);
    }

    public async Task<string> GetDefaultTemplateNameAsync(Guid inspectionTypeId)
    {
        var templateId = await _inspectionService.GetDefaultInspectionReportTemplate(inspectionTypeId, CancellationToken.None);
        if (!templateId.HasValue) return null;
        var template = Templates.Single(t => t.Id == templateId);
        return template.TemplateName;
    }
}
