﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class UpdateClientContact : ICommand
    {
        public UpdateClientContact(
            Guid clientId,
            ContactDto contact)
        {
            ClientId = clientId;
            Contact = contact;
        }

        public Guid ClientId { get; }
        public ContactDto Contact { get; }
    }

    public class EditContactHandler : DapperRequestHandler<UpdateClientContact, CommandResult>
    {
        public EditContactHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateClientContact request)
        {
            var existing = await db.GetListAsync<ContactDto>(
                $"WHERE {nameof(ContactDto.Email)}=@{nameof(request.Contact.Email)} AND {nameof(ContactDto.Id)}<>@{nameof(request.Contact.Id)}", 
                request.Contact);
            if (existing.Any()) return CommandResult.Fail("That email address already exists in the system");

            var contact = await db.GetAsync<ContactDto>(request.Contact.Id);
            if (contact.ClientId != request.ClientId) return CommandResult.Fail("Contact does not belong to this client");

            contact.FirstName = request.Contact.FirstName;
            contact.LastName = request.Contact.LastName;
            contact.Email = request.Contact.Email;
            contact.MobilePhone = request.Contact.MobilePhone;
            contact.OfficePhone = request.Contact.OfficePhone;
            contact.Position = request.Contact.Position;

            await db.UpdateAsync(contact);

            return CommandResult.Success();
        }
    }
}
