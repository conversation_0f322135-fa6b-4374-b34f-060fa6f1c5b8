﻿using System;
using System.Collections.Generic;
using System.Linq;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Documents.Publishing
{
    public class DocumentModel
    {
        public Guid DocumentId { get; set; }
        public string TemplateCode { get; set; }
        public IEnumerable<byte[]> TemplateFiles { get; set; }
        public IEnumerable<(string Tag, string Text)> Tags { get; set; }
        public IEnumerable<DocumentSection> Sections { get; set; }
        public IEnumerable<Func<CompiledDocument, CompiledDocument>> AdditionalTransforms { get; set; } = Enumerable.Empty<Func<CompiledDocument, CompiledDocument>>();
    }
}
