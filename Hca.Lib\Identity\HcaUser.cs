﻿using System;
using Hca.Lib.Features.Users;
using Microsoft.AspNetCore.Identity;

namespace Hca.Lib.Identity;

public class HcaUser : IdentityUser
{
    public HcaUser(Guid id, string username, UserRole role, string permissions, Guid? clientId = null) : base(username)
    {
        Id = id.ToString();
        Role = role;
        ClientId = clientId;
        SecurityStamp = Guid.NewGuid().ToString();
        Email = NormalizedEmail = NormalizedUserName = username;
        Permissions = permissions?.Split(',') ?? Array.Empty<string>();
    }

    public UserRole Role { get; }

    public Guid? ClientId { get; }

    public Guid UserId { get => Guid.Parse(Id); }

    public string[] Permissions { get; }
}
