﻿CREATE TABLE [dbo].[tblContacts] (
    [Id]          UNIQUEIDENTIFIER CONSTRAINT [DF_Contacts_Id] DEFAULT (newid()) NOT NULL,
    [Created]     DATETIME         CONSTRAINT [DF_Contacts_Created] DEFAULT (getdate()) NOT NULL,
    [ClientId]    UNIQUEIDENTIFIER NOT NULL,
    [AddressId]   UNIQUEIDENTIFIER NULL,
    [FirstName]   NVARCHAR (MAX)   NOT NULL,
    [LastName]    NVARCHAR (MAX)   NOT NULL,
    [MobilePhone] NVARCHAR (MAX)   NULL,
    [OfficePhone] NVARCHAR (MAX)   NULL,
    [Email]       NVARCHAR (MAX)   NULL,
    [Notes]       NVARCHAR (MAX)   NULL,
    [Password] NVARCHAR(MAX) NULL, 
    [Position] NVARCHAR(MAX) NULL, 
    CONSTRAINT [PK_Contacts] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Contacts_Addresses] FOREIG<PERSON> KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id]),
    CONSTRAINT [FK_Contacts_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id])
);

