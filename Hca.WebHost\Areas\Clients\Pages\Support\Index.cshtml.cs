using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Services;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Clients.Pages.Support;

public class IndexModel : PageModel
{
    private readonly ViewManager _viewManager;
    private readonly IMediator _mediator;

    public IndexModel(IMediator mediator, ViewManager viewManager)
    {
        _mediator = mediator;
        _viewManager = viewManager;
    }

    [BindProperty] public string Message { get; set; }

    public bool SendEmailSuccess { get; private set; }

    public bool IsAdminUser { get; private set; }

    public async Task OnGetAsync(CancellationToken cancellationToken)
    {
        IsAdminUser = await _viewManager.IsAdminUser;
    }

    public async Task OnPostAsync(CancellationToken cancellationToken)
    {
        // identity user ID relates to contact for client users and user for admin users
        if (await _viewManager.IsClientUser)
        {
            var contact = await _mediator.Send(new GetClientContact(_viewManager.UserClientId, _viewManager.UserId), cancellationToken);
            var newLine = "<br>";

            var message = $"Support request from {contact.FirstName} {contact.LastName} ({contact.Email}){newLine}" +
                $"Position: {contact.Position}{newLine}" +
                $"Phone: {string.Join(" / ", contact.OfficePhone, contact.MobilePhone)}{newLine}" +
                $"{newLine}{Message}";

            var request = new SendEmail(
                "<EMAIL>",
                $"Support request from {contact.FirstName} {contact.LastName}",
                message,
                contact.Email);

            await _mediator.Send(request, cancellationToken);

            SendEmailSuccess = true;
        }
    }
}
