﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewSitePlan : IQuery<ValidationResult>
{
    public CanContactViewSitePlan(Guid sitePlanId, Guid contactId)
    {
        SitePlanId = sitePlanId;
        ContactId = contactId;
    }

    public Guid SitePlanId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewSitePlanHandler : DapperRequestHandler<CanContactViewSitePlan, ValidationResult>
{
    public CanContactViewSitePlanHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewSitePlan request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.SitePlans} " +
            $"JOIN {TableNames.Sites} ON {TableNames.Sites}.{nameof(SiteDto.Id)} = {TableNames.SitePlans}.{nameof(SitePlanDto.SiteId)} " +
            $"JOIN {TableNames.Contacts} ON {TableNames.Contacts}.{nameof(ContactDto.ClientId)} = {TableNames.Sites}.{nameof(SiteDto.ClientId)} " +
            $"WHERE {TableNames.SitePlans}.{nameof(SitePlanDto.Id)} = @{nameof(request.SitePlanId)} " +
            $"AND {TableNames.Contacts}.{nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}