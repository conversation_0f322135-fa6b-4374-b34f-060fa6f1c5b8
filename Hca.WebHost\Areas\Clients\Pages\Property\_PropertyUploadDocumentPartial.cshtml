﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">ADD PROPERTY DOCUMENT</small>
    </div>
    <form method="post" enctype="multipart/form-data">
        <div class="card-body">

            <div class="form-group row">
                <label class="col-xl-2 col-form-label" for="UploadFile">Upload a Document</label>
                <div class="col-xl-10">
                    <div id="file-drop-container"></div>
                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                </div>
            </div>

            <script>
                $(() => {
                    initializeFileDragAndDrop({
                        selector: '#file-drop-container',
                        inputName: '@Html.NameFor(m => m.UploadFile)',
                        onFilesChanged: (files) => {
                            if (files && files[0]) $('#divDate').show();
                        }
                    });
                });
            </script>

            <div class="form-group row mb-2" id="divDate" style="display: none;">
                <label class="col-md-2 col-form-label mb-2">Document Date</label>
                <div class="col-xl-6 col-10">
                    <div class="input-group date" id="documentDatePicker">
                        <input class="form-control" type="text" asp-for="Document.DocumentDate" />
                        <span class="input-group-append input-group-addon">
                            <span class="input-group-text fas fa-calendar-alt"></span>
                        </span>
                    </div>
                </div>
            </div>

            <script>
                $("#Document_DocumentDate").change(function (e) {
                    if ($(e.target).val() > '') {
                        // $(e.target).prop('disabled', true);
                        $('#divDocType').show();
                    }
                });
            </script>

            <select asp-id="divDocType"
                    asp-style="display: none;"
                    row-label="Document Type"
                    asp-for="Document.PropertyDocumentType"
                    asp-items="Html.GetEnumSelectList<Hca.Lib.Features.Clients.PropertyDocumentType>()"></select>

            <script>
                $("#Document_PropertyDocumentType").change(function (e) {
                    if ($(e.target).val() > '') {
                        //  $(e.target).prop('disabled', true);
                        $('#divCompany').show();
                    }
                });
            </script>

            <input type="text"
                   asp-for="Document.CompanyName"
                   row-label="Company Name"
                   asp-id="divCompany"
                   asp-style="display: none;"
                   asp-single-row="true" />

            <script>
                $("#Document_CompanyName").keyup(function (e) {
                    if ($(e.target).val() > '') {
                        $('#divNotes').show();

                        var docType = $("#Document_PropertyDocumentType").val();

                        if (["110", "120", "130", "140", "145", "150", "160", "170"].includes(docType)) {
                            $('#divAcmsDiscovered').show();
                        } else {
                            $('#divFooter').show();
                        }
                    }
                });
            </script>

            <textarea asp-for="Document.Notes"
                      row-label="Notes"
                      asp-id="divNotes"
                      asp-style="display: none;"
                      asp-single-row="true"
                      placeholder="If the inspection relates to a specific part of the property only, please provide information here. Anything else of significance is also to be included here." />

            <div class="form-group row mb-2" id="divAcmsDiscovered" style="display: none;">
                <div class="col-xl-6 col-10">
                    <label class="form-check-label" for="Document_AcmsDiscovered">Have asbestos containing materials been identified/presumed?</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="Document_AcmsDiscovered" id="Document_AcmsDiscovered_Yes" value="true" />
                        <label class="form-check-label" for="Document_AcmsDiscovered_Yes">Yes</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="Document_AcmsDiscovered" id="Document_AcmsDiscovered_No" value="false" />
                        <label class="form-check-label" for="Document_AcmsDiscovered_No">No</label>
                    </div>
                </div>
            </div>

            <script>
                $("#Document_AcmsDiscovered_Yes, #Document_AcmsDiscovered_No").change(function (e) {
                    var acmsDiscovered = $(e.target).val();

                    if (acmsDiscovered === "true") {
                        $('#divInspectionDate').show();
                        $('#divFooter').hide();
                    } else {
                        $('#divInspectionDate').hide();
                        $('#divFooter').show();
                    }
                });
            </script>

            <div class="form-group row mb-2" id="divInspectionDate" style="display: none;">
                <label class="col-md-2 col-form-label mb-2">Next Inspection Date</label>
                <div class="col-xl-6 col-10">
                    <div class="input-group date" id="nextInspectionDatePicker">
                        <input class="form-control" type="text" asp-for="Document.NextInspectionDate" />
                        <span class="input-group-append input-group-addon">
                            <span class="input-group-text fas fa-calendar-alt"></span>
                        </span>
                    </div>
                </div>
            </div>

            <script>
                $("#Document_NextInspectionDate").change(function (e) {
                    if ($(e.target).val() > '') {
                        $('#divFooter').show();
                    }
                });
            </script>

        </div>
        <save-cancel-footer id="divFooter" style="display: none;" CancelUrl="@Urls.ClientPropertyDocuments(Model.Client.Id, Model.Property.Id)"></save-cancel-footer>
    </form>
</div>
