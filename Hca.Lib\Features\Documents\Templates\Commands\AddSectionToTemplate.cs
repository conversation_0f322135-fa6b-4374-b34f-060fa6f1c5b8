﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Commands
{
    public class AddSectionToTemplate : TemplateCommand
    {
        public AddSectionToTemplate(
            Guid templateId,
            Guid sectionId,
            string sectionCode,
            string sectionTitle) : base(templateId)
        {
            SectionId = sectionId;
            SectionCode = sectionCode;
            SectionTitle = sectionTitle;
        }

        public Guid SectionId { get; }
        public string SectionCode { get; }
        public string SectionTitle { get; }
    }

    public class AddSectionToTemplateHandler : DapperRequestHandler<AddSectionToTemplate, CommandResult>
    {
        public AddSectionToTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddSectionToTemplate request)
        {
            var sectionCount = await db.ExecuteScalarAsync<int>(
                "SELECT COUNT(1) FROM tblTemplateSections WHERE TemplateId=@TemplateId",
                new { request.TemplateId });

            await db.InsertAsync(new TemplateSectionDto
            {
                Id = request.SectionId,
                TemplateId = request.TemplateId,
                SectionOrder = sectionCount + 1,
                SectionTitle = request.SectionTitle,
                SectionCode = request.SectionCode,
            });

            return CommandResult.Success();
        }
    }
}
