﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class DeleteSite : ICommand
    {
        public DeleteSite(Guid clientId, Guid siteId, DateTime? deleted = null)
        {
            ClientId = clientId;
            SiteId = siteId;
            Deleted = deleted ?? DateTime.UtcNow;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
        public DateTime? Deleted { get; }
    }

    public class DeleteSiteHandler : DapperRequestHandler<DeleteSite, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;
        public DeleteSiteHandler(IDbHelper dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteSite request)
        {
            var site = await db.GetAsync<SiteDto>(request.SiteId);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET {nameof(SiteDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(SiteDto.Id)}=@{nameof(request.SiteId)}", request);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(PropertyDto.SiteId)}=@{nameof(request.SiteId)} " +
                $"AND {nameof(PropertyDto.Deleted)} IS NULL", request);

            _clientCountsService.ClearClientCountsAsync(site.ClientId);

            return CommandResult.Success();
        }
    }
}
