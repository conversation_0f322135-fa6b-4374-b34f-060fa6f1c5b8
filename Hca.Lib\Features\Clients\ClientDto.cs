﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using Dapper;

    [Table("tblClients")]
    public class ClientDto
    {
        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        public Guid? AddressId { get; set; } // Head office

        public string BuildingName { get; set; }
        public string BuildingNumber { get; set; }
        public string Unit { get; set; }
        public string Floor { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string ClientName { get; set; }

        public string UrlSafeClientName { get; set; }

        public string LogoUrl { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public ClientType ClientType { get; set; }

        public string EmergencyContactDetails { get; set; }

        public string EscalationProcedure { get; set; }

        public string KpisAndSlas { get; set; }

        [System.ComponentModel.DataAnnotations.EmailAddress]
        public string InvoiceEmailAddress { get; set; }

        [System.ComponentModel.DataAnnotations.EmailAddress]
        public string AccountQueriesEmailAddress { get; set; }

        public string GeneralRequirements { get; set; }

        public DateTime? Archived { get; set; }

        public DateTime? Deleted { get; set; }

        public string ArchiveReason { get; set; }
    }
}
