﻿using Hca.WebHost.Pipeline;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers.NavMenu
{
    [HtmlTargetElement("nav-archive")]
    public class NavArchive : TagHelper
    {
        private readonly IHtmlGenerator _html;
        private readonly ViewManager _viewManager;

        public NavArchive(IHtmlGenerator html, ViewManager viewManager) : base()
        {
            _html = html;
            _viewManager = viewManager;
        }

        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("item-name")]
        public string ItemName { get; set; }

        [HtmlAttributeName("text")]
        public string Text { get; set; }

        [HtmlAttributeName("display")]
        public string Display { get; set; } = "inline";

        [HtmlAttributeName("buttonClass")]
        public string ButtonClass { get; set; } = "nav-link";

        [ViewContext]
        [HtmlAttributeNotBound]
        public ViewContext ViewContext { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            if (!_viewManager.UserCanDelete.Result)
            {
                output.TagName = "span";
                output.Attributes.SetAttribute("style", "display: none");
                return;
            }

            output.TagName = "form";
            output.Attributes.Add("method", "post");
            output.Attributes.Add("style", $"display: {Display};");
            output.Attributes.Add("action", Action);

            output.PreContent.SetHtmlContent(
                _html.GenerateAntiforgery(ViewContext).ToHtmlString() +
                $"<button class=\"{ButtonClass} d-flex btn {NavButtonType.Archive.ClassName()}\" style=\"width: 100%; margin: 5px 0;\" " +
                $"data-archive-dialog=\"Are you sure you wish to archive {ItemName}?\" data-archive-field=\"{Constants.ArchiveReasonFormName}\">");

            output.Content.SetContent(Text ?? "Archive");

            output.PostContent.SetHtmlContent("</button>");
        }
    }
}
