﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Exceptions;
using MediatR;

namespace Hca.Lib.Core
{
    public class CommandSetHandler<TAgg> : IRequestHandler<CommandSet<TAgg>, CommandResult> where TAgg : IAggregate
    {
        private readonly IAggregateRepository<TAgg> _repo;

        public CommandSetHandler(IAggregateRepository<TAgg> repo)
        {
            _repo = repo;
        }

        // todo: sort this mess out, ensure single and multiple commands use the same logic
        // todo: allow multiple aggregates by caching aggregate instances locally during the set execution so commands of all types can be executed at once
        // todo: perhaps single commands are just a single instance of a command set and there is only one set of logic?
        public async Task<CommandResult> Handle(CommandSet<TAgg> request, CancellationToken cancellationToken)
        {
            var id = new EntityId(request.AggregateId);
            TAgg agg;

            try
            {
                agg = request.Commands.First() is AggregateCreateCommand<TAgg>
                    ? (TAgg)Activator.CreateInstance(typeof(TAgg), id)
                    : await _repo.LoadByIdAsync(id);
            }
            catch (Exception ex)
            {
                throw new AggregateNotFoundException($"Failed to load {typeof(TAgg)} with id {request.AggregateId}", ex);
            }

            foreach (var command in request.Commands)
            {
                if (command.AggregateId != request.AggregateId)
                {
                    return CommandResult.Fail();
                }

                try
                {
                    agg.Execute((dynamic)command);
                }
                catch (Exception ex)
                {
                    throw new CommandExecutionException($"Failed to change {typeof(TAgg)} with id {request.AggregateId}", ex);
                }
            }

            try
            {
                await _repo.SaveAsync(agg);
            }
            catch (Exception ex)
            {
                throw new AggregateSaveException($"Failed to save {typeof(TAgg)} with id {request.AggregateId}", ex);
            }

            return CommandResult.Success();
        }
    }
}
