﻿using System.Threading.Tasks;

namespace Hca.Lib.Core
{
    public interface IRepository<T>
        where T : IEntity
    {
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        Task<T> LoadByIdAsync(EntityId id);
    }

    public interface IAggregateRepository<T>
        : IRepository<T>
        where T : IAggregate
    {
        Task SaveAsync(T aggregate);
    }
}
