﻿using Hca.Lib.Core.Queries;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Razor.TagHelpers;
using System;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("pagedtable")]
    public class TableTagHelper : TagHelper
    {
        [HtmlAttributeName("paged-dto-set")]
        public PagedDtoSet<object> PagedDtoSet { get; set; }

        [HtmlAttributeName("item-template")]
        public Func<object, HelperResult> ItemRowTemplate { get; set; }

        [HtmlAttributeName("header-template")]
        public string HeaderRowTemplate { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            // Generate table markup
            output.TagName = "table";
            output.TagMode = TagMode.StartTagAndEndTag;

            // Generate table header
            var header = new TagBuilder("thead");
            header.InnerHtml.AppendHtml(HeaderRowTemplate);
            output.Content.AppendHtml(header);

            // Generate table body
            var body = new TagBuilder("tbody");
            foreach (var item in PagedDtoSet.Items)
            {
                var row = new TagBuilder("tr");
                row.InnerHtml.AppendHtml(ItemRowTemplate(item));
                body.InnerHtml.AppendHtml(row);
            }
            output.Content.AppendHtml(body);
        }
    }
}
