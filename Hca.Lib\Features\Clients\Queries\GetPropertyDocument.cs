﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetPropertyDocument : IQuery<PropertyDocumentDto>
    {
        public GetPropertyDocument(Guid clientId, Guid propertyId, Guid documentId)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            DocumentId = documentId;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public Guid DocumentId { get; }
    }

    public class GetPropertyDocumentHandler : DapperRequestHandler<GetPropertyDocument, PropertyDocumentDto>
    {
        public GetPropertyDocumentHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PropertyDocumentDto> OnHandleAsync(IDbHelper db, GetPropertyDocument request)
        {
            var dto = await db.GetAsync<PropertyDocumentDto>(
                $"WHERE {nameof(PropertyDocumentDto.PropertyId)} = @{nameof(GetPropertyDocument.PropertyId)} " +
                $"AND {nameof(PropertyDocumentDto.Id)} = @{nameof(GetPropertyDocument.DocumentId)}",
                request);

            return dto;
        }
    }
}
