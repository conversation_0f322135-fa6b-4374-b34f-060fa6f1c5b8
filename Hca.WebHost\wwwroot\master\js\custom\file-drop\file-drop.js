﻿function initializeFileDragAndDrop(options) {
    const { selector, inputName, accept = '*', onFilesChanged = (files) => { } } = options;
    const fileDropContainer = $(selector);

    // Dynamically create and append the markup with a specific input name
    fileDropContainer.html(`
            <div class="file-drop-area">
                <div class="drop-message">Select a file to upload or drag and drop it here</div>
                <div class="upload-icon"></div>
                <div class="file-drop-spinner" style="display: none;"></div>
            </div>
            <input type="file" id="${inputName}" name="${inputName}" accept="${accept}" class="file-drop-input" style="display: none;"/>
        `);

    const fileDropArea = $(selector + ' .file-drop-area'); // fileDropContainer.find('.file-drop-area');
    const fileInput = $(selector + ' .file-drop-input'); // fileDropContainer.find('.file-drop-input');
    const spinner = $(selector + ' .file-drop-spinner'); // fileDropContainer.find('.file-drop-spinner');

    function reset() {
        spinner.hide();
        fileInput.val(''); // Clear the input
    }

    fileDropArea.on('dragover', function (e) {
        e.preventDefault();
        fileDropArea.addClass('drag-over');
    });

    fileDropArea.on('dragleave', function (e) {
        e.preventDefault();
        fileDropArea.removeClass('drag-over');
    });

    fileDropArea.on('drop', function (e) {
        e.preventDefault();
        fileDropArea.removeClass('drag-over');
        const files = e.originalEvent.dataTransfer.files;
        fileInput[0].files = files; 
        handleFileChange(files);
    });

    fileInput.on('change', function () {
        handleFileChange(this.files);
    });

    fileDropArea.on('click', function () {
        fileInput.click();
    });

    function handleFileChange(files) {
        if (files.length > 0) {
            spinner.text('File selected: ' + files[0].name); // Set spinner text to 'File selected'
            spinner.show();
            onFilesChanged(files);
            console.log("File selected:", files[0]);
        }
    }

    // Expose reset function if needed
    window.resetUpload = reset;
}