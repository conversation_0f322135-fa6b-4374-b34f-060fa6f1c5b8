﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Queries
{
    public class FindDocuments : IQueryMany<DocumentDto>
    {
        public FindDocuments(Guid? inspectionId, DocumentStatus? status)
        {
            InspectionId = inspectionId;
            Statuses = status.HasValue ? new[] { status.Value } : Array.Empty<DocumentStatus>();
        }

        public FindDocuments(Guid? inspectionId, DocumentStatus[] statuses = null)
        {
            InspectionId = inspectionId;
            Statuses = statuses ?? Array.Empty<DocumentStatus>();
        }

        public Guid? InspectionId { get; }
        public DocumentStatus[] Statuses { get; }
    }

    public class FindDocumentHandler : DapperRequestHandler<FindDocuments, DtoSet<DocumentDto>>
    {
        public FindDocumentHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<DocumentDto>> OnHandleAsync(IDbHelper db, FindDocuments request)
        {
            var whereClause = "WHERE 1=1";
            if (request.InspectionId.HasValue) whereClause += " AND InspectionId = @InspectionId";
            if (request.Statuses.Any()) whereClause += " AND DocumentStatus IN @Statuses";

            return DtoSet.From(await db.GetListAsync<DocumentDto>(
                whereClause,
                new { request.InspectionId, Statuses = request.Statuses.Select(s => (int)s) }));
        }
    }
}
