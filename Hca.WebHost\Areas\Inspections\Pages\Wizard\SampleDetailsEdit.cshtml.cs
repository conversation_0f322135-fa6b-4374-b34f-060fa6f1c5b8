﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

[IgnoreAntiforgeryToken(Order = 1001)]
public class SampleDetailsEditModel : HcaPageModel
{
    public SampleDetailsEditModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public Guid InspectionId { get; private set; }
    public InspectionSampleDto Sample { get; private set; }

    public async Task OnGetAsync(
        Guid inspectionId,
        Guid sampleId,
        CancellationToken cancellationToken)
    {
        InspectionId = inspectionId;
        Sample = await _mediator.Send(new GetInspectionSampleById(sampleId), cancellationToken);
    }

    public async Task<IActionResult> OnPostSampleReferenceAsync(
        Guid inspectionId,
        Guid sampleId,
        [FromBody] string sampleReference,
        CancellationToken cancellationToken)
    {
        await _mediator.Send(
            new SetInspectionSampleReference(
                inspectionId,
                sampleId,
                sampleReference),
            cancellationToken);

        return new OkResult();
    }

    public async Task<IActionResult> OnPostSampleNotesAsync(
        Guid inspectionId,
        Guid sampleId,
        [FromBody] string sampleNotes,
        CancellationToken cancellationToken)
    {
        await _mediator.Send(
            new SetInspectionSampleNotes(
                inspectionId,
                sampleId,
                sampleNotes),
            cancellationToken);

        return new OkResult();
    }
}
