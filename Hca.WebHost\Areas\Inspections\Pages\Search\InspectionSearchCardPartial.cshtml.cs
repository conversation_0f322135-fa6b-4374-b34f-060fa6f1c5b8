﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Search;

public class InspectionSearchCardPartialModel : HcaPageModel
{
    public InspectionSearchCardPartialModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task OnGetAsync([FromQuery]string title = "Search Results", CancellationToken cancellationToken = default)
    {
        Title = title;
        Inspections = await _mediator.Send(new GetInspections(), cancellationToken);
    }

    public string Title { get; private set; }
    public PagedDtoSet<InspectionWithDetailsDto> Inspections { get; private set; }
}
