﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Projects.Queries;

namespace Hca.Lib.Features.Projects.Documents.Publishing
{
    public class QuoteDocumentModel
    {
        public DocumentModel DocumentModel { get; set; }
        public QuoteDto Quote { get; set; }
        public ClientDto Client { get; set; }
        public ContactDto Contact { get; set; }
        public AddressDto HeadOfficeAddress { get; set; }
        public ContactDto ClientContact { get; set; }
        public DtoSet<QuotePropertyWithAddressDto> Properties { get; set; }
    }
}
