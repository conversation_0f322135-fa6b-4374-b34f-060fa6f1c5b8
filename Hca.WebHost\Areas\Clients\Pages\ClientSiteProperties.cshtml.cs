﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSitePropertiesModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSitePropertiesModel(
        ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    [BindProperty]
    public SiteDtoExtended Site { get; set; }
    public IEnumerable<PropertyDtoExtended> Properties { get; private set; }
    public string Id { get; private set; }
    public ClientDto Client { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (Guid.TryParse(id, out var siteId))
        {
            if (!await IsHcaUser)
            {
                var valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
                if (!valid.IsValid) return Forbid();
            }

            Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);
            Properties = (await _clientService.GetPropertiesAsync(clientId, siteId, cancellationToken)).Items;
        }

        return Page();
    }
}
