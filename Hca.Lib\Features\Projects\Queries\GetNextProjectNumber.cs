﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Config.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Queries
{
    public class GetNextProjectNumber : IQuery<ProjectNumber> { }

    public class GetNextProjectNumberHandler : IRequestHandler<GetNextProjectNumber, ProjectNumber>
    {
        private readonly IMediator _mediator;

        public GetNextProjectNumberHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<ProjectNumber> Handle(GetNextProjectNumber request, CancellationToken cancellationToken) =>
            _mediator.Send(new GetIncrementConfig("ProjectNumber")).ContinueWith(c => new ProjectNumber(c.Result));
    }
}
