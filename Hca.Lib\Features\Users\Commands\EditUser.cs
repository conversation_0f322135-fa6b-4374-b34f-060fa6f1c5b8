﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Commands;

public class EditUser : ICommand
{
    public EditUser(UserDto user)
    {
        User = user;
    }

    public UserDto User { get; }
}

public class EditUserHandler : DapperRequestHandler<EditUser, CommandResult>
{
    public EditUserHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EditUser request)
    {
        if (request.User.Id == Guid.Empty) return CommandResult.Fail("User ID cannot be empty.");

        await db.UpdateAsync(request.User);

        return CommandResult.Success();
    }
}
