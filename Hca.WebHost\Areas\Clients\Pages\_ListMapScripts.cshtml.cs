﻿using System;
using System.Collections.Generic;
using System.Linq;
using Hca.Lib.Features.Clients;

namespace Hca.WebHost.Areas.Clients.Pages;

public class _ListMapScriptsModel
{
    public _ListMapScriptsModel(ClientDto client, IEnumerable<PropertyDtoExtended> properties)
    {
        Client = client;
        MapModels = properties.Select(p => new MapModel
        {
            DisplayText = p.GetDisplayText(),
            Lat = p.Address.Lat,
            Lon = p.Address.Lon,
            DirectUrl = Urls.ClientProperty(Client.Id, p.Id),
            NextInspection = p.NextInspection,
        });
    }

    public _ListMapScriptsModel(ClientDto client, IEnumerable<SiteDtoExtended> sites)
    {
        Client = client;
        MapModels = sites.Select(s => new MapModel
        {
            DisplayText = s.GetDisplayText(),
            Lat = s.Address.Lat,
            Lon = s.Address.Lon,
            DirectUrl = Urls.ClientSite(Client.Id, s.Id),
            PropertyCount = s.PropertyCount,
        });
    }

    public string PointerColour { get; set; }

    public bool ShowMapNumbers { get; set; } = false;

    public ClientDto Client { get; }

    public IEnumerable<MapModel> MapModels { get; }

    public class MapModel
    {
        public string DisplayText { get; set; }

        public double? Lat { get; set; }

        public double? Lon { get; set; }

        public string DirectUrl { get; set; }

        public DateTime? NextInspection { get; set; }

        public int PropertyCount { get; set; } = 1;
    }
}

