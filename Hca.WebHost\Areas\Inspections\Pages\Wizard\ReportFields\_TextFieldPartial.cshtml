﻿@model Hca.Lib.Features.Documents.Fields.DocumentTextField

    <link href="~/Vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />

    <script src="~/Vendor/x-editable/dist/bootstrap3-editable/js/bootstrap-editable.js"></script>

    <p><strong>HINT</strong>&nbsp;&nbsp;@Model.Value.Hint</p>

    <div id="divActual-@(Model.DocumentFieldId)"
         class="form-control textarea"
         style="height: 50px;"
         contenteditable="true">
        @Html.Raw(Model.Value.Actual ?? Model.Value.Placeholder)
    </div>
    <input type="hidden" name="actual" id="hidActual-@(Model.DocumentFieldId)" value="@(Model.Value.Actual ?? Model.Value.Placeholder)" />

    @*<textarea class="form-control" name="actual" rows="3">@(Model.Actual ?? Model.Placeholder)</textarea>*@

    <script>
    (() => {
        $('#divActual-@(Model.DocumentFieldId)').on("input", function () {
            $('#hidActual-@(Model.DocumentFieldId)').val(this.innerText);
        });
    })();
    </script>