﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries;
using MediatR;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class SetSiteAddress : ICommand
    {
        public SetSiteAddress(
            Guid clientId,
            Guid siteId,
            string street,
            string town,
            string city,
            string county,
            string country,
            string postcode,
            double? lat,
            double? lon)
        {
            ClientId = clientId;
            SiteId = siteId;
            Street = street;
            Town = town;
            City = city;
            County = county;
            Country = country;
            Postcode = postcode;
            Lat = lat;
            Lon = lon;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }

        public string Street { get; }
        public string Town { get; }
        public string City { get; }
        public string County { get; }
        public string Country { get; }
        public string Postcode { get; }
        public double? Lat { get; }
        public double? Lon { get; }
    }

    public class SetSiteAddressHandler : DapperRequestHandler<SetSiteAddress, CommandResult>
    {
        private readonly IMediator _mediator;
        public SetSiteAddressHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetSiteAddress request)
        {
            var siteDto = await _mediator.Send(new GetClientSite(request.ClientId, request.SiteId));
            AddressDto addressDto;

            if (siteDto.AddressId.HasValue)
            {
                addressDto = await db.GetAsync<AddressDto>(siteDto.AddressId.Value);
            }
            else
            {
                addressDto = new AddressDto { Id = Guid.NewGuid() };
            }

            addressDto.StreetName = request.Street;
            addressDto.Town = request.Town;
            addressDto.City = request.City;
            addressDto.County = request.County;
            addressDto.Country = request.Country;
            addressDto.Postcode = request.Postcode;
            addressDto.Lat = request.Lat;
            addressDto.Lon = request.Lon;

            if (siteDto.AddressId.HasValue)
            {
                await db.UpdateAsync(addressDto);
            }
            else
            {
                await db.InsertAsync(addressDto);

                siteDto.AddressId = addressDto.Id;
                await db.UpdateAsync(siteDto);
            }

            return CommandResult.Success();
        }
    }
}
