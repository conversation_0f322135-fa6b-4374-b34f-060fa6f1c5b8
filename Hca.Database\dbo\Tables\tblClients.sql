﻿CREATE TABLE [dbo].[tblClients] (
    [Id]                         UNIQUEIDENTIFIER CONSTRAINT [DF_Clients_Id] DEFAULT (newid()) NOT NULL,
    [Created]                    DATETIME         CONSTRAINT [DF_Clients_Created] DEFAULT (getdate()) NOT NULL,
    [AddressId]                  UNIQUEIDENTIFIER NULL,
    [ClientName]                 NVARCHAR (MAX)   NOT NULL,
    [LogoUrl]                    NVARCHAR (MAX)   NULL,
    [ClientType]                 INT              DEFAULT ((2)) NOT NULL,
    [EmergencyContactDetails]    NVARCHAR (MAX)   NULL,
    [EscalationProcedure]        NVARCHAR (MAX)   NULL,
    [<PERSON><PERSON><PERSON>ndSlas]                NVARCHAR (MAX)   NULL,
    [InvoiceEmailAddress]        NVARCHAR (MAX)   NULL,
    [AccountQueriesEmailAddress] NVARCHAR (MAX)   NULL,
    [GeneralRequirements]        NVARCHAR (MAX)   NULL,
    [BuildingName]               NVARCHAR (MAX)   NULL,
    [BuildingNumber]             NVARCHAR (MAX)   NULL,
    [Unit]                       NVARCHAR (MAX)   NULL,
    [Floor]                      NVARCHAR (MAX)   NULL,
    [UrlSafeClientName] NVARCHAR(MAX) NULL, 
    [Archived] DATETIME NULL,
    [Deleted] DATETIME NULL,
    [ArchiveReason] NVARCHAR(MAX) NULL, 
    CONSTRAINT [PK_Clients] PRIMARY KEY CLUSTERED ([Id] ASC)
);

