﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Hca.Lib.Core.Queries;

public class DtoSet
{
    public static DtoSet<X> From<X>(IEnumerable<X> items) where X : class, new()
    {
        return new DtoSet<X> { Items = items.ToList() };
    }
}

public class DtoSet<T> where T : class, new()
{
    public IEnumerable<T> Items { get; internal set; }
}

public class PagedDtoSet
{
    public static PagedDtoSet<X> From<X>(
        IEnumerable<X> items,
        int currentPage,
        int itemsPerPage,
        int totalItems) where X : class, new()
    {
        return new PagedDtoSet<X>
        {
            Items = items,
            CurrentPage = currentPage,
            ItemsPerPage = itemsPerPage,
            TotalItems = totalItems,
            TotalPages = (int)Math.Round(totalItems * 1.0 / itemsPerPage, 0, MidpointRounding.ToPositiveInfinity)
        };
    }
}

public class PagedDtoSet<T> : DtoSet<T> where T : class, new()
{
    public int CurrentPage { get; internal set; }

    public int TotalPages { get; internal set; }

    public int TotalItems { get; internal set; }

    public int ItemsPerPage { get; internal set; }
}
