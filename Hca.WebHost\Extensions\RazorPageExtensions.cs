﻿using System;
using System.Text;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Razor;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Hca.WebHost
{
    public static class ViewPageExtensions
    {
        private const string SCRIPTBLOCK_BUILDER = "ScriptBlockBuilder";

        public static HtmlString ScriptBlock(
            this RazorPage webPage,
            Func<dynamic, HelperResult> template)
        {
            if (!webPage.Context.IsAjaxRequest())
            {
                var scriptBuilder = webPage.Context.Items[SCRIPTBLOCK_BUILDER]
                                    as StringBuilder ?? new StringBuilder();

                scriptBuilder.Append(template(null).ToHtmlString());

                webPage.Context.Items[SCRIPTBLOCK_BUILDER] = scriptBuilder;

                return new HtmlString(string.Empty);
            }
            return new HtmlString(template(null).ToHtmlString());
        }

        public static HtmlString WriteScriptBlocks(this PageModel webPage)
        {
            var scriptBuilder = webPage.HttpContext.Items[SCRIPTBLOCK_BUILDER]
                                as StringBuilder ?? new StringBuilder();

            return new HtmlString(scriptBuilder.ToString());
        }
    }
}
