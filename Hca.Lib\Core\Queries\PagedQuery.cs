﻿using MediatR;

namespace Hca.Lib.Core.Queries;

public abstract class PagedQuery<T> : IRequest<PagedDtoSet<T>> where T : class, new()
{
    public int Page { get; }

    public int PageSize { get; }

    public PagedQuery(int? page = 1, int? pageSize = 20)
    {
        Page = page ?? 1;
        PageSize = pageSize ?? 20;
    }
    public int Skip => (Page - 1) * PageSize;

    public int Take => PageSize; 
}
