﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Commands;
using Hca.Lib.Features.Documents.Queries;
using Hca.Lib.Features.Projects.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Documents.Commands
{
    public class CreateQuoteDocument : ICommand<Guid>
    {
        public CreateQuoteDocument(
            Guid quoteId,
            string quoteBody)
        {
            QuoteId = quoteId;
            QuoteBody = quoteBody;
        }

        public Guid QuoteId { get; }
        public string QuoteBody { get; }
    }

    public class CreateQuoteDocumentHandler : DapperRequestHandler<CreateQuoteDocument, CommandResult<Guid>>
    {
        private readonly IMediator _mediator;

        public CreateQuoteDocumentHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult<Guid>> OnHandleAsync(IDbHelper db, CreateQuoteDocument request)
        {
            var documentId = Guid.NewGuid();

            var quoteTemplate = await _mediator.Send(new GetQuoteTemplate());

            await _mediator.Send(new CreateDocument(documentId, quoteTemplate));

            await db.InsertAsync(new QuoteDocumentDto
            {
                Id = Guid.NewGuid(),
                DocumentId = documentId,
                QuoteId = request.QuoteId,
            });

            var document = await _mediator.Send(new GetDocumentObject(documentId));

            await _mediator.Send(new SetDocumentTextField(
                documentId,
                document.DocumentSections.Single().DocumentSectionId,
                document.DocumentSections.Single().DocumentFields.Single().DocumentFieldId,
                request.QuoteBody));

            return CommandResult<Guid>.Success(documentId);
        }
    }
}
