﻿using System.Collections.Generic;
using System.IO;
using System.Linq;
using OpenXmlPowerTools;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class DocumentCombiner
    {
        public static CompiledDocument Combine(IEnumerable<byte[]> documents)
        {
            if (documents.Count() == 0) return null;

            //var reportDocument = new ReportDocument(documents.First());

            //if (documents.Count() > 1)
            //{
            //    foreach(var doc in documents.Skip(1))
            //    {

            //    }
            //}

            if (documents.Count() == 1) return new CompiledDocument(documents.First());

            // todo: BELOW DOES NOT WORK

            var sources = new List<Source>();

            foreach (var doc in documents)
            {
                var tempms = new MemoryStream(doc);
                tempms.Seek(0, SeekOrigin.Begin);
                sources.Add(new Source(new WmlDocument(doc.Length.ToString(), tempms), true));
            }

            var mergedDoc = DocumentBuilder.BuildDocument(sources);

            return new CompiledDocument(mergedDoc.DocumentByteArray);
        }
    }
}
