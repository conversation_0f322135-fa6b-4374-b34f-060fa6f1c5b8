﻿using System;
namespace Hca.Lib.Features.Documents.Fields
{
    public interface IDocumentField<out T> where T : Field
    {
        Guid DocumentFieldId { get; set; }
        bool IsComplete { get; set; }
        T Value { get; }
    }

    public abstract class DocumentField<T> : IDocumentField<T> where T : Field
    {
        internal DocumentField(Guid documentFieldId)
        {
            DocumentFieldId = documentFieldId;
        }

        public Guid DocumentFieldId { get; set; }

        public bool IsComplete { get; set; }

        public T Value { get; set; }
    }
}
