﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.InspectionValues.Models;

public abstract class InspectionValueEditPage : HcaPageModel
{
    private readonly ValueListsService _inspectionValuesService;

    public InspectionValueEditPage(
        ValueListsService inspectionValuesService,
        IMediator mediator,
        ViewManager viewManager) : base(mediator, viewManager)
    {
        _inspectionValuesService = inspectionValuesService;
    }

    public abstract ValueListType ValueListType { get; }
    public abstract string IndexUrl { get; }

    [BindProperty]
    public ValueDto Value { get; set; }

    public async virtual Task OnGetAsync(Guid id, CancellationToken cancellationToken)
    {
        Value = await _inspectionValuesService.GetAsync(id, cancellationToken);
    }

    public async virtual Task<IActionResult> OnPostAsync(Guid id, CancellationToken cancellationToken)
    {
        Value.Id = id;
        Value.ValueListType = ValueListType;
        await _inspectionValuesService.UpdateAsync(Value, cancellationToken);
        return Redirect(IndexUrl);
    }

    public async virtual Task<IActionResult> OnPostDeleteAsync(Guid id, CancellationToken cancellationToken)
    {
        await _inspectionValuesService.DeleteAsync(id, cancellationToken);
        return Redirect(IndexUrl);
    }
}
