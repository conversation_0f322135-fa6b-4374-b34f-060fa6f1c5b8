﻿namespace Hca
{
    public static class StringExtensions
    {
        public static bool IsNullOrWhiteSpace(this string str) => string.IsNullOrWhiteSpace(str);

        public static bool IsNullOrEmpty(this string str) => string.IsNullOrEmpty(str);

        public static bool IsNull(this string str) => str == null;

        public static bool IsPopulated(this string str) => !str.IsNullOrWhiteSpace();
    }
}
