﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Templates;
using MediatR;

namespace Hca.Lib.Features.Documents.Queries
{
    public class GetDocumentModel : IQuery<DocumentModel>
    {
        public GetDocumentModel(
            Guid documentId,
            TemplateObject documentTemplate = null)
        {
            DocumentId = documentId;
            DocumentTemplate = documentTemplate;
        }

        public Guid DocumentId { get; }
        public TemplateObject DocumentTemplate { get; }
    }

    public class GetDocumentModelHandler : DapperRequestHandler<GetDocumentModel, DocumentModel>
    {
        private readonly IMediator _mediator;

        public GetDocumentModelHandler(
            IDbHelper dbHelper,
            IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public async override Task<DocumentModel> OnHandleAsync(IDbHelper db, GetDocumentModel request)
        {
            var documentDto = await db.GetAsync<DocumentDto>(request.DocumentId);
            var templateFileDtos = request.DocumentTemplate?.Files;
            var templateId = request.DocumentTemplate?.Id;

            if (request.DocumentTemplate == null)
            {
                // todo: should this be stored against the document, or the whole file?
                var templateDto = await db.GetAsync<TemplateDto>("WHERE TemplateCode=@TemplateCode", new { documentDto.TemplateCode });
                templateId = templateDto.Id;
                templateFileDtos = await db.GetListAsync<TemplateFileDto>(
                    "WHERE TemplateId = @TemplateId " +
                    "ORDER BY DisplayOrder ASC",
                    new { templateDto.Id });
            }

            var templateFiles = templateFileDtos.Select(async f => await _mediator.Send(new DownloadBlob(
                    StorageConstants.TemplateContainerName,
                    $"{templateId}/{f.FileName}")));

            var documentSections = await db.GetListAsync<DocumentSectionDto>(
                "WHERE DocumentId = @DocumentId",
                request);
            var documentFields = await db.QueryAsync<DocumentFieldDto>(
                "SELECT tblDocumentFields.* FROM tblDocumentFields " +
                "JOIN tblDocumentSections ON DocumentSectionId = tblDocumentSections.Id " +
                "WHERE DocumentId = @DocumentId",
                request);

            var model = new DocumentModel
            {
                DocumentId = request.DocumentId,
                TemplateCode = documentDto.TemplateCode,
                TemplateFiles = templateFiles.Select(f => f.Result),
                Sections = BuildDocumentSections(documentSections, documentFields),
            };

            return model;
        }

        private IEnumerable<DocumentSection> BuildDocumentSections(
            IEnumerable<DocumentSectionDto> documentSectionDtos,
            IEnumerable<DocumentFieldDto> documentFieldDtos) =>
            documentSectionDtos.Select(sectionDto =>
                new DocumentSection
                {
                    DocumentSectionId = sectionDto.Id,
                    SectionCode = sectionDto.SectionCode,
                    SectionComplete = sectionDto.SectionComplete,
                    SectionOrder = sectionDto.SectionOrder,
                    SectionTitle = sectionDto.SectionTitle,
                    DocumentFields = documentFieldDtos
                        .Where(f => f.DocumentSectionId == sectionDto.Id)
                        .Select(f => BuildField(f))
                        .ToList(),
                });

        private IDocumentField<Field> BuildField(DocumentFieldDto f) =>
            f.FieldType switch
            {
                FieldType.Choice => new DocumentChoiceField(f.Id, f.FieldOrder, f.IsOptional, f.FieldContent),
                FieldType.Text => new DocumentTextField(f.Id, f.FieldOrder, f.IsOptional, f.FieldContent),
                _ => throw new NotImplementedException(),
            };
    }
}