﻿@page

@model Hca.WebHost.Pages.IndexModel
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    ViewData["Title"] = "Home Counties Asbestos Ltd";
}


@if (ViewManager.IsClientUser.Result)
{
    Response.Redirect("/clients/" + ViewManager.GetCurrentClientAsync().Result.UrlSafeClientName);
}
else if (ViewManager.ClientId.HasValue)
{
    @if (await ViewManager.IsHcaUser)
    {
        <div class="row">
            <div class="col">
                Viewing the dashboard as seen by the client<br />
                <a class="ml-auto btn btn-danger" href="/?@(Constants.QueryStringViewAsClient)=clear">Close Client View</a>
            </div>>
        </div>
    }
    <partial name="ClientView/_ClientHome" />
}
else
{
    Response.Redirect("/clients");
}