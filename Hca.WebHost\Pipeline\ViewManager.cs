﻿using System;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Identity;
using Hca.WebHost.Identity;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;

namespace Hca.WebHost.Pipeline;

// persist user-specific view data across postbacks
// when scaling either implement shared session or move to a distributed/centralised cache
public class ViewManager
{
    private readonly HttpContext _httpContext;
    private readonly IAuthorizationService _authService;
    private readonly IMediator _mediator;

    public ViewManager(
        IAuthorizationService authService,
        IHttpContextAccessor httpContextAccessor,
        IMediator mediator)
    {
        _authService = authService;
        _httpContext = httpContextAccessor.HttpContext;
        _mediator = mediator;
    }

    private Guid? _clientId;
    // allows the user to potentially view another client, i.e. if a staff user needs to look at a specific client's data
    public Guid? ClientId
    {
        get
        {
            if (_clientId.HasValue) return _clientId;

            if (_httpContext.Request.Cookies.ContainsKey(Constants.StoredClientId))
            {
                return Guid.Parse(_httpContext.Request.Cookies[Constants.StoredClientId]);
            }
            else
            {
                return null;
            }
        }
        set
        {
            _clientId = value;

            if (value.HasValue)
            {
                _httpContext.Response.Cookies.Append(Constants.StoredClientId, value.ToString());
            }
            else
            {
                _httpContext.Response.Cookies.Delete(Constants.StoredClientId);
            }
        }
    }

    public string ClientPageName
    {
        get
        {
            var pathParts = _httpContext.Request.Path.Value.Split('/').Where(s => !string.IsNullOrWhiteSpace(s)).ToArray();
            if (pathParts?.Length > 1 && pathParts[0] == "client")
            {
                return pathParts[1];
            }

            return null;
        }
    }

    public bool IsClientPage => !string.IsNullOrWhiteSpace(ClientPageName);

    public async Task<ClientDto> GetCurrentClientAsync(CancellationToken cancellationToken = default)
    {
        // if a client user is logged in always return their client
        if (await IsClientUser) return await _mediator.Send(new GetClientByIdQuery(UserClientId), cancellationToken);
        // otherwise check the url to see if a client page is being requested e.g. login
        return IsClientPage ? await _mediator.Send(new GetClientByUrlQuery(ClientPageName), cancellationToken) : null;
    }

    public string WriteUrl(string absoluteUrl)
    {
        if (!IsClientPage) return absoluteUrl;

        var client = GetCurrentClientAsync().Result;
        return Path.Join($"/clients/{client.UrlSafeClientName}", absoluteUrl);
    }

    public async Task<string> WriteUrlAsync(string absoluteUrl, CancellationToken cancellationToken = default)
    {
        if (!IsClientPage) return absoluteUrl;

        var client = await GetCurrentClientAsync(cancellationToken);
        return Path.Join($"/clients/{client.UrlSafeClientName}", absoluteUrl);
    }

    public Guid UserId
    {
        get => Guid.Parse(_httpContext.User.Claims.SingleOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value ?? Guid.Empty.ToString());
    }

    public Guid UserClientId
    {
        get => Guid.Parse(_httpContext.User.Claims.SingleOrDefault(c => c.Type == HcaClaimTypes.CLIENTID)?.Value ?? Guid.Empty.ToString());
    }

    public Task<bool> UserCanDelete => _authService.AuthorizeAsync(_httpContext.User, Permissions.CANDELETE).ContinueWith(r => r.Result.Succeeded);

    public Task<bool> IsAdminUser => _authService.AuthorizeAsync(_httpContext.User, HcaRole.ROLE_ADMIN).ContinueWith(r => r.Result.Succeeded);

    public Task<bool> IsHcaUser => _authService.AuthorizeAsync(_httpContext.User, HcaRole.ROLE_HCA).ContinueWith(r => r.Result.Succeeded);

    public Task<bool> IsClientUser => _authService.AuthorizeAsync(_httpContext.User, HcaRole.ROLE_CLIENT).ContinueWith(r => r.Result.Succeeded);

    public string ContactId => _httpContext.User.Claims.SingleOrDefault(c => c.Type == HcaClaimTypes.CONTACTID)?.Value;
}
