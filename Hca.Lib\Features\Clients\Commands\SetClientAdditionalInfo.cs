﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class SetClientAdditionalInfo : ICommand
    {
        public SetClientAdditionalInfo(
            Guid clientId,
            string emergencyContactDetails,
            string escalationProcedure,
            string kpisAndSlas,
            string invoiceEmailAddress,
            string accountQueriesEmailAddress,
            string generalRequirements)
        {
            ClientId = clientId;
            EmergencyContactDetails = emergencyContactDetails;
            EscalationProcedure = escalationProcedure;
            KpisAndSlas = kpisAndSlas;
            InvoiceEmailAddress = invoiceEmailAddress;
            AccountQueriesEmailAddress = accountQueriesEmailAddress;
            GeneralRequirements = generalRequirements;
        }

        public Guid ClientId { get; }

        public string EmergencyContactDetails { get; }
        public string EscalationProcedure { get; }
        public string KpisAndSlas { get; }
        public string InvoiceEmailAddress { get; }
        public string AccountQueriesEmailAddress { get; }
        public string GeneralRequirements { get; }
    }

    public class SetClientAdditionalInfoHandler : DapperRequestHandler<SetClientAdditionalInfo, CommandResult>
    {
        public SetClientAdditionalInfoHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetClientAdditionalInfo request)
        {
            var clientDto = await db.GetAsync<ClientDto>(request.ClientId);
            clientDto.EmergencyContactDetails = request.EmergencyContactDetails;
            clientDto.EscalationProcedure = request.EscalationProcedure;
            clientDto.KpisAndSlas = request.KpisAndSlas;
            clientDto.InvoiceEmailAddress = request.InvoiceEmailAddress;
            clientDto.AccountQueriesEmailAddress = request.AccountQueriesEmailAddress;
            clientDto.GeneralRequirements = request.GeneralRequirements;
            await db.UpdateAsync(clientDto);
            return CommandResult.Success();
        }
    }
}
