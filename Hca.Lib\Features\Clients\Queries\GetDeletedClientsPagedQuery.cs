﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetDeletedClientsPagedQuery : PagedQuery<DeletedClientModel>
    {
        public GetDeletedClientsPagedQuery(
            int? pageNum = 1, 
            int? pageSize = 20) : base(pageNum, pageSize)
        {
        }
    }

    public class GetDeletedClientsPagedQueryHandler : DapperRequestHandler<GetDeletedClientsPagedQuery, PagedDtoSet<DeletedClientModel>>
    {
        public GetDeletedClientsPagedQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<DeletedClientModel>> OnHandleAsync(IDbHelper db, GetDeletedClientsPagedQuery request)
        {
            var sql = @"
SELECT Id, ClientName
FROM tblClients
WHERE Deleted IS NOT NULL
ORDER BY ClientName
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblClients 
WHERE Deleted IS NOT NULL";

            var (Items, Total) = await db.QueryPageAsync<DeletedClientModel>(sql, request.Page, request.PageSize);

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
