﻿@page "/clients"
@model Hca.WebHost.Areas.Clients.Pages.ClientsIndexModel
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    ViewData["Title"] = "Clients";
}

<div class="row">
    <div class="col-xl-3 col-lg-4">

        @if (await ViewManager.IsAdminUser)
        {
            <div class="collapse show">
                <div class="card card-default">
                    <div class="card-body">
                        <ul class="nav nav-pills flex-column">
                            <li class="nav-item p-2">
                                <small class="text-muted">ACTIONS</small>
                            </li>
                            <li class="nav-item">
                                <a href="@Urls.ClientsNew.AddNewMode()" class="nav-link d-flex btn btn-success btn-add-client" style="width: 100%; margin: 5px 0;">New Client</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        }
        <!-- END menu-->
    </div>

    <div class="col-xl-9 col-lg-8" id="divSearchClients">
        <div class="row">
            <div class="col">

                <form hx-post="/clients"
                      hx-target="#divClientSearchResults"
                      hx-swap="innerHTML"
                      hx-trigger="load, click from:#btnSearchClients, change from:#ShowArchivedClients">
                    <div class="form-group mb-4">
                        <input class="form-control mb-2" type="text" placeholder="Search clients" id="txtSearch" name="searchText">
                        <div class="d-flex">
                            <button class="btn btn-secondary"
                                    type="button"
                                    id="btnSearchClients">
                                Search
                            </button>
                            <button class="btn btn-sm btn-secondary">Clear</button>
                            <div class="d-flex align-items-center">
                                <input asp-for="ShowArchivedClients" class="ml-4 mx-2" />Show Archived Clients
                            </div>
                        </div>

                    </div>
                    @Html.AntiForgeryToken()
                </form>
            </div>
        </div>

        <div class="row">
            <div class="col">
                <div class="card card-default">
                    <div class="card-header">
                        <small class="text-muted">CLIENTS</small>
                    </div>
                    <div class="card-body" id="divClientSearchResults">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Custom layout for content that needs to fill height with proper padding */
        .layout-content-fill .wrapper .section-container {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
            margin-bottom: 0 !important; /* Override the default footer margin */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            /* Keep the normal padding for readability */
        }

        /* Make the container-fluid fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Ensure the main content area fills available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the card fill the available height */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col > .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row:last-child > .col > .card > .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    </style>
}

@section scripts {
    <script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>
    <script>
        $(function () {
            drawBreadcrumb([]);
        });
    </script>
}
