﻿@model Hca.Lib.Features.Clients.AddressDto
@{
    var showMap = (bool)(ViewData["showMap"] ?? true);
    var showMapKey = (bool)(ViewData["showMapKey"] ?? true);
}

<div class="row">
    <div class="col">
        <partial name="~/Areas/Clients/Pages/_AddressFieldsPartial.cshtml" model="Model" />
    </div>
    @if (showMap)
    {
        <div class="col">
            <partial name="~/Areas/Clients/Pages/_Map.cshtml" model='new _MapModel { ShowMapKey = showMapKey }' />
        </div>
    }
</div>