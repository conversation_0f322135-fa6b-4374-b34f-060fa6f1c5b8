﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Commands;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Documents.Templates.Queries;
using Hca.Lib.Features.TemplateFiles.Templates.Queries;
using Hca.Lib.Features.Templates;
using Hca.Lib.Features.Templates.Commands;
using Hca.Lib.Features.Templates.Data.Queries;
using MediatR;

namespace Hca.WebHost.Services
{
    public class TemplateService
    {
        private readonly IMediator _mediator;

        public TemplateService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<TemplateDto> GetTemplateAsync(Guid templateId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetTemplate(templateId), cancellationToken);

        public Task RemoveTemplateFileAsync(Guid templateId, Guid fileId, CancellationToken cancellationToken) =>
            _mediator.Send(new RemoveTemplateFile(templateId, fileId), cancellationToken);

        public Task<DtoSet<TemplateFileDto>> GetTemplateFilesAsync(Guid templateId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetTemplateFiles(templateId), cancellationToken);

        public Task<DtoSet<TemplateSectionDto>> GetTemplateSectionsAsync(Guid templateId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetTemplateSectionsQuery(templateId), cancellationToken);

        public Task UpdateTemplateAsync(TemplateDto template, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateTemplate(template.Id, template.TemplateCode, template.TemplateType, template.TemplateName), cancellationToken);

        public Task AddTemplateFile(Guid templateId, string fileName, byte[] fileContent, string contentType, CancellationToken cancellationToken) =>
            _mediator.Send(new AddFileToTemplate(templateId, fileName, fileContent, contentType), cancellationToken);

        internal Task UpdateFieldAsync(Guid templateId, Guid sectionId, ITemplateField<Field> field, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateTemplateField(templateId, sectionId, field), cancellationToken);

        public Task AddTemplate(Guid templateId, string templateCode, TemplateType templateType, string templateName, CancellationToken cancellationToken) =>
            _mediator.Send(new CreateTemplate(templateId, templateCode, templateType, templateName), cancellationToken);

        public Task<IEnumerable<TemplateFieldDto>> GetFieldsAsync(Guid templateId, Guid sectionId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetTemplateSectionFieldsQuery(templateId, sectionId), cancellationToken).ContinueWith(t => t.Result.Items);

        public Task<TemplateSection> GetTemplateSectionAsync(Guid templateId, Guid sectionId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetTemplateSectionObject(sectionId), cancellationToken);

        public Task UpdateFileOrders(IEnumerable<TemplateFileOrder> values, CancellationToken cancellationToken) =>
            Task.WhenAll(values.Select(v => _mediator.Send(new SetTemplateFilePriority(v.Id, v.Priority), cancellationToken)));

        public Task AddSectionAsync(TemplateSection section, CancellationToken cancellationToken) =>
            _mediator.Send(new AddSectionToTemplate(
                section.TemplateId,
                Guid.NewGuid(),
                section.SectionCode,
                section.SectionTitle), cancellationToken);

        public Task UpdateSectionAsync(TemplateSection section, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateTemplateSection(
                section.TemplateId,
                section.TemplateSectionId,
                section.SectionCode,
                section.SectionTitle), cancellationToken);

        public Task<DtoSet<TemplateDto>> GetTemplatesAsync(TemplateType? type, CancellationToken cancellationToken) =>
            _mediator.Send(new FindTemplates(type), cancellationToken);

        public Task DeleteSectionAsync(Guid templateId, Guid sectionId, CancellationToken cancellationToken) =>
            _mediator.Send(new RemoveSectionFromTemplate(templateId, sectionId), cancellationToken);

        public Task DeleteFieldAsync(Guid templateId, Guid sectionId, Guid fieldId, CancellationToken cancellationToken) =>
            _mediator.Send(new RemoveFieldFromTemplateSection(templateId, sectionId, fieldId), cancellationToken);

        public Task<byte[]> RetrieveTemplateFile(Guid templateId, Guid fileId, CancellationToken cancellationToken) =>
            _mediator.Send(new DownloadTemplateFile(templateId, fileId), cancellationToken);

        public class TemplateFileOrder
        {
            public Guid Id { get; set; }

            public int Priority { get; set; }
        }
    }
}
