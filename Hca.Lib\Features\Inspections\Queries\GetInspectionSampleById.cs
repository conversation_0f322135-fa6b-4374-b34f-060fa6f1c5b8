﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections.Queries;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetInspectionSampleById : IQuery<InspectionSampleDto>
    {
        public GetInspectionSampleById(Guid sampleId)
        {
            SampleId = sampleId;
        }

        public Guid SampleId { get; }
    }

    public class GetInspectionSampleByIdHandler : DapperRequestHandler<GetInspectionSampleById, InspectionSampleDto>
    {
        public GetInspectionSampleByIdHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<InspectionSampleDto> OnHandleAsync(IDbHelper db, GetInspectionSampleById request)
        {
            return db.GetAsync<InspectionSampleDto>(request.SampleId);
        }
    }
}
