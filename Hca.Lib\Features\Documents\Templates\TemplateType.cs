﻿using System;

namespace Hca.Lib.Features.Templates
{
    public enum TemplateType
    {
        Report = 1,
        Quote
    }

    public static class TemplateTypeExtensions
    {
        public static string DisplayName(this TemplateType templateType) =>
            templateType switch
            {
                TemplateType.Quote => "Quote Template",
                TemplateType.Report => "Report Template",
                _ => throw new ApplicationException("Unknown template type")
            };
    }
}
