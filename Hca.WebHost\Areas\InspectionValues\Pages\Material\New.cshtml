﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Material.New
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@using Hca.WebHost.Areas.InspectionValues.Pages.Widgets
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
{
            { nameof( ValueDto.DisplayText), "Material Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
            { nameof( ValueDto.Value), "Requires a colour" },
        },
        Header = "Material",
        Value = Model.Value,
        InspectionValueType = InspectionValueType.Boolean,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />