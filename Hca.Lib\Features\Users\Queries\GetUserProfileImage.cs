﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Users.Queries
{
    public class GetUserProfileImage : IRequest<string>
    {
        public GetUserProfileImage(Guid userId)
        {
            UserId = userId;
        }

        public Guid UserId { get; }
    }

    public class GetUserProfileImageHandler : DapperRequestHandler<GetUserProfileImage, string>
    {
        private readonly IMediator _mediator;

        public GetUserProfileImageHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public async override Task<string> OnHandleAsync(IDbHelper db, GetUserProfileImage request)
        {
            var user = await db.GetAsync<UserDto>(request.UserId);
            var sig = await _mediator.Send(new GetSasUrl("users", user.ProfileImageFileName, false));
            return sig.AbsoluteUri;
        }
    }
}
