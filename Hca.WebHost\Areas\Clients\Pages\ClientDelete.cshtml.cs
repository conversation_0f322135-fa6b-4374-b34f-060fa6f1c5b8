﻿using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientDeleteModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientDeleteModel(
        ClientService clientService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnPostAsync(
        Guid clientId,
        CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();
        //{
        //    var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
        //    if (!valid.IsValid) return Forbid();
        //}

        var deleteResult = await _clientService.DeleteClientAsync(clientId, cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.Clients);
        }

        ErrorMessage = deleteResult.Reason;
        return Page();
    }
}
