﻿CREATE TABLE [dbo].[tblInspections] (
    [Id]               UNIQUEIDENTIFIER CONSTRAINT [DF_Inspections_Id] DEFAULT (newid()) NOT NULL,
    [Created]          DATETIME         CONSTRAINT [DF_Inspections_Created] DEFAULT (getdate()) NOT NULL,
    [PropertyId]       UNIQUEIDENTIFIER NOT NULL,
    [InspectionTypeId] UNIQUEIDENTIFIER NOT NULL,
    [ProjectId]        UNIQUEIDENTIFIER NULL,
    CONSTRAINT [PK_Inspections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Inspections_InspectionTypes] FOREIGN KEY ([InspectionTypeId]) REFERENCES [dbo].[tbl<PERSON><PERSON><PERSON><PERSON><PERSON>] ([Id]),
    CONSTRAINT [FK_Inspections_Projects] FOREIGN KEY ([ProjectId]) REFERENCES [dbo].[tblProjects] ([Id]),
    CONSTRAINT [FK_Inspections_Properties] FOREI<PERSON>N KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id])
);

