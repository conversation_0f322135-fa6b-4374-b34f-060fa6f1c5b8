﻿using Hca.Lib.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Hca.WebHost.Controllers;

[ApiController]
public class ClientsController : Controller
{
    private readonly ClientCountsService _clientCountsService;

    public ClientsController(ClientCountsService clientCountsService)
    {
        _clientCountsService = clientCountsService;
    }

    [HttpGet("api/{clientId}/child-counts")]
    public async Task<IActionResult> GetClientChildCounts(Guid clientId)
    {
        var counts = await _clientCountsService.GetClientCountsAsync(clientId);
        return Ok(counts);
    }
}
