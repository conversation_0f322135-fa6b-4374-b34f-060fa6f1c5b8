﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    //public class SetInspectionReportTemplate : InspectionCommand
    //{
    //    public SetInspectionReportTemplate(
    //        Guid inspectionId,
    //        Guid templateId) : base(inspectionId)
    //    {
    //        TemplateId = templateId;
    //    }

    //    public Guid TemplateId { get; }
    //}

    //public class SetInspectionReportTemplateHandler : DapperRequestHandler<SetInspectionReportTemplate, CommandResult>
    //{
    //    public SetInspectionReportTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

    //    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetInspectionReportTemplate request)
    //    {
    //        var inspection = await db.GetAsync<InspectionDto>(request.InspectionId);
    //        inspection.TemplateId = request.TemplateId;
    //        await db.UpdateAsync(inspection);

    //        return CommandResult.Success();
    //    }
    //}
}
