﻿using MediatR.Pipeline;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Core
{

    public class CommandExceptionHandler : AsyncRequestExceptionHandler<AggregateCommand<IAggregate>, CommandResult>
    {
        private readonly ILogger<CommandExceptionHandler> _logger;

        public CommandExceptionHandler(ILogger<CommandExceptionHandler> logger)
        {
            _logger = logger;
        }

        protected override Task Handle(AggregateCommand<IAggregate> request, Exception exception, RequestExceptionHandlerState<CommandResult> state, CancellationToken cancellationToken)
        {
            _logger.LogError($"Error processing command: {JsonConvert.SerializeObject(request)}", exception);

            state.SetHandled(CommandResult.Fail());

            return Task.CompletedTask;
        }
    }
}
