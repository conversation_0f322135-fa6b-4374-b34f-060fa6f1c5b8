﻿namespace Hca.Lib.Core
{
    public class CommandResult
    {
        public bool IsSuccess { get; protected set; } = false;

        public string Reason { get; protected set; }

        public static CommandResult Success()
        {
            return new CommandResult
            {
                IsSuccess = true,
            };
        }

        public static CommandResult Fail(string reason = null)
        {
            return new CommandResult { Reason = reason };
        }
    }

    public class CommandResult<T> : CommandResult
    {
        public T Value { get; private set; }

        public static CommandResult<T> Success(T value) 
        {
            return new CommandResult<T>
            {
                IsSuccess = true,
                Value = value
            };
        }

        public new static CommandResult<T> Fail(string reason = null)
        {
            return new CommandResult<T> { Reason = reason };
        }
    }
}
