﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Commands
{
    public class UpdateQuoteProperty : ICommand
    {
        public UpdateQuoteProperty(Guid quotePropertyId, Guid propertyId, double proposedFee, string notes)
        {
            QuotePropertyId = quotePropertyId;
            PropertyId = propertyId;
            ProposedFee = proposedFee;
            Notes = notes;
        }

        public Guid QuotePropertyId { get; }
        public Guid PropertyId { get; }
        public double ProposedFee { get; }
        public string Notes { get; }
    }

    public class UpdateQuotePropertyHandler : DapperRequestHandler<UpdateQuoteProperty, CommandResult>
    {
        public UpdateQuotePropertyHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateQuoteProperty request)
        {
            var dto = await db.GetAsync<QuotePropertyDto>(request.QuotePropertyId);
            dto.PropertyId = request.PropertyId;
            dto.Notes = request.Notes;
            dto.ProposedFee = request.ProposedFee;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
