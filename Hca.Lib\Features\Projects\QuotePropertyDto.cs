﻿using System;
using Dapper;

namespace Hca.Lib.Features.Projects
{
    [Table("tblQuoteProperties")]
    public class QuotePropertyDto
    {
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public Guid QuoteId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public Guid PropertyId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public double ProposedFee { get; set; }

        public string Notes { get; set; }
    }
}
