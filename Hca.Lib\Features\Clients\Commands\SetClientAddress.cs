﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class SetClientAddress : ICommand
    {
        public SetClientAddress(
            Guid clientId,
            string buildingNumber,
            string buildingName,
            string unit,
            string floor,
            string street,
            string town,
            string city,
            string county,
            string country,
            string postcode)
        {
            ClientId = clientId;
            BuildingNumber = buildingNumber;
            BuildingName = buildingName;
            Unit = unit;
            Floor = floor;
            Street = street;
            Town = town;
            City = city;
            County = county;
            Country = country;
            Postcode = postcode;
        }

        public Guid ClientId { get; }

        public string BuildingNumber { get; }
        public string BuildingName { get; }
        public string Unit { get; }
        public string Floor { get; }
        public string Street { get; }
        public string Town { get; }
        public string City { get; }
        public string County { get; }
        public string Country { get; }
        public string Postcode { get; }
    }

    public class SetClientAddressHandler : DapperRequestHandler<SetClientAddress, CommandResult>
    {
        public SetClientAddressHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetClientAddress request)
        {
            var clientDto = await db.GetAsync<ClientDto>(request.ClientId);
            AddressDto addressDto;

            if (clientDto.AddressId.HasValue)
            {
                addressDto = await db.GetAsync<AddressDto>(clientDto.AddressId.Value);
            }
            else
            {
                addressDto = new AddressDto { Id = Guid.NewGuid() };
            }

            clientDto.BuildingName = request.BuildingName;
            clientDto.BuildingNumber = request.BuildingNumber;
            clientDto.Floor = request.Floor;
            clientDto.Unit = request.Unit;
            addressDto.StreetName = request.Street;
            addressDto.Town = request.Town;
            addressDto.City = request.City;
            addressDto.County = request.County;
            addressDto.Country = request.Country;
            addressDto.Postcode = request.Postcode;

            if (clientDto.AddressId.HasValue)
            {
                await db.UpdateAsync(addressDto);
            }
            else
            {
                await db.InsertAsync(addressDto);

                clientDto.AddressId = addressDto.Id;
            }

            await db.UpdateAsync(clientDto);

            return CommandResult.Success();
        }
    }
}
