﻿using System;

namespace Hca.Lib.Features.Documents.Fields
{
    public enum FieldType
    {
        Text = 1,
        Choice = 2
    }

    public static class FieldTypeExtensions
    {
        public static string ToFieldTypeString(this FieldType fieldType) =>
            fieldType switch
            {
                FieldType.Choice => "choice",
                FieldType.Text => "text",
                _ => throw new ArgumentOutOfRangeException(nameof(fieldType)),
            };

        public static FieldType ToFieldType(this string from) =>
            from.ToLower() switch
            {
                "choice" => FieldType.Choice,
                "text" => FieldType.Text,
                _ => throw new ArgumentOutOfRangeException(nameof(from)),
            };
    }
}
