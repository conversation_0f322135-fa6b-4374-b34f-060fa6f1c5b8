﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Config.Commands;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Projects;
using Hca.Lib.Features.Projects.Commands;
using Hca.Lib.Features.Projects.Documents.Commands;
using Hca.Lib.Features.Projects.Queries;
using Hca.Lib.Features.Templates;
using MediatR;

namespace Hca.WebHost.Services
{
    public class QuoteService
    {
        private readonly IMediator _mediator;
        public QuoteService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<PagedDtoSet<QuoteDto>> GetQuotesAsync(Guid clientId, CancellationToken cancellationToken) =>
            _mediator.Send(new FindClientQuotes(clientId), cancellationToken);

        public Task<IEnumerable<QuotePropertyWithAddressDto>> GetQuotePropertiesAsync(Guid quoteId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetQuoteProperties(quoteId), cancellationToken).ContinueWith(t => t.Result.Items);

        public Task<QuoteDto> GetQuoteAsync(Guid quoteId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetQuote(quoteId), cancellationToken);

        public Task AddQuoteAsync(Guid clientId, QuoteDto quote, CancellationToken cancellationToken) =>
            _mediator.Send(new CreateQuote(
                clientId,
                quote.ContactId,
                quote.Id,
                quote.QuoteNumber,
                quote.InspectionTypeId.Value,
                quote.Notes,
                quote.BaseFee),
                cancellationToken);

        public Task UpdateQuoteAsync(Guid clientId, QuoteDto quote, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateQuote(
                quote.Id,
                quote.ContactId,
                quote.InspectionTypeId.Value,
                quote.Notes,
                quote.BaseFee),
                cancellationToken);

        public async Task<string> GetNextQuoteNumber(CancellationToken cancellationToken)
        {
            var nextNumber = await _mediator.Send(new GetNextQuoteNumber(), cancellationToken);
            return nextNumber.GetValue();
        }

        public Task AddPropertyToQuoteAsync(Guid quoteId, Guid propertyId, double proposedFee, string notes, CancellationToken cancellationToken) =>
            _mediator.Send(new AddPropertyToQuote(quoteId, propertyId, proposedFee, notes), cancellationToken);

        public Task RemovePropertyFromQuoteAsync(Guid quotePropertyId, CancellationToken cancellationToken) =>
            _mediator.Send(new RemovePropertyFromQuote(quotePropertyId), cancellationToken);

        public Task UpdatePropertyAsync(Guid quotePropertyId, Guid propertyId, double proposedFee, string notes, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateQuoteProperty(quotePropertyId, propertyId, proposedFee, notes), cancellationToken);

        public Task<TemplateObject> GetQuoteTemplateAsync(CancellationToken cancellationToken) =>
            _mediator.Send(new GetQuoteTemplate(), cancellationToken);

        public Task<Guid> CreateQuoteDocumentAsync(Guid quoteId, string quoteBody, CancellationToken cancellationToken) =>
            _mediator.Send(new CreateQuoteDocument(quoteId, quoteBody), cancellationToken).ContinueWith(r => r.Result.Value);

        public Task PublishQuoteDocumentAsync(Guid quoteId, Guid documentId, CancellationToken cancellationToken) =>
            _mediator.Send(new PublishQuoteDocument(documentId, quoteId), cancellationToken);

        public Task SetQuoteTemplateAsync(Guid templateId, CancellationToken cancellationToken) =>
            _mediator.Send(new SetQuoteTemplate(templateId), cancellationToken);

        public Task<Guid> ConvertQuoteToProjectAsync(Guid clientId, Guid quoteId, CancellationToken cancellationToken) =>
            _mediator.Send(new ConvertQuoteToProject(quoteId), cancellationToken).ContinueWith(r => r.Result.Value);

        internal Task DeleteQuoteAsync(Guid clientId, Guid quoteId, CancellationToken cancellationToken) =>
            _mediator.Send(new DeleteQuote(quoteId), cancellationToken);
    }
}
