﻿CREATE TABLE [dbo].[tblPropertyDocuments] (
    [Id]                       UNIQUEIDENTIFIER CONSTRAINT [DF_PropertyDocuments_Id] DEFAULT (newid()) NOT NULL,
    [Created]                  DATETIME         CONSTRAINT [DF_PropertyDocuments_Created] DEFAULT (getdate()) NOT NULL,
    [PropertyId]               UNIQUEIDENTIFIER NOT NULL,
    [ContainerName]            NVARCHAR (MAX)   NOT NULL,
    [BlobName]                 NVARCHAR (MAX)   NOT NULL,
    [DocumentDate]             DATETIME         NULL,
    [DocumentType]             NVARCHAR (MAX)   NULL,
    [PropertyDocumentType]     INT              NULL,
    [CompanyName]              NVARCHAR (MAX)   NULL,
    [Notes]                    NVARCHAR (MAX)   NULL,
    [NextInspection]           DATETIME         NULL,
    [QrCodeId] NVARCHAR(50) NULL, 
    CONSTRAINT [PK_PropertyDocuments] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_PropertyDocuments_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id])
);

