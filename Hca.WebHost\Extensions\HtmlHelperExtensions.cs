﻿using System;
using Microsoft.AspNetCore.Html;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Hca.WebHost
{
    public static class HtmlHelperExtensions
    {
        public static string IsActive(this IHtmlHelper html, string page = null, string area = null)
        {
            string activeClass = "active"; // change here if you another name to activate sidebar items
                                           // detect current app state
            string actualPage = (string)html.ViewContext.RouteData.Values["page"];
            string actualArea = (string)html.ViewContext.RouteData.Values["area"];
            //string actualController = (string)html.ViewContext.RouteData.Values["controller"];

            //if (String.IsNullOrEmpty(controller))
            //    controller = actualController;

            if (String.IsNullOrEmpty(page))
                page = actualPage;
            // controller == actualController && 
            return (area == actualArea && page == actualPage) ? activeClass : String.Empty;
        }

        public static IHtmlContent PreserveNewLines(this IHtmlHelper htmlHelper, string message)
        {
            return message == null ? null : htmlHelper.Raw(htmlHelper.Encode(message).Replace("\n", "<br/>"));
        }
    }
}
