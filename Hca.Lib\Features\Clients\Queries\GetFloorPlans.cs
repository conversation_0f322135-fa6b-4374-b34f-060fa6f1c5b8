﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetFloorPlans : IQueryMany<FloorPlanDto>
    {
        public GetFloorPlans(Guid clientId, Guid propertyId)
        {
            ClientId = clientId;
            PropertyId = propertyId;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
    }

    public class GetFloorPlansHandler : DapperRequestHandler<GetFloorPlans, DtoSet<FloorPlanDto>>
    {
        public GetFloorPlansHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<FloorPlanDto>> OnHandleAsync(IDbHelper db, GetFloorPlans request)
        {
            var sql = $"SELECT {TableNames.FloorPlans}.*, {TableNames.ValueLists}.{nameof(ValueDto.DisplayText)} AS FloorName " +
                $"FROM {TableNames.FloorPlans} " +
                $"LEFT JOIN {TableNames.ValueLists} ON {nameof(FloorPlanDto.FloorId)} = {TableNames.ValueLists}.{nameof(ValueDto.Id)} " +
                $"WHERE {nameof(FloorPlanDto.PropertyId)} = @{nameof(GetFloorPlans.PropertyId)}";
            var dtos = await db.QueryAsync<FloorPlanDto>(sql, request);

            return DtoSet.From(dtos.OrderByDescending(d => d.Created));
        }
    }
}
