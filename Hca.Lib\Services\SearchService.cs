﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Features.Clients;
using MediatR;

namespace Hca.Lib.Services
{
    public class SearchRequest : PagedQuery<SearchResult>
    {
        public SearchRequest(string searchTerm, bool isArchived = false)
        {
            SearchTerm = searchTerm;
            IsArchived = isArchived;
        }

        public string SearchTerm { get; }
        public bool IsArchived { get; }
    }

    public class SearchResult
    {
        public SearchEntity EntityType { get; set; }

        public string DisplayText { get; set; }

        public Guid EntityId { get; set; }
    }

    public enum SearchEntity
    {
        Client,
        Location,
        Project
    }

    public class SearchService : IRequestHandler<SearchRequest, PagedDtoSet<SearchResult>>
    {
        private readonly IDbHelper _dbHelper;

        public SearchService(IDbHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        public async Task<PagedDtoSet<SearchResult>> Handle(SearchRequest request, CancellationToken cancellationToken)
        {
            var sql = @$"
DECLARE @Results TABLE(
    DisplayText NVARCHAR(MAX), 
    EntityType NVARCHAR(MAX), 
    EntityId UNIQUEIDENTIFIER,
    TotalCount INT)

;WITH ClientsCte AS (
    SELECT
        ClientName AS DisplayText,
        '{nameof(SearchEntity.Client)}' AS EntityType,
        tblClients.Id AS EntityId
    FROM tblClients
    LEFT JOIN tblAddresses ON AddressId = tblAddresses.Id
    WHERE (ClientName LIKE '%' + @SearchTerm + '%'
    OR BuildingName LIKE '%' + @SearchTerm + '%'
    OR StreetName LIKE '%' + @SearchTerm + '%'
    OR Town LIKE '%' + @SearchTerm + '%'
    OR County LIKE '%' + @SearchTerm + '%')
    AND IsClientArchived = @IsArchived
),
LocationsCte AS (
    SELECT
        {nameof(PropertyDtoExtended.PropertyCode)} AS DisplayText,
        '{nameof(SearchEntity.Location)}' AS EntityType,
        tblProperties.Id AS EntityId
    FROM tblProperties
    LEFT JOIN tblAddresses ON AddressId = tblAddresses.Id
    WHERE ({nameof(PropertyDtoExtended.PropertyCode)} LIKE '%' + @SearchTerm + '%'
    OR BuildingName LIKE '%' + @SearchTerm + '%'
    OR StreetName LIKE '%' + @SearchTerm + '%'
    OR Town LIKE '%' + @SearchTerm + '%'
    OR County LIKE '%' + @SearchTerm + '%')
    AND IsPropertyArchived = @IsArchived
),
UnionCte AS (
    SELECT * FROM ClientsCte
    UNION
    SELECT * FROM LocationsCte
),
TotalCountCte AS (
    SELECT COUNT(*) AS TotalCount FROM UnionCte
)
INSERT INTO @Results
SELECT * FROM UnionCte, TotalCountCte
ORDER BY DisplayText
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT DisplayText, EntityType, EntityId FROM @Results

SELECT MAX(TotalCount) AS TotalCount FROM @Results";

            var (Items, Total) = await _dbHelper.QueryPageAsync<SearchResult>(sql, request.Page, request.PageSize, request);

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
