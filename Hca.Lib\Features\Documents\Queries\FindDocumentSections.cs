﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Queries
{
    public class FindDocumentSections : IQueryMany<DocumentSectionDto>
    {
        public FindDocumentSections(Guid documentId)
        {
            DocumentId = documentId;
        }

        public Guid DocumentId { get; }
    }

    public class FindDocumentSectionsHandler : DapperRequestHandler<FindDocumentSections, DtoSet<DocumentSectionDto>>
    {
        public FindDocumentSectionsHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<DocumentSectionDto>> OnHandleAsync(IDbHelper db, FindDocumentSections request)
        {
            return DtoSet.From((await db.GetListAsync<DocumentSectionDto>(
                    "WHERE DocumentId = @DocumentId",
                    request))
                .OrderBy(s => s.SectionOrder));
        }
    }
}
