﻿body .sk-chasing-dots .sk-dot1, body .sk-chasing-dots .sk-dot2, body .sk-circle .sk-child:before, body .sk-cube-grid .sk-cube, body .sk-double-bounce .sk-double-bounce1, body .sk-double-bounce .sk-double-bounce2, body .sk-fading-circle .sk-circle:before, body .sk-rotating-plane, body .sk-rotating-plane.sk-spinner, body .sk-spinner-pulse.sk-spinner, body .sk-three-bounce div, body .sk-wandering-cubes .sk-cube1, body .sk-wandering-cubes .sk-cube2, body .sk-wave .sk-rect {
    background-color: #5d9cec !important;
}

div.wrapper {
    background-image: url(/images/bg_asbestos.jpg);
}

.slick-prev:before, .slick-next:before {
    color: #5d9cec !important;
}

.glyphicon-ok::before {
    content: "\f00c";
}

.glyphicon-remove::before {
    content: "\f00d";
}

.glyphicon {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-style: normal;
}

.dropzone .dz-default.dz-message {
    font-size: 20px !important;
}

.dropzone .dz-message {
    margin: 1em 0 !important;
}

/* ========================================================================
     Component: mailbox
 ========================================================================== */

$mail-box-active-item: $primary;
$mail-box-hover-item: #C0D0D3;
$mail-mails-active: #f7f8f9;


// Mail Navigation
// -----------------------------------
.mb-boxes {
    // height: 0;
    overflow: hidden;

    .nav > li > a {
        font-weight: 600;
        color: $primary;
        margin: 5px 0;
        @include transition(background-color .3s ease);

        &:hover,
        &:focus {
            background-color: $mail-box-hover-item;
        }
    }

    .nav > li.active > a {
        background-color: $mail-box-active-item !important;
        color: #fff;
    }

    @include media-breakpoint-up(lg) {
        height: auto !important;
        visibility: visible !important;
        display: block;
    }
}

@include media-breakpoint-up(lg) {
    .mb-compose-button {
        // .btn-block
        display: block;
        width: 100%;
    }

    .mb-toggle-button {
        display: none;
    }
}

// Mails list
// -----------------------------------
.mb-mails {

    > tbody > tr > td {
        border-top-color: transparent;
        cursor: pointer;
    }

    .mb-mail-active {
        background-color: $mail-mails-active !important;
    }

    .mb-mail-avatar {
        float: left;
        margin-right: 10px;
        width: 40px;
        height: 40px;
        border-radius: 3px;
    }

    .mb-mail-date {
        display: block;
        float: right;
        color: $text-muted;
        font-size: 12px;
        font-weight: bold;
    }

    .mb-mail-from {
        // display: block;
        font-size: 14px;
        font-weight: 600;
        line-height: 1;
    }

    .mb-mail-meta {
        overflow: hidden;
    }

    .mb-mail-subject {
        font-size: 18px;
        font-weight: 600;
        line-height: 1.1;
        margin-bottom: 6px;
    }

    .mb-mail-preview {
        clear: both;
        margin-top: 10px;
        color: $text-muted;
        margin-top: 2px;
    }

    .mb-attachment {
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid $gray-light;
    }
}

// Viewing mails
// -----------------------------------
.mb-view {
    > tbody > tr > td {
        cursor: auto;
    }

    .mb-mail-to-list {
        > a {
            margin-right: 5px;
            color: $text-muted;
        }
    }
}

// Fake Textarea
// -----------------------------------
div.textarea {
    -moz-appearance: textfield-multiline;
    -webkit-appearance: textarea;
    font: medium -moz-fixed;
    font: -webkit-small-control;
    overflow: auto;
    resize: both;
}

div.textarea hint {
    color: blue;
}

div.textarea span.hint {
    color: blue;
}

// buggering about with the logo
// doesn't cater for theme-specifics
a.navbar-brand {
    background-color: white;
    padding-top: 1px;
    border-right: 1px solid #ccc;
}

div.brand-logo-collapsed {
    padding: 10px 6px !important;
}

// Error formatting
// ------------------------------------
div.validation-summary-errors ul {
    list-style: none;
    padding: 5px;
}

// Fixed header tables
// ------------------------------------
div.fixed-header-table {
    overflow: auto;
}

div.fixed-header-table table {
    border-collapse: collapse;
}

div.fixed-header-table table th {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 2;
}

div.fixed-header-table table th[scope=row] {
    position: -webkit-sticky;
    position: sticky;
    left: 0;
    z-index: 1;
}

// Menu items
// ------------------------------------
span.menu-item-count {
    background-color: #f0f0f0;
    padding: 5px 10px;
    border-radius: 4px;
    text-align: center;
    min-width: 30px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

li.nav-count {
    display: ruby;
}

li.nav-count a {
    width: 160px;
}