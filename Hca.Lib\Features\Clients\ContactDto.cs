﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using Dapper;

    [Table(TableNames.Contacts)]
    public class ContactDto
    {
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public Guid ClientId { get; set; }

        public Guid? AddressId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public string FirstName { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public string LastName { get; set; }

        public string Email { get; set; }

        public string MobilePhone { get; set; }

        public string OfficePhone { get; set; }

        public string Password { get; set; }

        public string Position { get; set; }
    }
}
