﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel
@using Hca.Lib.Features.Inspections
@{
    var floorList = new SelectList(Model.Floors, nameof(ValueDto.Id), nameof(ValueDto.DisplayText), Model.FloorPlan?.Floor);
}
<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">ADD FLOOR PLAN</small>
    </div>
    <form method="post" enctype="multipart/form-data">
        <div class="card-body">

            <div class="form-group row">
                <label class="col-xl-2 col-form-label" for="UploadImage">Upload a Floor Plan</label>
                <div class="col-xl-10">
                    <div id="file-drop-container"></div>
                    <div asp-validation-summary="ModelOnly" class="text-danger text-center"></div>
                </div>
            </div>
            <select asp-for="FloorPlan.Floor"
                    asp-items="floorList"
                    row-label="Floor">
                <option selected="selected" value="">Please select</option>
            </select>
            <input type="text" asp-for="FloorPlan.Notes" row-label="Notes" />
        </div>
        <save-cancel-footer CancelUrl="@Urls.FloorPlans(Model.Client.Id, Model.Property.Id)"></save-cancel-footer>
    </form>
</div>

<script>
    $(() => {
        initializeFileDragAndDrop({
            selector: '#file-drop-container',
            inputName: '@Html.NameFor(m => m.UploadFile)',
        });
    });
</script>