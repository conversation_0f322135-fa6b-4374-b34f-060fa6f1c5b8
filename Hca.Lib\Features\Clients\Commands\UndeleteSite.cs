﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UndeleteSite : ICommand
    {
        public UndeleteSite(Guid clientId, Guid siteId)
        {
            ClientId = clientId;
            SiteId = siteId;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
    }

    public class UndeleteSiteHandler : DapperRequestHandler<UndeleteSite, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;

        public UndeleteSiteHandler(IDbHelper dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UndeleteSite request)
        {
            var site = await db.GetAsync<SiteDto>(request.SiteId);
            var deletedDate = site.Deleted;

            // undelete the site
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET {nameof(SiteDto.Deleted)} = NULL " +
                $"WHERE {nameof(SiteDto.Id)}=@{nameof(request.SiteId)}", request);

            // undelete the properties
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)} = NULL " +
                $"WHERE {nameof(PropertyDto.SiteId)}=@{nameof(request.SiteId)} " +
                $"AND {nameof(PropertyDto.Deleted)}=@{nameof(deletedDate)}",
                new { request.SiteId, deletedDate });

            _clientCountsService.ClearClientCountsAsync(site.ClientId);

            return CommandResult.Success();
        }
    }
}
