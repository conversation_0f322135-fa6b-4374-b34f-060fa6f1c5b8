# Access Management Guide for Spotlite System

## Introduction

This document is designed to help you, as a non-technical stakeholder, provide access to the Spotlite system for a new developer or maintainer in the event that the current technical lead is no longer available. Don't worry - this process is straightforward and I'll guide you through each step.

The key areas we need to cover are:
1. Granting access to the source code repository (GitHub)
2. Providing access to the cloud infrastructure (Microsoft Azure)
3. Ensuring database access for maintenance and backups

## 1. GitHub Repository Access

The source code for Spotlite is stored in a private repository on GitHub at: https://github.com/stratacompliance/hca

### Steps to Grant Access:

1. **Sign in to GitHub**
   - Go to https://github.com and sign in with your admin credentials

2. **Navigate to the Repository**
   - Go to https://github.com/stratacompliance/hca

3. **Access Repository Settings**
   - Click on the "Settings" tab (located on the right side of the repository page)

4. **Manage Access**
   - In the left sidebar, click on "Collaborators & teams"
   - You'll see a section "Manage access"

5. **Invite a New Collaborator**
   - Click the green "Add people" button
   - Enter the email address or GitHub username of the person you want to invite
   - Select the appropriate permission level:
     - **Read** - Can view code but not make changes
     - **Triage** - Can view and manage issues and pull requests
     - **Write** - Can view, make changes, and push code
     - **Maintain** - Can manage repository without access to sensitive areas
     - **Admin** - Full access to the repository (recommended for someone taking over maintenance)

6. **Send Invitation**
   - Click "Add [username] to repository"
   - The person will receive an email invitation to join the repository

### Important Notes:
- The new developer will need their own GitHub account
- If they don't have one, they can create it for free at https://github.com/join
- For someone taking over maintenance, Admin access is recommended

## 2. Azure and Azure DevOps Access

Spotlite is hosted on Microsoft Azure, and the deployment pipelines are managed through Azure DevOps.

### Steps to Grant Azure Access:

1. **Sign in to Azure Portal**
   - Go to https://portal.azure.com and sign in with your admin credentials

2. **Navigate to Microsoft Entra ID (formerly Azure Active Directory)**
   - In the left sidebar, click on "Microsoft Entra ID (formerly Azure Active Directory)"

3. **Manage Users**
   - Click on "Users" in the left sidebar under "Manage"
   - Click "+ New user" at the top

4. **Create a New User**
   - Select "Invite user" (recommended for external developers)
   - Enter the email address of the person you want to invite
   - Fill in their name and other details
   - Click "Invite"

5. **Assign Roles**
   - After creating the user, click on their name in the user list
   - Click on "Assigned roles" in the left sidebar
   - Click "+ Add assignments"
   - Select appropriate roles:
     - **Reader** - Can view resources but not make changes
     - **Contributor** - Can manage resources but not access to them
     - **Owner** - Full access to resources (recommended for someone taking over maintenance)

### Steps to Grant Azure DevOps Access:

1. **Sign in to Azure DevOps**
   - Go to https://dev.azure.com/stratacompliance and sign in with your admin credentials

2. **Access Organization Settings**
   - Click on "Organization settings" in the bottom left corner

3. **Manage Users**
   - Click on "Users" in the left sidebar under "Security"

4. **Add Users**
   - Click "+ Add users" at the top
   - Enter the email address of the person you want to invite
   - Select appropriate access level:
     - **Stakeholder** - Free access for basic features
     - **Basic** - Standard access for developers
     - **Basic + Test Plans** - Includes test management features
     - **Visual Studio Subscriber** - For users with Visual Studio subscriptions

5. **Assign to Projects**
   - After adding the user, you'll be prompted to assign them to projects
   - Select the Spotlite project and assign appropriate permissions

### Important Notes:
- Azure and Azure DevOps are separate systems, so access needs to be granted to both
- For someone taking over maintenance, Owner access in Azure and Basic or higher in Azure DevOps is recommended
- The new developer may need assistance from a technical person to understand the specific resources they need to manage

## 3. Database Access for Backups

Spotlite uses Azure SQL Database (Platform as a Service), which makes database access and backups straightforward through the Azure portal.

### Steps to Grant Database Access:

1. **Sign in to Azure Portal**
   - Go to https://portal.azure.com and sign in with your admin credentials

2. **Navigate to SQL Databases**
   - In the search bar at the top, type "SQL databases" and select it
   - Find the database used by Spotlite (likely named something like "HCA" based on the connection string)

3. **Access Database Settings**
   - Click on the database name to open its settings

4. **Manage Access**
   - In the left sidebar, under "Settings", click on "Access control (IAM)"
   - Click "+ Add" and then "Add role assignment"

5. **Assign Roles**
   - Select appropriate roles:
     - **SQL DB Contributor** - Can manage the database but not access its contents
     - **SQL Security Manager** - Can manage security-related features
     - **Owner** - Full access (recommended for someone taking over maintenance)
   - Assign to the user you want to grant access to

### Database Backup Information:

1. **Automatic Backups**
   - Azure SQL Database automatically creates backups
   - These are managed by Azure and don't require manual intervention

2. **Manual Backups**
   - If manual backups are needed, they can be created through:
     - Azure portal (export feature)
     - SQL Server Management Studio
     - Azure CLI or PowerShell

3. **Accessing Backups**
   - The new developer can access backups through the Azure portal
   - In the database settings, under "Overview", there's an "Export" button for creating manual backups

### Important Notes:
- Azure SQL Database handles most backup scenarios automatically
- For manual backups, the new developer may need assistance from a technical person initially
- Database connection strings and credentials are stored in Azure Key Vault for security

## 4. Additional Considerations

### Documentation and Knowledge Transfer

To ensure smooth continuity, consider the following:

1. **Share Existing Documentation**
   - Provide access to the README.md file we created
   - Share any other technical documentation that exists

2. **Introduce Key Contacts**
   - If there are other technical team members, introduce them to the new developer
   - Provide contact information for ongoing support

3. **Explain Business Context**
   - Help the new developer understand the business purpose of Spotlite
   - Explain key features and their importance to the organization

### Ongoing Support

If you're concerned about the complexity of these steps, please don't hesitate to:

1. **Request Assistance**
   - Ask the new developer to work with you through these steps
   - Consider having a brief meeting to walk through the process together

2. **Seek Technical Help**
   - If you encounter any issues, technical support is available
   - The new developer should also be able to help troubleshoot access issues

## Conclusion

While this might seem like a lot of information, each step is straightforward and designed to be manageable even for non-technical users. The most important thing is ensuring that someone qualified has access to maintain the system.

If you have any questions or concerns about any of these steps, please don't hesitate to reach out for assistance. The goal is to make this transition as smooth as possible for everyone involved.

Remember, you're not alone in this process - the new developer will be able to help with the technical aspects, and support is available if needed.