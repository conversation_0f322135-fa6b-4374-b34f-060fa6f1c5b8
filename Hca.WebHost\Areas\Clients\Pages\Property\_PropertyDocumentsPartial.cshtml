﻿@using Hca.Lib.Services.QRFileSpot
@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">INSPECTION REPORTS</small>
    </div>
    <div class="card-body">
        @if (Model.Table1Documents.Any())
        {
            <div class="fixed-header-table" style="max-height: 500px;">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th style="width: 20px;"></th>
                            <th style="width: 120px; text-align: center;">Document Date</th>
                            <th style="width: 200px;">Document Type</th>
                            <th>Company</th>
                            <th>Notes</th>
                            <th style="width: 140px; text-align: center;">QR Codes</th>
                            <th style="width: 60px"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var document in Model.Table1Documents)
                        {
                            var url = await Model.GetDocumentUrl(document);

                            <tr style="cursor: pointer; vertical-align: middle;">
                                <td style="vertical-align: middle;">
                                    <a class="text-muted mr-1" href="@url" title="Download"><em class="fa fa-download fa-fw"></em></a>
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 100px; vertical-align: middle; text-align: center;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.DocumentDate.ToString("dd-MM-yyyy")
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.PropertyDocumentType.GetDisplayName()
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 100px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.CompanyName
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.Notes
                                </td>
                                <td style="text-align: center">
                                    <qrcode action="@Url.PageLink(pageHandler: "CreateQrCode", values: new { documentId = document.Id })"
                                            image-url="@QrFileSpotConfig.GetDocumentQrCodeImageUrl(document.QrCodeId)"></qrcode>
                                </td>
                                <td style="text-align: center">
                                    <delete action="@Urls.ClientPropertyDocumentDelete(Model.Client.Id, Model.Property.Id, document.Id)" item-name="document"></delete>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="col">
                <p>No documents uploaded</p>
            </div>
        }
    </div>
</div>

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">OTHER DOCUMENTS</small>
    </div>
    <div class="card-body">
        @if (Model.Table2Documents.Any())
        {
            <div class="fixed-header-table" style="max-height: 500px;">
                <table class="table table-striped table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th style="width: 20px;"></th>
                            <th style="width: 120px;">Document Date</th>
                            <th style="width: 200px;">Document Type</th>
                            <th>Company</th>
                            <th>Notes</th>
                            <th style="width: 20px;"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var document in Model.Table2Documents)
                        {
                            var url = await Model.GetDocumentUrl(document);

                            <tr style="cursor: pointer;">
                                <td style="vertical-align: middle;">
                                    <a class="text-muted mr-1" href="@url" title="Download"><em class="fa fa-download fa-fw"></em></a>
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 100px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.DocumentDate.ToString("dd-MM-yyyy")
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.PropertyDocumentType.GetDisplayName()
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 100px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.CompanyName
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px; vertical-align: middle;"
                                    onclick="window.location.href='@Urls.ClientPropertyDocument(Model.Client.Id, Model.Property.Id, document.Id).AddEditMode()';">
                                    @document.Notes
                                </td>
                                <td style="align-items: center; display: inline-flex;">
                                    <qrcode action="@Url.PageLink(pageHandler: "CreateQrCode", values: new { documentId = document.Id })"
                                            image-url="@QrFileSpotConfig.GetDocumentQrCodeImageUrl(document.QrCodeId)"></qrcode>
                                    <delete action="@Urls.ClientPropertyDocumentDelete(Model.Client.Id, Model.Property.Id, document.Id)" item-name="document" />
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="col">
                <p>No documents uploaded</p>
            </div>
        }
    </div>
</div>