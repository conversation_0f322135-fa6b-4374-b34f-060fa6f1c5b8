﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetInspections : PagedQuery<InspectionWithDetailsDto>
    {
        public GetInspections(int? page = 1, int? pageSize = 24) : base(page, pageSize)
        {
        }
    }

    public class GetInspectionsHandler : DapperPagedQueryHandler<GetInspections, InspectionWithDetailsDto>
    {
        public GetInspectionsHandler(IDbHelper dbHelper) : base(dbHelper) { }
            
        protected override (string sql, object args) Build(GetInspections request)
        {
            var sql = @$"
SELECT tblInspections.*, {nameof(ClientDto.ClientName)}, {nameof(PropertyDtoExtended.PropertyCode)}
FROM tblInspections
JOIN tblProperties ON tblInspections.PropertyId = tblProperties.Id
JOIN tblClients ON tblProperties.ClientId = tblClients.Id
ORDER BY Created DESC

SELECT COUNT(*)
FROM tblInspections";

            return (sql, new { });
        }
    }
}
