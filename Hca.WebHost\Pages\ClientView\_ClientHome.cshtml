﻿@*
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="row" id="divLocations">
    <div class="col">
        <div class="card card-default">
            <div class="card-header">
                <small class="text-muted">PROPERTIES</small>
            </div>
            <div class="card-body">
                <div class="row" id="divLocationFilter" style="display: none;">
                    <div class="col">
                        <div class="form-group mb-4">
                            <input class="form-control mb-2" type="text" placeholder="Filter Properties" id="txtFilterLocations">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <partial name="_PropertyListPartial" model="Model.Properties" />
                        <br />
                        <form method="post"><input asp-for="ShowArchivedProperties" onclick="this.form.submit()" /> Show Archived Properties</form>
                        @if (Model.ShowArchivedProperties)
                        {
                            <br />
                            <partial name="_PropertyListPartial" model="Model.ArchivedProperties" />
                        }
                    </div>
                    <div class="col-lg-6">
                        <partial name="_Map" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>*@

                        Client home page