﻿@page "/InspectionValues/Quantity/{id:guid}"
@model Hca.WebHost.Areas.InspectionValues.Pages.Quantity.Edit
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Quantity Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Quantity",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />