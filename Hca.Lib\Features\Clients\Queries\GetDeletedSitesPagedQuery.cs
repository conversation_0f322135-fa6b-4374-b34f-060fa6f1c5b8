﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetDeletedSitesPagedQuery : PagedQuery<DeletedSiteModel>
    {
        // todo: generic paged query (+ handler?)
        public GetDeletedSitesPagedQuery(
            int? pageNum = 1, 
            int? pageSize = 20) : base(pageNum, pageSize)
        {
        }
    }

    public class GetArchivedSitesPagedQueryHandler : DapperRequestHandler<GetDeletedSitesPagedQuery, PagedDtoSet<DeletedSiteModel>>
    {
        public GetArchivedSitesPagedQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<DeletedSiteModel>> OnHandleAsync(IDbHelper db, GetDeletedSitesPagedQuery request)
        {
            var sql = @"
SELECT tblSites.Id, SiteName, tblClients.Id AS ClientId, ClientName
FROM tblSites 
JOIN tblClients ON tblSites.ClientId = tblClients.Id
WHERE tblSites.Deleted IS NOT NULL 
ORDER BY ClientName
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblSites 
WHERE tblSites.Deleted IS NOT NULL";

            var (Items, Total) = await db.QueryPageAsync<DeletedSiteModel>(sql, request.Page, request.PageSize);

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
