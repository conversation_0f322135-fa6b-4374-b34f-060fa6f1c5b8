﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Commands;
using MediatR;

namespace Hca.Lib.Features.Inspections.Commands
{
    public class CreateInspectionReport : ICommand
    {
        public CreateInspectionReport(
            Guid documentId,
            Guid inspectionId)
        {
            DocumentId = documentId;
            InspectionId = inspectionId;
        }

        public Guid DocumentId { get; }
        public Guid InspectionId { get; }
    }

    public class CreateInspectionDocumentHandler : DapperRequestHandler<CreateInspectionReport, CommandResult>
    {
        private readonly IMediator _mediator;

        public CreateInspectionDocumentHandler(<PERSON>b<PERSON><PERSON><PERSON> dbH<PERSON>per, IMediator mediator) : base(dbHelper) {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateInspectionReport request)
        {
            await _mediator.Send(new CreateDocument(request.DocumentId, null));

            await db.InsertAsync(new InspectionReportDto
            {
                Id = request.DocumentId,
                InspectionId = request.InspectionId,
                DocumentId = request.DocumentId,
            });

            return CommandResult.Success();
        }
    }
}
