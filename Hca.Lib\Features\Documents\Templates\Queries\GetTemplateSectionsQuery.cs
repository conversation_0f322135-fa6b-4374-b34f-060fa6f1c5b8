﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateSectionsQuery : IQueryMany<TemplateSectionDto>
    {
        public GetTemplateSectionsQuery(Guid templateId)
        {
            TemplateId = templateId;
        }

        public Guid TemplateId { get; }
    }

    public class GetTemplateSectionsQueryHandler : DapperRequestHandler<GetTemplateSectionsQuery, DtoSet<TemplateSectionDto>>
    {
        public GetTemplateSectionsQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<TemplateSectionDto>> OnHandleAsync(IDbHelper db, GetTemplateSectionsQuery request)
        {
            return DtoSet.From(await db.GetListAsync<TemplateSectionDto>(
                "WHERE TemplateId = @TemplateId " +
                "ORDER BY SectionOrder ASC",
                new { request.TemplateId }));
        }
    }
}
