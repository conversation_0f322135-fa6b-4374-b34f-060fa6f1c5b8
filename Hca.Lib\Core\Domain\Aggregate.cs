﻿using System;
using System.Collections.Generic;

namespace Hca.Lib.Core
{

    public abstract class Aggregate : Entity, IAggregate
    {
        protected readonly List<AggregateEvent> Changes = new();

        protected Aggregate(EntityId id) : base(id) { }

        protected void Apply(AggregateEvent @event)
        {
            if (@event == null) throw new ArgumentNullException(nameof(@event));

            // first apply the event across all handlers in the aggregate
            Route(@event);

            // then queue it up to be advertised post-commit
            Changes.Add(@event);
        }

        public IEnumerable<AggregateEvent> GetChanges() => Changes;

        public void ClearChanges() => Changes.Clear();

        public void Execute<TAgg>(AggregateCommand<TAgg> command) where TAgg : IAggregate
        {
            Route(command);
        }
    }
}
