﻿using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;

namespace Hca.Lib.Features.Documents.Fields
{
    public class ChoiceField : Field
    {
        private static readonly string MINCHOICES = "minchoices";
        private static readonly string MAXCHOICES = "maxchoices";
        private static readonly string CHOICES = "choices";

        internal ChoiceField(int fieldOrder, bool isOptional, string serialisedContent) :
            base(FieldType.Choice, fieldOrder, isOptional, serialisedContent)
        { }

        internal ChoiceField(
            IEnumerable<Choice> choices,
            int? maxChoices = null,
            int? minChoices = null,
            string hint = null) : base(FieldType.Choice, minChoices == 0, hint)
        {
            _fieldContent.Add(CHOICES, JToken.FromObject(choices));
            if (minChoices.HasValue) _fieldContent.Add(MINCHOICES, JToken.FromObject(minChoices));
            if (maxChoices.HasValue) _fieldContent.Add(MAXCHOICES, JToken.FromObject(maxChoices));
        }

        public ChoiceList Choices
        {
            get => (_fieldContent[CHOICES] ?? new JArray()).ToObject<ChoiceList>();
            set
            {
                if (!_fieldContent.TryAdd(CHOICES, JToken.FromObject(value)))
                {
                    _fieldContent.Remove(CHOICES);
                    _fieldContent.Add(CHOICES, JToken.FromObject(value));
                }
            }
        }

        public int? MaxChoices
        {
            get => _fieldContent.GetValue(MAXCHOICES)?.ToObject<int?>();
            set
            {
                if (!_fieldContent.TryAdd(MAXCHOICES, JToken.FromObject(value ?? 0)))
                {
                    _fieldContent.Remove(MAXCHOICES);
                    _fieldContent.Add(MAXCHOICES, JToken.FromObject(value ?? 0));
                }
            }
        }

        public int? MinChoices { get => _fieldContent.GetValue(MINCHOICES)?.ToObject<int?>();
            set
            {
                if (!_fieldContent.TryAdd(MINCHOICES, JToken.FromObject(value ?? 0)))
                {
                    _fieldContent.Remove(MINCHOICES);
                    _fieldContent.Add(MINCHOICES, JToken.FromObject(value ?? 0));
                }
            }
        }
    }

    public class ChoiceList : List<Choice>
    {
        public ChoiceList() { }

        public ChoiceList(IEnumerable<Choice> choices) : base(choices) { }

        public override string ToString()
        {
            return string.Join(Environment.NewLine, this.Select(choice => $"{choice.Hint},{choice.Text}"));
        }
    }

    public static class ChoiceListExtensions
    {
        public static ChoiceList ToList(this IEnumerable<Choice> choices) => new (choices);

        public static ChoiceList ToChoiceList(this string from) =>
            from.Split(Environment.NewLine).Select(t => Choice.Create(t.Split(',')[0].Trim(), t.Split(',')[1].Trim())).ToList();
    }

    public class Choice
    {
        public Guid Id { get; set; } // not a database key

        public string Text { get; set; }

        public string Hint { get; set; }

        public bool Chosen { get; set; }

        public static Choice Create(string text, string hint)
        {
            return new Choice
            {
                Text = text,
                Hint = hint,
            };
        }
    }
}
