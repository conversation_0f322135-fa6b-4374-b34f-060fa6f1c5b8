﻿@page "/inspections/{inspectionId:guid}/samples/{sampleId:guid}/details/{handler?}"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.SampleDetailsEditModel
@{
    Layout = null;
}

<link href="~/Vendor/x-editable/dist/bootstrap3-editable/css/bootstrap-editable.css" rel="stylesheet" />

<script src="~/Vendor/x-editable/dist/bootstrap3-editable/js/bootstrap-editable.js"></script>

<div class="form-group">
    <label>Sample Reference</label>
    <a id="lnkSampleReference" href="#" data-type="text">@Model.Sample.SampleReference</a>
</div>
<div class="form-group">
    <label style="vertical-align: top;">Sample Notes</label>
    <a id="lnkSampleNotes" href="#" data-type="textarea">@Model.Sample.SampleNotes</a>
</div>

<script>
    $('#lnkSampleReference').editable({
        mode: 'inline',
        success: async (response, newValue) => {
            let [result, err1] = await to($.ajax({
                type: 'POST',
                url: '/inspections/@Model.InspectionId/samples/@Model.Sample.Id/details/SampleReference',
                dataType: 'json',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify(newValue)
            }));

            loadSamples('@Model.Sample.Id');
        }
    });

    $('#lnkSampleNotes').editable({
        mode: 'inline',
        success: async (response, newValue) => {
            let [result, err1] = await to($.ajax({
                type: 'POST',
                url: '/inspections/@Model.InspectionId/samples/@Model.Sample.Id/details/SampleNotes',
                dataType: 'json',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify(newValue)
            }));

            loadSamples('@Model.Sample.Id');
        }
    });
</script>