﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class ArchiveSite : ICommand
    {
        public Guid SiteId { get; }
        public string ArchiveReason { get; }
        public DateTime? Archived { get; }

        public ArchiveSite(Guid siteId, string archiveReason, DateTime? archived = null)
        {
            SiteId = siteId;
            ArchiveReason = archiveReason;
            Archived = archived ?? DateTime.UtcNow;
        }
    }

    public class ArchiveSiteHandler : DapperRequestHandler<ArchiveSite, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;

        public ArchiveSiteHandler(IDb<PERSON><PERSON><PERSON> dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, ArchiveSite request)
        {
            var site = await db.GetAsync<SiteDto>(request.SiteId);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET " +
                $"{nameof(SiteDto.Archived)}=@{nameof(request.Archived)}, " +
                $"{nameof(SiteDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
                $"WHERE {nameof(SiteDto.Id)}=@{nameof(request.SiteId)}", request);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET " +
                $"{nameof(PropertyDto.Archived)}=@{nameof(request.Archived)}, " +
                $"{nameof(PropertyDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
                $"WHERE {nameof(PropertyDto.SiteId)}=@{nameof(request.SiteId)} " +
                $"AND {nameof(PropertyDto.Archived)} IS NULL", request);

            _clientCountsService.ClearClientCountsAsync(site.ClientId);

            return CommandResult.Success();
        }
    }
}
