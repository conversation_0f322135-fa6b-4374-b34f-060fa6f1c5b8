﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Domain.Commands;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.Lib.Identity;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientContactModel : HcaPageModel
{
    private readonly ClientService _clientService;
    private readonly IPasswordHasher<HcaUser> _passwordHasher;

    public ClientContactModel(
        ClientService clientService,
        IPasswordHasher<HcaUser> passwordHasher,
        IMediator mediator,
        ViewManager viewManager) : base(mediator, viewManager)
    {
        _clientService = clientService;
        _passwordHasher = passwordHasher;
    }

    [BindProperty]
    [Required]
    public ContactDto Contact { get; set; } = new ContactDto();

    [BindProperty]
    public bool CanLogin { get; set; }

    public string NewPassword { get; private set; }
    public string Id { get; private set; }
    public ClientDto Client { get; private set; }
    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (Client == null) return NotFound();

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (Guid.TryParse(id, out var contactId))
        {
            Contact = (await _clientService.GetContactsAsync(clientId, cancellationToken)).Items.Single(c => c.Id == contactId);
            CanLogin = !string.IsNullOrWhiteSpace(Contact.Password);
        }
        else
        {
            Contact = new ContactDto { Id = Guid.NewGuid() };
        }

        return Page();
    }


    public async Task<IActionResult> OnPostAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (Client == null) return NotFound();

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        CommandResult result;

        if (id.ToLower() == "new")
        {
            result = await _mediator.Send(new AddContactToClient(clientId, Contact), cancellationToken);

            if (CanLogin)
            {
                NewPassword = Guid.NewGuid().ToString().Split('-').First();

                await _clientService.EnableClientContactLoginAsync(
                    Contact.Id,
                    _passwordHasher.HashPassword(null, NewPassword),
                    cancellationToken);
            }
        }
        else
        {
            result = await _mediator.Send(new UpdateClientContact(clientId, Contact), cancellationToken);

            if (!CanLogin)
            {
                await _clientService.DisableClientContactLoginAsync(Contact.Id, cancellationToken);
            }
            else if (Contact.Password.IsNullOrEmpty())
            {
                NewPassword = Guid.NewGuid().ToString().Split('-').First();

                await _clientService.EnableClientContactLoginAsync(
                    Contact.Id,
                    _passwordHasher.HashPassword(null, NewPassword),
                    cancellationToken);
            }
        }

        if (!result.IsSuccess)
        {
            ErrorMessage = result.Reason;
            return Page();
        }

        return string.IsNullOrWhiteSpace(NewPassword) ? Redirect(Urls.ClientContact(clientId, Contact.Id)) : Page();
    }

    public async Task<IActionResult> OnPostDeleteContactAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        await _clientService.DeleteContactAsync(Guid.Parse(id), cancellationToken);
        return Redirect(Urls.ClientContacts(clientId));
    }
}
