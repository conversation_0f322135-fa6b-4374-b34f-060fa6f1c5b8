﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewProperty : IQuery<ValidationResult>
{
    public CanContactViewProperty(Guid propertyId, Guid contactId)
    {
        PropertyId = propertyId;
        ContactId = contactId;
    }

    public Guid PropertyId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewPropertyHandler : DapperRequestHandler<CanContactViewProperty, ValidationResult>
{
    public CanContactViewPropertyHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewProperty request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.Properties} " +
            $"JOIN {TableNames.Contacts} ON {TableNames.Contacts}.{nameof(ContactDto.ClientId)} = {TableNames.Properties}.{nameof(PropertyDto.ClientId)} " +
            $"WHERE {TableNames.Properties}.{nameof(PropertyDto.Id)} = @{nameof(request.PropertyId)} " +
            $"AND {TableNames.Contacts}.{nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}
