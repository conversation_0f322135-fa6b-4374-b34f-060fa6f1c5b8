﻿using System;

namespace Hca.Lib.Features.Documents.Fields
{
    public class DocumentTextField : DocumentField<TextField>
    {
        internal DocumentTextField(Guid documentFieldId, int fieldOrder, bool isOptional, string serialisedContent) : base(documentFieldId) {
            Value = new TextField(fieldOrder, isOptional, serialisedContent);
        }

        protected DocumentTextField(string hint = null, bool isOptional = false) : this(Guid.NewGuid(), hint, isOptional) { }

        protected DocumentTextField(
            Guid documentFieldId,
            string hint = null,
            bool isOptional = false) : base(documentFieldId)
        {
            Value = new TextField(hint, isOptional);
        }

        protected DocumentTextField(
            Guid documentFieldId,
            string placeholder,
            bool isHtml,
            string hint = null,
            bool isOptional = false) : base(documentFieldId)
        {
            Value = new TextField(placeholder, isHtml, hint, isOptional);
        }
    }
}
