﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class SetInspectionSampleNotes : InspectionCommand
    {
        public SetInspectionSampleNotes(
            Guid inspectionId,
            Guid sampleId,
            string sampleNotes) : base(inspectionId)
        {
            SampleId = sampleId;
            SampleNotes = sampleNotes;
        }

        public Guid SampleId { get; }
        public string SampleNotes { get; }
    }

    public class SetInspectionNotesTemplateHandler : DapperRequestHandler<SetInspectionSampleNotes, CommandResult>
    {
        public SetInspectionNotesTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetInspectionSampleNotes request)
        {
            var sample = await db.GetAsync<InspectionSampleDto>(request.SampleId);
            sample.SampleNotes = request.SampleNotes;
            await db.UpdateAsync(sample);

            return CommandResult.Success();
        }
    }
}
