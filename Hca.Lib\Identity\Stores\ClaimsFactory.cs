﻿using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using System;
using System.Diagnostics;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Hca.Lib.Identity.Stores;

public class ClaimsFactory : UserClaimsPrincipalFactory<HcaUser>
{
    public ClaimsFactory(
        UserManager<HcaUser> userManager,
        IOptions<IdentityOptions> optionsAccessor) : base(userManager, optionsAccessor)
    {

    }

    protected override async Task<ClaimsIdentity> GenerateClaimsAsync(HcaUser user)
    {
        Debug.WriteLine("Claims generated");

        var identityTask = await base.GenerateClaimsAsync(user);
        if (!Guid.TryParse(user.Id, out var userGuid)) return identityTask;

        var role = await UserManager.GetRolesAsync(user);

        if (role.Contains(HcaRole.ROLE_CLIENT))
        {
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERACCESS, HcaClaimValues.CLIENT));
            identityTask.AddClaim(new Claim(HcaClaimTypes.CLIENTID, user.ClientId.ToString()));
            identityTask.AddClaim(new Claim(HcaClaimTypes.CONTACTID, user.Id));
        }

        else if (role.Contains(HcaRole.ROLE_HCA))
        {
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERACCESS, HcaClaimValues.HCA));
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERID, user.Id));
        }

        else if (role.Contains(HcaRole.ROLE_ADMIN))
        {
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERACCESS, HcaClaimValues.HCA));
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERACCESS, HcaClaimValues.ADMIN));
            identityTask.AddClaim(new Claim(HcaClaimTypes.USERID, user.Id));
        }

        foreach(var permission in user.Permissions)
        {
            identityTask.AddClaim(new Claim("Permission", permission));
        }

        return identityTask;
    }
}
