﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Events;
using Hca.Lib.Features.Documents.Fields;
using MediatR;

namespace Hca.Lib.Features.Documents.Commands
{
    public class SetDocumentTextField : IRequest<bool>
    {
        public SetDocumentTextField(
            Guid documentId,
            Guid sectionId,
            Guid fieldId,
            string actual)
        {
            DocumentId = documentId;
            SectionId = sectionId;
            FieldId = fieldId;
            Actual = actual;
        }

        public Guid DocumentId { get; }
        public Guid SectionId { get; }
        public Guid FieldId { get; }
        public string Actual { get; }
    }

    public class SetDocumentTextFieldHandler : <PERSON><PERSON><PERSON>equestHandler<SetDocumentTextField, bool>
    {
        private readonly IMediator _mediator;
        public SetDocumentTextFieldHandler(IMediator mediator, IDbHelper dbHelper) : base(dbHelper) { _mediator = mediator; }

        public override async Task<bool> OnHandleAsync(IDbHelper db, SetDocumentTextField request)
        {
            var dto = await db.GetAsync<DocumentFieldDto>(request.FieldId);
            var field = new DocumentTextField(dto.Id, dto.FieldOrder, dto.IsOptional, dto.FieldContent)
            {
                IsComplete = true
            };
            field.Value.Actual = request.Actual;

            dto.FieldContent = field.Value.FieldContent;

            await db.UpdateAsync(dto);
            await _mediator.Publish(new DocumentFieldCompleted(request.DocumentId, request.SectionId, request.FieldId));

            return true;
        }
    }
}
