﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;

namespace Hca.WebHost.Areas.InspectionValues.Pages.FloorPlanColours;

public class EditModel : InspectionValueEditPage
{
    public EditModel(
        ValueListsService inspectionValuesService,
        ViewManager viewManager,
        IMediator mediator) : base(inspectionValuesService, mediator, viewManager) { }

    public override ValueListType ValueListType => ValueListType.FloorPlanColours;

    public override string IndexUrl => Urls.FloorPlanColours;
}
