﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetAllClientsPagedQuery : PagedQuery<ClientDto>
    {
        // todo: generic paged query (+ handler?)
        public GetAllClientsPagedQuery(
            int? pageNum = 1, 
            int? pageSize = 20, 
            bool isArchived = false, 
            bool isDeleted = false) : base(pageNum, pageSize)
        {
            IsArchived = isArchived;
            IsDeleted = isDeleted;
        }

        public bool IsArchived { get; }
        public bool IsDeleted { get; }
    }

    public class GetAllClientsPagedQueryHandler : DapperRequestHandler<GetAllClientsPagedQuery, PagedDtoSet<ClientDto>>
    {
        public GetAllClientsPagedQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<ClientDto>> OnHandleAsync(IDbHelper db, GetAllClientsPagedQuery request)
        {
            var sql = @"
SELECT *
FROM tblClients " +
$"WHERE Archived IS {(request.IsArchived ? "NOT NULL" : "NULL")} " +
$"AND Deleted IS {(request.IsDeleted ? "NOT NULL" : "NULL")} " +
@"ORDER BY ClientName
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblClients " +
$"WHERE Archived IS {(request.IsArchived ? "NOT NULL" : "NULL")} " +
$"AND Deleted IS {(request.IsDeleted ? "NOT NULL" : "NULL")}";

            var (Items, Total) = await db.QueryPageAsync<ClientDto>(sql, request.Page, request.PageSize, new { request.IsArchived, request.IsDeleted });

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
