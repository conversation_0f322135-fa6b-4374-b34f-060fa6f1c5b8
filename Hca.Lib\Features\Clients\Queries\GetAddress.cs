﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetAddress : IQuery<AddressDto>
    {
        public GetAddress(Guid addressId)
        {
            AddressId = addressId;
        }

        public Guid AddressId { get; }
    }

    public class GetAddressHandler : DapperRequestHandler<GetAddress, AddressDto>
    {
        public GetAddressHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<AddressDto> OnHandleAsync(IDbHelper db, GetAddress request) => db.GetAsync<AddressDto>(request.AddressId);
    }
}
