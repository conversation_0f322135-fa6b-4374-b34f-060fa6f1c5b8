﻿@page "/clients/{clientId:guid}/projects/{projectId:guid}/inspections/{id}"
@using Lib.Features.Documents
@using Lib.Features.Clients
@model Hca.WebHost.Areas.Inspections.Pages.InspectionModel
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    Layout = Model.IsNew ? "~/Areas/Projects/Pages/_LayoutProject.cshtml" : "_LayoutHorizontal";
    ViewData["Title"] = Model.Client.ClientName;
    ViewData["Client"] = Model.Client;
    ViewData["Project"] = Model.Project;
}

<div class="row">

    @if (Model.IsNew)
    {
        <div class="col">
            <form method="post">
                <div class="card card-default">
                    <div class="card-header">
                        <small class="text-muted">NEW INSPECTION</small>
                    </div>
                    <div class="card-body">
                        <div class="col">
                            <input type="hidden" asp-for="Inspection.Id" />

                            <select class="chosen-select form-control invert-text"
                                    aria-label="Inspection Type"
                                    aria-describedby="inspection-type"
                                    asp-for="Inspection.InspectionTypeId"
                                    row-label="Inspection Type">
                                <option value="">Select</option>
                                @foreach (var inspectionType in Model.InspectionTypes)
                                {
                                    <option title="@inspectionType.HintText"
                                            value="@inspectionType.Id">
                                        @inspectionType.DisplayText
                                    </option>
                                }
                            </select>

                            <select class="chosen-select form-control invert-text"
                                    id="ddlProperty"
                                    aria-label="Property"
                                    aria-describedby="property"
                                    asp-for="Inspection.PropertyId"
                                    row-label="Property">
                                <option value="">Select</option>
                                @foreach (var property in Model.Properties.OrderBy(p => p.GetDisplayText()))
                                {
                                    <option value="@property.Id">
                                        @property.GetDisplayText()
                                    </option>
                                }
                            </select>
                        </div>
                    </div>
                    <save-cancel-footer EditButton="false"
                                        CancelUrl="@Urls.ClientProject(Model.Client.Id, Model.Project.Id)"></save-cancel-footer>
                </div>
            </form>
        </div>
    }
    else
    {
        <div class="col-xl-3 col-lg-4">
            <!-- START report sections-->
            <div class="collapse show">
                <div class="card card-default">
                    <div class="card-body">
                        <ul class="nav nav-pills flex-column">
                            <li class="nav-item p-2">
                                <small class="text-muted">REPORT SECTIONS</small>
                            </li>
                            <li>
                                <div id="reportSectionAccordion">
                                    @foreach (var row in (Model.ReportSections ?? Enumerable.Empty<DocumentSectionDto>()).Select((section, index) => (section, index)))
                                    {
                                        <div class="card b0 mb-2">
                                            <div class="card-header">
                                                <h4 class="card-title">
                                                    <a class="text-inherit" data-toggle="collapse" data-parent="#reportSectionAccordion" href="#repacccollapse@(row.index)">
                                                        <small>
                                                            <em class="fa fa-plus text-primary mr-2"></em>
                                                        </small>
                                                        <span>@row.section.SectionTitle</span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div class="collapse" id="repacccollapse@(row.index)">
                                                <div class="card-body">
                                                    <p>
                                                        Donec congue sagittis mi sit amet tincidunt. Quisque sollicitudin massa vel erat tincidunt blandit. Curabitur quis leo nulla. Phasellus faucibus placerat luctus. Integer fermentum molestie massa at congue. Quisque quis quam dictum diam
                                                        volutpat adipiscing.
                                                    </p>
                                                    <p>Proin ut urna enim.</p>
                                                    <div class="text-right">
                                                        <small class="text-muted mr-2">Was this information useful?</small>
                                                        <button class="btn btn-secondary btn-xs" type="button">
                                                            <em class="fa fa-thumbs-up text-muted"></em>
                                                        </button>
                                                        <button class="btn btn-secondary btn-xs" type="button">
                                                            <em class="fa fa-thumbs-down text-muted"></em>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- START menu-->
            @if (await ViewManager.IsAdminUser)
            {
                <div class="collapse show">
                    <div class="card card-default">
                        <div class="card-body">
                            <ul class="nav nav-pills flex-column">
                                <li class="nav-item p-2">
                                    <small class="text-muted">ACTIONS</small>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link d-flex btn btn-success"
                                       style="width: 100%; margin: 5px 0;"
                                       href="@Urls.UniqueInspectionRecordNew(Model.Inspection.Id)">New UIR</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            }
            <!-- END menu-->
        </div>

        <div class="col-xl-9 col-lg-8">
            <div class="row">
                <div class="col">
                    <div class="card card-default">
                        <div class="card-body">
                            This is an inspection
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

</div>

@section scripts {
    <script>

        $(() => {
            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientProjects(Model.Client.Id)', text: 'Projects' },
                { url: '@Urls.ClientProject(Model.Client.Id, Model.Project.Id)', text: '@Model.Project.ProjectNumber' },
                { url: '@Urls.ClientProjectInspections(Model.Client.Id, Model.Project.Id)', text: 'Inspections' },
                { text: '@(Model.Inspection?.PropertyCode ?? "New")' }]);
        });

    </script>
}
