﻿using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace Hca.Lib.Features.Documents.Fields
{
    public abstract class Field
    {
        protected static readonly string TYPE = "$type";
        protected static readonly string HINT = "hint";

        protected Field(FieldType fieldType, int fieldOrder, bool isOptional, string serialisedContent)
        {
            FieldType = fieldType;
            FieldOrder = fieldOrder;
            IsOptional = isOptional;
            FieldContent = serialisedContent;
        }

        protected Field(FieldType fieldType, bool isOptional = false, string hint = null)
        {
            FieldType = fieldType;
            if (hint != null) _fieldContent.Add(HINT, hint);
            IsOptional = isOptional;
        }

        public FieldType FieldType { get; private set; }

        protected JObject _fieldContent = new();
        public string FieldContent { get => _fieldContent.ToString(); private set => _fieldContent = JObject.Parse(value); }

        public int FieldOrder { get; set; }

        public bool IsOptional { get; set; }

        public string Hint
        {
            get => _fieldContent[HINT]?.ToString();
            set
            {
                if (!_fieldContent.TryAdd(HINT, JToken.FromObject(value ?? "")))
                {
                    _fieldContent.Remove(HINT);
                    _fieldContent.Add(HINT, JToken.FromObject(value ?? ""));
                }
            }
        }

    }
}
