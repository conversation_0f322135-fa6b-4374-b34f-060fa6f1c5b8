﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class RemovePropertyAddress : ICommand
    {
        public RemovePropertyAddress(
            Guid propertyId)
        {
            PropertyId = propertyId;
        }

        public Guid PropertyId { get; }
    }

    public class RemovePropertyAddressHandler : DapperRequestHandler<RemovePropertyAddress, CommandResult>
    {
        public RemovePropertyAddressHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, RemovePropertyAddress request)
        {
            var locationDto = await db.GetAsync<PropertyDtoExtended>(request.PropertyId);

            if (locationDto.AddressId.HasValue)
            {
                await db.DeleteAsync<AddressDto>(locationDto.AddressId.Value);
                locationDto.AddressId = null;
                await db.UpdateAsync(locationDto);
            }

            return CommandResult.Success();
        }
    }
}
