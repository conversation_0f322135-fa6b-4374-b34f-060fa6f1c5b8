﻿using System.Net;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("img", Attributes = "row-label")]
    [HtmlTargetElement("p", Attributes = "row-label")]
    public class FormRowTagHelper : TagHelper
    {
        [HtmlAttributeName("row-label")]
        public string Hca<PERSON>abel { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            base.Process(context, output);

            var preContent = $"<div class=\"form-group row\">" +
                $"<label class=\"col-xl-2 col-form-label\">{WebUtility.HtmlEncode(HcaLabel)}</label>" +
                $"<div class=\"col-xl-10\">";
            var postContent = "</div></div>";

            output.PreElement.SetHtmlContent(preContent);

            output.PostElement.SetHtmlContent(postContent);
        }
    }
}
