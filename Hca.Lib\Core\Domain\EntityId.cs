﻿using System;
using System.Collections.Generic;

namespace Hca.Lib.Core
{
    public class EntityId : ValueObject
    {
        public Guid Id { get; private set; }

        public EntityId()
        {
            Id = Guid.NewGuid();
        }

        public EntityId(Guid id)
        {
            Id = id;
        }

        public bool HasValue { get { return Id != Guid.Empty; } }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Id;
        }

        public static implicit operator Guid(EntityId from)
        {
            return from.Id;
        }

        public static implicit operator EntityId(Guid from)
        {
            return new EntityId(from);
        }

        public override string ToString()
        {
            return Id.ToString();
        }
    }
}
