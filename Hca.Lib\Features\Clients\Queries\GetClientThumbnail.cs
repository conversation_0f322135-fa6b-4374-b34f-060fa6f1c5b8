﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core.Queries;
using Hca.Lib.Services;
using MediatR;
using SixLabors.ImageSharp.Web.Resolvers.Azure;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientThumbnail : IQuery<byte[]>
    {
        public GetClientThumbnail(
            Guid clientId,
            string filename,
            int width,
            int height)
        {
            ClientId = clientId;
            Filename = filename;
            Width = width;
            Height = height;
        }

        public Guid ClientId { get; }
        public string Filename { get; }
        public int Width { get; }
        public int Height { get; }
    }

    public class GetClientThumbnailHandler : BlobRequestHandler<GetClientThumbnail, byte[]>
    {
        private readonly IMediator _mediator;

        public GetClientThumbnailHandler(IMediator mediator, BlobServiceClient blobClient)
            : base(blobClient, StorageConstants.ClientsContainerName)
        {
            _mediator = mediator;
        }

        public override async Task<byte[]> OnHandleAsync(
            BlobContainerClient client,
            GetClientThumbnail request,
            CancellationToken cancellationToken)
        {
            var resolver = new AzureBlobStorageImageResolver(client.GetBlobClient(request.Filename));
            using var stream = await resolver.OpenReadAsync();
            return await _mediator.Send(new GetImageJpegThumbnail(stream, request.Width, request.Height));
        }
    }
}
