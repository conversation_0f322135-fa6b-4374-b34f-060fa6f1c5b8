﻿@page "/inspections/{inspectionId:guid}/detailsstep"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.DetailsModel
@{
    Layout = null;
}
<div class="row">
    <div class="col-lg-4"></div>
    <div class="col-lg-8">

        <div class="card card-default">
            <div class="card-body">
                <fieldset>
                    <div class="form-group row">
                        <label class="col-md-2 col-form-label">Client</label>
                        <div class="col-md-10">
                            <p class="form-control-plaintext">@Model.Inspection.ClientName</p>
                        </div>
                    </div>
                </fieldset>
                <fieldset>
                    <div class="form-group row">
                        <label class="col-md-2 col-form-label">Location</label>
                        <div class="col-md-10">
                            <p class="form-control-plaintext">@Model.Inspection.PropertyCode</p>
                        </div>
                    </div>
                </fieldset>
                <fieldset>
                    <div class="form-group row">
                        <label class="col-md-2 col-form-label">Inspection Type</label>
                        <div class="col-md-10">
                            <p class="form-control-plaintext">@Model.Inspection.InspectionTypeId</p>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </div>
</div>