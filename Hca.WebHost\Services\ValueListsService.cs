﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using MediatR;

namespace Hca.WebHost.Services
{
    public class ValueListsService
    {
        private readonly IMediator _mediator;

        public ValueListsService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task<IEnumerable<ValueDto>> GetAllAsync(ValueListType type, CancellationToken cancellationToken) =>
            _mediator.Send(new GetValuesByType(type), cancellationToken)
            .ContinueWith(r =>
            {
                return r.Result.Items.OrderBy(v => v.Priority).AsEnumerable();
            });

        public Task<ValueDto> GetAsync(Guid valueId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetValue(valueId), cancellationToken);

        public Task CreateAsync(ValueDto value, CancellationToken cancellationToken)
        {
            if (value.ValueListType == 0)
            {
                throw new ArgumentException(nameof(value.ValueListType));
            }

            return _mediator.Send(new CreateValue(value), cancellationToken);
        }

        public Task UpdateAsync(ValueDto value, CancellationToken cancellationToken) =>
            _mediator.Send(new UpdateValue(value), cancellationToken);

        public Task DeleteAsync(Guid id, CancellationToken cancellationToken) =>
            _mediator.Send(new DeleteValue(id), cancellationToken);

        public Task UpdatePriorities(IEnumerable<ValueDto> values, CancellationToken cancellationToken) =>
            Task.WhenAll(values.Select(v => _mediator.Send(new UpdateValuePriority(v.Id, v.Priority.Value), cancellationToken)));
    }
}
