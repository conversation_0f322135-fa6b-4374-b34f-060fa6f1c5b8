﻿@page "/users"
@model Hca.WebHost.Areas.Identity.Pages.UsersModel
@inject Hca.WebHost.Services.UserService UserService
@{
    ViewData["Title"] = "Users";
}
<div class="card card-default">
    <div class="card-header">
        <h5>USERS</h5>
    </div>
    <div id="divUsers" class="card-body">
        <table class="table table-striped table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Email</th>
                    <th>Position</th>
                    <th>Office Phone</th>
                    <th>Mobile Phone</th>
                    <th>Admin</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var user in Model.Users)
                {
                    <tr>
                        <td>@user.FirstName</td>
                        <td>@user.LastName</td>
                        <td>@user.Email</td>
                        <td>@user.Position</td>
                        <td>@user.OfficePhone</td>
                        <td>@user.MobilePhone</td>
                        <td>
                            @if (user.Role == Hca.Lib.Features.Users.UserRole.Admin)
                            {
                                <input type="checkbox" checked disabled />
                            }
                            else
                            {
                                <input type="checkbox" disabled />
                            }
                        </td>
                        <td>
                            <edit action="@Urls.UserEdit(user.Id)"></edit>
                            <delete action="@Urls.UserDelete(user.Id)" item-name="user @user.FirstName, @user.LastName"></delete>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
        <a class="btn btn-success btn-add-user" href="@Urls.Users/New">New User</a>
    </div>
</div>


@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([{ url: '@Urls.Users', text: 'Users' }]);
        });</script>
}