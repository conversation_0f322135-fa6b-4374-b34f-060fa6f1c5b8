﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Users.Queries
{
    public class GetUserSignatureFile : IRequest<byte[]>
    {
        public GetUserSignatureFile(Guid userId)
        {
            UserId = userId;
        }

        public Guid UserId { get; }
    }

    public class GetUserSignatureFileHandler : DapperRequestHandler<GetUserSignatureFile, byte[]>
    {
        private readonly IMediator _mediator;

        public GetUserSignatureFileHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public async override Task<byte[]> OnHandleAsync(IDbHelper db, GetUserSignatureFile request)
        {
            var user = await db.GetAsync<UserDto>(request.UserId);
            return await _mediator.Send(new DownloadBlob("users", user.SignatureFileName));
        }
    }
}
