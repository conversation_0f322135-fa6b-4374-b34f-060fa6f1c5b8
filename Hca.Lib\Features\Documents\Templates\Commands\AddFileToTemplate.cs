﻿using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Templates.Commands
{
    public class AddFileToTemplate : TemplateCommand
    {
        public AddFileToTemplate(
            Guid templateId,
            string fileName,
            byte[] fileContents,
            string contentType) : base(templateId)
        {
            FileName = fileName;
            FileContents = fileContents;
            ContentType = contentType;
        }

        public string FileName { get; }

        public byte[] FileContents { get; }
        public string ContentType { get; }
    }

    public class AddFileToTemplateHandler : DapperRequestHandler<AddFileToTemplate, CommandResult>
    {
        private readonly IMediator _mediator;

        public AddFileToTemplateHandler(IMediator mediator, IDbHelper dbHelper) : base(dbHelper) { _mediator = mediator; }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddFileToTemplate request)
        {
            var template = await db.GetAsync<TemplateDto>(request.TemplateId);
            if (template == null) return CommandResult.Fail("Template does not exist");

            var uploadResult = await _mediator.Send(new UploadBlob(
                    StorageConstants.TemplateContainerName,
                    $"{request.TemplateId}/{request.FileName}",
                    request.FileContents,
                    request.ContentType
                ));

            if (uploadResult.IsSuccess)
            {
                var maxDisplayOrder = await db.ExecuteScalarAsync<int>(
                    "SELECT MAX(DisplayOrder) FROM tblTemplateFiles WHERE TemplateId=@TemplateId",
                    request);
                var newFile = new TemplateFileDto
                {
                    TemplateId = request.TemplateId,
                    FileName = request.FileName,
                    DisplayOrder = maxDisplayOrder + 1,
                    Id = Guid.NewGuid(),
                };
                await db.InsertAsync(newFile);
            }

            return CommandResult.Success();
        }
    }
}
