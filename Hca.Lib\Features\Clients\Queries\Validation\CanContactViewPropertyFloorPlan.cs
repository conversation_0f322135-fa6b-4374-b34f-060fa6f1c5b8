﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewPropertyFloorPlan : IQuery<ValidationResult>
{
    public CanContactViewPropertyFloorPlan(Guid propertyFloorPlanId, Guid contactId)
    {
        PropertyFloorPlanId = propertyFloorPlanId;
        ContactId = contactId;
    }

    public Guid PropertyFloorPlanId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewPropertyFloorPlanHandler : DapperRequestHandler<CanContactViewPropertyFloorPlan, ValidationResult>
{
    public CanContactViewPropertyFloorPlanHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewPropertyFloorPlan request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.PropertyFloorPlans} " +
            $"JOIN {TableNames.Properties} ON {TableNames.Properties}.{nameof(PropertyDto.Id)} = {TableNames.PropertyFloorPlans}.{nameof(FloorPlanDto.PropertyId)} " +
            $"JOIN {TableNames.Contacts} ON {TableNames.Contacts}.{nameof(ContactDto.ClientId)} = {TableNames.Properties}.{nameof(PropertyDto.ClientId)} " +
            $"WHERE {TableNames.PropertyFloorPlans}.{nameof(FloorPlanDto.Id)} = @{nameof(request.PropertyFloorPlanId)} " +
            $"AND {TableNames.Contacts}.{nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}