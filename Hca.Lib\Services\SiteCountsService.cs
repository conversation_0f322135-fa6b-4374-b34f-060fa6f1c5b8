﻿using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Models;
using LazyCache;
using MediatR;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Services;

public class SiteCountsService
{
    private readonly IMediator _mediator;
    private readonly IAppCache _appCache;

    public SiteCountsService(IAppCache appCache, IMediator mediator)
    {
        _appCache = appCache ?? throw new ArgumentNullException(nameof(appCache));
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    }

    public async Task<SiteCountsModel> GetSiteCountsAsync(Guid clientId, Guid siteId)
    {
        return await _appCache.GetOrAddAsync($"SiteCounts_{siteId}", async () =>
        {
            var siteCounts = await _mediator.Send(new GetSiteCounts(clientId, siteId));
            return siteCounts;
        }, DateTimeOffset.UtcNow.AddMinutes(10));
    }

    public void ClearSiteCountsAsync(Guid siteId)
    {
        _appCache.Remove($"SiteCounts_{siteId}");
    }
}
