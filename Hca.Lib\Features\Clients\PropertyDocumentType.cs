﻿using Dapper;
using System;
using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Hca.Lib.Features.Clients
{
    public enum PropertyDocumentType
    {
        [Display(Name = " ")]
        Unspecified = 0,

        [Display(Name = "Asbestos Demolition Survey")] AsbestosDemolitionSurvey = 110,
        [Display(Name = "Asbestos Management Survey")] AsbestosManagementSurvey = 120,
        [Display(Name = "Asbestos Reinspection")] AsbestosReinspection = 130,
        [Display(Name = "Asbestos Refurbishment Survey")] AsbestosRefurbishmentSurvey = 140,
        [Display(Name = "Asbestos Management Plan")] AsbestosManagementPlan = 145,
        [Display(Name = "Type 1 Asbestos Survey")] Type1AsbestosSurvey = 150,
        [Display(Name = "Type 2 Asbestos Survey")] Type2AsbestosSurvey = 160,
        [Display(Name = "Type 3 Asbestos Survey")] Type3AsbestosSurvey = 170,

        [Display(Name = "Air test certificate")] AirTestCertificate = 210,
        [Display(Name = "Asbestos remediation ASB5")] AsbestosRemediationASB5 = 220,
        [Display(Name = "Asbestos remediation NNLW1")] AsbestosRemediationNNLW1 = 230,
        [Display(Name = "Asbestos remediation RAMS")] AsbestosRemediationRAMS = 240,
        [Display(Name = "Four stage clearance certificate")] FourStageClearanceCertificate = 250,
        [Display(Name = "Sample analysis certificate")] SampleAnalysisCertificate = 260,
        [Display(Name = "Sampling report")] SamplingReport = 270,
        [Display(Name = "Waste consignment note")] WasteConsignmentNote = 280,
    }

    public class PropertyDocumentTypeConverter : JsonConverter<PropertyDocumentType>
    {
        public override PropertyDocumentType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
        {
            if (reader.TokenType == JsonTokenType.Number)
            {
                return (PropertyDocumentType)Enum.ToObject(typeof(PropertyDocumentType), reader.GetInt32());
            }

            if (reader.TokenType == JsonTokenType.String)
            {
                return (PropertyDocumentType)(Enum.Parse(typeof(PropertyDocumentType), reader.GetString()));
            }

            return PropertyDocumentType.Unspecified;
        }

        public override void Write(Utf8JsonWriter writer, PropertyDocumentType value, JsonSerializerOptions options)
        {
            writer.WriteNumberValue((int)value);
        }
    }

    public static class JsonTool
    {
        public static PropertyDocumentType ParseDocumentType(object value)
        {
            try
            {
                return (PropertyDocumentType)Enum.ToObject(typeof(PropertyDocumentType), value);
            }
            catch
            {
                try
                {
                    return (PropertyDocumentType)(Enum.Parse(typeof(PropertyDocumentType), value.ToString()));
                }
                catch
                {
                    return PropertyDocumentType.Unspecified;
                }
            }
        }
    }

    public class PropertyDocumentTypeSqlMapper : SqlMapper.TypeHandler<PropertyDocumentType>
    {
        public override PropertyDocumentType Parse(object value) => JsonTool.ParseDocumentType(value);

        public override void SetValue(IDbDataParameter parameter, PropertyDocumentType value)
        {
            parameter.Value = (int)value;
            parameter.Value = DbType.Int32;
        }
    }
}
