﻿using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using MediatR;

namespace Hca.Lib.Azure.Blob;

public class DownloadBlob : IRequest<byte[]>
{
    public DownloadBlob(
        string containerName,
        string blobName)
    {
        ContainerName = containerName;
        BlobName = blobName;
    }

    public string ContainerName { get; }
    public string BlobName { get; }
}

public class DownloadBlobHandler : IRequestHandler<DownloadBlob, byte[]>
{
    private readonly BlobServiceClient _client;
    public DownloadBlobHandler(BlobServiceClient blobClient) { _client = blobClient; }

    public async Task<byte[]> Handle(DownloadBlob request, CancellationToken cancellationToken)
    {
        var containerClient = _client.GetBlobContainerClient(request.ContainerName);
        var blobClient = containerClient.GetBlobClient(request.BlobName);
        var blob = await blobClient.DownloadAsync(cancellationToken);

        using MemoryStream ms = new();
        await blob.Value.Content.CopyToAsync(ms, cancellationToken);
        return ms.ToArray();
    }
}
