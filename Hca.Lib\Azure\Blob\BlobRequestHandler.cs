﻿using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using MediatR;

namespace Hca.Lib.Azure.Core
{
    public abstract class BlobRequestHandler<TReq, TResp> : IRequestHandler<TReq, TResp> where TReq : IRequest<TResp>
    {
        private readonly BlobServiceClient _client;
        protected readonly string _containerName;

        public BlobRequestHandler(BlobServiceClient blobClient, string containerName)
        {
            _containerName = containerName;
            _client = blobClient;
        }


        public async Task<TResp> Handle(TReq request, CancellationToken cancellationToken)
        {
            var containerClient = _client.GetBlobContainerClient(_containerName);
            return await OnHandleAsync(containerClient, request, cancellationToken);
        }

        public abstract Task<TResp> OnHandleAsync(BlobContainerClient client, TReq request, CancellationToken cancellationToken);
    }
}
