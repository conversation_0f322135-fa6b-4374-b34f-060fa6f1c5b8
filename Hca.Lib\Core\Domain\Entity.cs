﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace Hca.Lib.Core
{
    public abstract class Entity : IEntity
    {
        Action<AggregateMessage> _rootAction;
        readonly IAggregateMessageRouterWithConfig _router;
        readonly IList<IEntity> _childEntities;
        readonly List<ValueObject> _values;

        protected Entity(EntityId id)
        {
            EntityId = id;
            _router = new AggregateMessageRouter();
            _childEntities = new List<IEntity>();
            _values = new List<ValueObject>();
        }

        public EntityId EntityId { get; protected set; }

        public void Route(AggregateMessage message)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));
            _router.Route(message);
        }

        protected void Register<TEvent>(Action<TEvent> handler) where TEvent : AggregateMessage
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            _router.ConfigureRoute(handler);
        }

        protected void Apply(AggregateMessage message)
        {
            if (message == null) throw new ArgumentNullException(nameof(message));
            _rootAction(message);
        }

        public void SetRootAction(Action<AggregateMessage> rootAction)
        {
            _rootAction = rootAction;
        }

        protected Action<AggregateMessage> RootAction => _rootAction;

        protected void AddChild(IEntity entity, Action<AggregateMessage> rootAction)
        {
            entity.SetRootAction(rootAction);
            _childEntities.Add(entity);
        }

        protected void AddChildren(IEnumerable<IEntity> entities, Action<AggregateMessage> rootAction)
        {
            foreach(var entity in entities)
            {
                AddChild(entity, rootAction);
            }
        }

        protected IEnumerable<IEntity> ChildEntities => _childEntities;

        protected IEnumerable<TEntity> GetChildEntities<TEntity>() where TEntity : class, IEntity =>
            _childEntities
                .Where(c => c is TEntity)
                .Select(c => c as TEntity);

        protected void AddValue(ValueObject value) => _values.Add(value);

        protected void AddValues(IEnumerable<ValueObject> values) => _values.AddRange(values);

        protected IEnumerable<ValueObject> Values => _values;

        protected IEnumerable<TValue> GetValues<TValue>() where TValue : ValueObject =>
            _values
                .Where(c => c is TValue)
                .Select(c => c as TValue);
    }
}
