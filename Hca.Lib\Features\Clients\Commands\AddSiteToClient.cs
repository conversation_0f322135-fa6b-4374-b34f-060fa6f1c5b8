﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class AddSiteToClient : ICommand
    {
        public AddSiteToClient(Guid clientId, Guid siteId, string siteName)
        {
            ClientId = clientId;
            SiteId = siteId;
            SiteName = siteName;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
        public string SiteName { get; }
    }

    public class AddSiteToClientHandler : DapperRequestHandler<AddSiteToClient, CommandResult>
    {
        private readonly SiteCountsService _siteCountsService;
        private readonly ClientCountsService _clientCountsService;

        public AddSiteToClientHandler(IDbHelper dbHelper, ClientCountsService clientCountsService, SiteCountsService siteCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
            _siteCountsService = siteCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddSiteToClient request)
        {
            await db.InsertAsync(new SiteDtoExtended
            {
                ClientId = request.ClientId,
                Id = request.SiteId,
                SiteName = request.SiteName,
            });

            _clientCountsService.ClearClientCountsAsync(request.ClientId);
            _siteCountsService.ClearSiteCountsAsync(request.SiteId);

            return CommandResult.Success();
        }
    }
}
