﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Projects.Queries.Models;

namespace Hca.Lib.Features.Projects.Queries
{
    public class GetProject : IQuery<ProjectQueryModel>
    {
        public GetProject(Guid projectId)
        {
            ProjectId = projectId;
        }

        public Guid ProjectId { get; }
    }

    public class GetProjectHandler : DapperRequestHandler<GetProject, ProjectQueryModel>
    {
        public GetProjectHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<ProjectQueryModel> OnHandleAsync(IDbHelper db, GetProject request) =>
            db.QuerySingleOrDefaultAsync<ProjectQueryModel>(@"
                    SELECT tblProjects.*, ClientName
                    FROM tblProjects
                    JOIN tblClients ON ClientId = tblClients.Id
                    WHERE tblProjects.Id = @ProjectId",
                request);
    }
}
