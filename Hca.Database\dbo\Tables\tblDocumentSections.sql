﻿CREATE TABLE [dbo].[tblDocumentSections] (
    [Id]              UNIQUEIDENTIFIER CONSTRAINT [DF_DocumentSections_Id] DEFAULT (newid()) NOT NULL,
    [Created]         DATETIME         CONSTRAINT [DF_DocumentSections_Created] DEFAULT (getdate()) NOT NULL,
    [DocumentId]      UNIQUEIDENTIFIER NOT NULL,
    [SectionType]     INT              NOT NULL,
    [SectionTitle]    NVARCHAR (MAX)   NULL,
    [SectionContent]  NVARCHAR (MAX)   NULL,
    [SectionOrder]    INT              DEFAULT ((0)) NOT NULL,
    [SectionComplete] BIT              DEFAULT ((0)) NOT NULL,
    [SectionCode]     NVARCHAR (MAX)   NOT NULL,
    CONSTRAINT [PK_DocumentSections] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_DocumentSections_Documents] FOREIGN KEY ([DocumentId]) REFERENCES [dbo].[tblDocuments] ([Id])
);

