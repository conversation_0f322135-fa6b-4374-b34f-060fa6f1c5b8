﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries;

public class GetPropertyCounts : IQuery<PropertyCountsModel>
{
    public GetPropertyCounts(Guid clientId, Guid propertyId)
    {
        ClientId = clientId;
        PropertyId = propertyId;
    }

    public Guid ClientId { get; }
    public Guid PropertyId { get; }
}

public class GetPropertyCountsHandler : DapperRequestHandler<GetPropertyCounts, PropertyCountsModel>
{
    public GetPropertyCountsHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<PropertyCountsModel> OnHandleAsync(IDbHelper db, GetPropertyCounts request)
    {
        var documentTask = db.CountAsync<PropertyDocumentDto>($"WHERE {nameof(PropertyDocumentDto.PropertyId)} = @{nameof(GetPropertyDocuments.PropertyId)}", request);
        var floorPlanTask = db.CountAsync<FloorPlanDto>($"WHERE {nameof(FloorPlanDto.PropertyId)} = @{nameof(GetPropertyDocuments.PropertyId)}", request);

        await Task.WhenAll(documentTask, floorPlanTask);

        return new PropertyCountsModel
        {
            DocumentCount = documentTask.Result,
            FloorPlanCount = floorPlanTask.Result
        };
    }
}
