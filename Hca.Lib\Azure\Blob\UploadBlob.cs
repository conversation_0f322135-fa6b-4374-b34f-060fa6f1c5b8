﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Hca.Lib.Core;
using MediatR;

namespace Hca.Lib.Azure.Core
{
    public class UploadBlob : ICommand<Uri>
    {
        public UploadBlob(
            string containerName,
            string blobName,
            byte[] contents,
            string contentType)
        {
            ContainerName = containerName;
            BlobName = blobName;
            Contents = contents;
            ContentType = contentType;
        }

        public UploadBlob(
            string containerName,
            string blobName,
            Stream stream,
            string contentType)
        {
            ContainerName = containerName;
            BlobName = blobName;
            Stream = stream;
            ContentType = contentType;
        }

        public string ContainerName { get; }
        public string BlobName { get; }
        public Stream Stream { get; }
        public byte[] Contents { get; }
        public string ContentType { get; }
    }

    public class UploadBlobHandler : IRequestHandler<UploadBlob, CommandResult<Uri>>
    {
        private readonly BlobServiceClient _client;
        public UploadBlobHandler(BlobServiceClient blobClient) { _client = blobClient; }

        public async Task<CommandResult<Uri>> Handle(UploadBlob request, CancellationToken cancellationToken)
        {
            var containerClient = _client.GetBlobContainerClient(request.ContainerName);
            var blob = containerClient.GetBlobClient(request.BlobName);
            var stream = request.Stream;

            if (stream == null)
            {
                stream = new MemoryStream();
                await stream.WriteAsync(request.Contents.AsMemory(0, request.Contents.Length), cancellationToken);
                stream.Seek(0, SeekOrigin.Begin);
            }

            await blob.UploadAsync(
                stream,
                httpHeaders: new BlobHttpHeaders { ContentType = request.ContentType },
                cancellationToken: cancellationToken);

            return CommandResult<Uri>.Success(blob.Uri);
        }
    }
}
