﻿using Refit;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Services.QRFileSpot;

public interface IQrFileSpotService
{
    [Get("/api/v1/documents/{id}")]
    Task<DocumentModel> GetDocumentAsync(string id, CancellationToken ct);

    [Multipart, Post("/api/v1/documents")]
    Task<ClientDocumentModel> PostDocumentAsync(
        [AliasAs("file")] StreamPart fileContent,
        [AliasAs("category")] string category,
        [AliasAs("documentType")] string documentType,
        [AliasAs("content")] string content,
        CancellationToken ct);

    [Multipart, Put("/api/v1/documents/{id}")]
    Task<ClientDocumentModel> PutDocumentAsync(
        string id,
        [AliasAs("file")] StreamPart fileContent,
        [AliasAs("category")] string category,
        [AliasAs("documentType")] string documentType,
        [AliasAs("content")] string content,
        CancellationToken ct);

    [Delete("/api/v1/documents/{id}")]
    Task DeleteDocumentAsync(string id, CancellationToken ct);
}