﻿using System;
using System.Threading.Tasks;
using System.Web;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class CreateClient : ICommand
    {
        public CreateClient(
            Guid clientId,
            string clientName,
            ClientType clientType)
        {
            ClientId = clientId;
            ClientName = clientName;
            ClientType = clientType;
        }

        public CreateClient(
            string clientName,
            ClientType clientType) : this(
                Guid.NewGuid(),
                clientName,
                clientType)
        { }

        public string ClientName { get; }
        public ClientType ClientType { get; }
        public string Email { get; }
        public string MobilePhone { get; }
        public string Website { get; }

        public Guid ClientId { get; }
    }

    public class CreateClientHandler : DapperRequestHandler<CreateClient, CommandResult>
    {
        public CreateClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateClient request)
        {
            await db.InsertAsync(new ClientDto
            {
                Id = request.ClientId,
                ClientName = request.ClientName,
                ClientType = request.ClientType,
                UrlSafeClientName = HttpUtility.UrlEncode(request.ClientName.Replace(" ", "")),
            });

            return CommandResult.Success();
        }
    }
}
