﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Users;
using Hca.Lib.Features.Users.Commands;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Hca.Lib.Identity.Services
{
    public class NewUserService
    {
        private readonly IMediator _mediator;
        private readonly IPasswordHasher<HcaUser> _passwordHasher;

        public NewUserService(IMediator mediator, IPasswordHasher<HcaUser> passwordHasher)
        {
            _mediator = mediator; 
            _passwordHasher = passwordHasher;
        }

        public async Task<HcaUser> Create(string email, string password, UserRole role, CancellationToken cancellationToken = default)
        {
            var userId = Guid.NewGuid();
            var user = new HcaUser(userId, email, role, null);
            user.PasswordHash = _passwordHasher.HashPassword(user, password);
            await _mediator.Send(new CreateUser(userId, user.UserName, user.PasswordHash, user.Role), cancellationToken);
            return user;
        }
    }
}
