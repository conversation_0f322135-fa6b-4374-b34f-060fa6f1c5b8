﻿@*<script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>*@

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.1/jquery.validate.min.js" integrity="sha256-sPB0F50YUDK0otDnsfNHawYmA5M0pjjUf4TvRJkGFrI=" crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/3.2.11/jquery.validate.unobtrusive.js" integrity="sha256-XNNC8ESw29iopRLukVRazlP44TxnjGmEQanHJ5kHmtk=" crossorigin="anonymous"></script>
<script>
    const ifAsbestosPresumed = 'if-asbestos-presumed';
    $.validator.unobtrusive.adapters.add(ifAsbestosPresumed, [ifAsbestosPresumed], function (options) {
        options.rules[ifAsbestosPresumed] = options.params;
        if (options.message != null) {
            options.messages[ifAsbestosPresumed] = options.message;
        }
    });

    $.validator.addMethod(ifAsbestosPresumed, function (value, element, params) {
        var asbestosPresumed = $('#chkAsbestosPresumed')[0].checked;
        if (!asbestosPresumed) return true;
        return value > '';
    });

    const ifAccessible = 'if-accessible';
    $.validator.unobtrusive.adapters.add(ifAccessible, [ifAccessible], function (options) {
        options.rules[ifAccessible] = options.params;
        if (options.message != null) {
            options.messages[ifAccessible] = options.message;
        }
    });

    $.validator.addMethod(ifAccessible, function (value, element, params) {
        var status = $('#ddlStatus').val();
        if (status === 'NA') return true;
        return value > '';
    });

    const townOrCity = 'town-or-city';
    $.validator.unobtrusive.adapters.add(townOrCity, [townOrCity], function (options) {
        options.rules[townOrCity] = options.params;
        if (options.message != null) {
            options.messages[townOrCity] = options.message;
        }
    });

    $.validator.addMethod(townOrCity, function (value, element, params) {
        var town = $('#Address_Town').val();
        var city = $('#Address_City').val();
        if (town > '' || city > '') return true;
        return false;
    });

    const ifMaterialColourRequired = 'material-colour';
    $.validator.unobtrusive.adapters.add(ifMaterialColourRequired, [ifMaterialColourRequired], function (options) {
        options.rules[ifMaterialColourRequired] = options.params;
        if (options.message != null) {
            options.messages[ifMaterialColourRequired] = options.message;
        }
    });

    $.validator.addMethod(ifMaterialColourRequired, function (value, element, params) {
        var material = $('#txtMaterial')[0].value;
        if (material.indexOf('Floor tile') > -1 || material.indexOf('Linoleum') > -1) return false;
        return true;
    });

    $.validator.unobtrusive.parse($('form'));</script>