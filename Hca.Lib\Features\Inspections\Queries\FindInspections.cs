﻿using System;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections.Queries.Models;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class FindInspections : PagedQuery<InspectionQueryModel>
    {
        public FindInspections(Guid projectId, int? page = 1, int? pageSize = 24) : base(page, pageSize)
        {
            ProjectId = projectId;
        }

        public Guid ProjectId { get; }
    }

    public class FindInspectionsHandler : DapperPagedQueryHandler<FindInspections, InspectionQueryModel>
    {
        public FindInspectionsHandler(IDbHelper dbHelper) : base(dbHelper) { }
            
        protected override (string sql, object args) Build(FindInspections request)
        {
            var sql = @$"
SELECT
    tblInspections.*,
    {nameof(InspectionQueryModel.ClientId)},
    {nameof(InspectionQueryModel.ClientName)},
    {nameof(InspectionQueryModel.PropertyCode)}, 
    {TableNames.Properties}.{nameof(InspectionQueryModel.BuildingName)}, 
    {TableNames.Properties}.{nameof(InspectionQueryModel.BuildingNumber)}, 
    {nameof(InspectionQueryModel.Country)}, 
    {nameof(InspectionQueryModel.County)}, 
    {TableNames.Properties}.{nameof(InspectionQueryModel.Floor)}, 
    {nameof(InspectionQueryModel.Postcode)}, 
    {nameof(InspectionQueryModel.StreetName)},
    {nameof(InspectionQueryModel.Town)},
    {TableNames.Properties}.{nameof(InspectionQueryModel.Unit)} 
FROM tblInspections
JOIN tblProperties ON tblInspections.PropertyId = tblProperties.Id
JOIN tblAddresses ON tblAddresses.Id = tblProperties.AddressId
JOIN tblClients ON tblProperties.ClientId = tblClients.Id
WHERE ProjectId = @{nameof(FindInspections.ProjectId)}
ORDER BY Created DESC

SELECT COUNT(*)
FROM tblInspections
WHERE ProjectId = @{nameof(FindInspections.ProjectId)}";

            return (sql, request);
        }
    }
}
