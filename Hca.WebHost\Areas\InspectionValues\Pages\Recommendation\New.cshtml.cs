﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Recommendation
{
    public class New : InspectionValueCreatePage
    {
        public New(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override   ValueListType  ValueListType =>   ValueListType.Recommendation;

        public override string IndexUrl => Urls.Recommendation;
    }
}
