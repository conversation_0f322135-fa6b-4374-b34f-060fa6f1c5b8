﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class DeleteContact : ICommand
    {
        public DeleteContact(Guid contactId)
        {
            ContactId = contactId;
        }

        public Guid ContactId { get; }
    }

    public class DeleteContactHandler : DapperRequestHandler<DeleteContact, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;

        public DeleteContactHandler(IDbHelper dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteContact request)
        {
            var contact = await db.GetAsync<ContactDto>(request.ContactId);

            await db.DeleteAsync<ContactDto>(request.ContactId);

            _clientCountsService.ClearClientCountsAsync(contact.ClientId);

            return CommandResult.Success();
        }
    }
}
