﻿using System;
using System.IO;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using MediatR;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UploadPropertyDocument : ICommand
    {
        public UploadPropertyDocument(
            Guid clientId,
            Guid propertyId,
            Stream imageFile,
            string imageFileContentType,
            string filename,
            DateTime documentDate,
            PropertyDocumentType documentType,
            string companyName,
            string notes,
            DateTime? nextInspectionDate)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            ImageFile = imageFile;
            ImageFileContentType = imageFileContentType;
            Filename = filename;
            DocumentDate = documentDate;
            CompanyName = companyName;
            DocumentType = documentType;
            Notes = notes;
            NextInspectionDate = nextInspectionDate;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public Stream ImageFile { get; }
        public string ImageFileContentType { get; }
        public string Filename { get; }
        public DateTime DocumentDate { get; }
        public string CompanyName { get; }
        public PropertyDocumentType DocumentType { get; }
        public string Notes { get; }
        public DateTime? NextInspectionDate { get; }
    }

    public class UploadPropertyDocumentHandler : DapperRequestHandler<UploadPropertyDocument, CommandResult>
    {
        private readonly IMediator _mediator;
        private readonly PropertyCountsService _propertyCountsService;

        public UploadPropertyDocumentHandler(IDbHelper dbHelper, IMediator mediator, PropertyCountsService propertyCountsService) : base(dbHelper)
        {
            _mediator = mediator;
            _propertyCountsService = propertyCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UploadPropertyDocument request)
        {
            var blobName = $"{request.ClientId}/properties/{request.PropertyId}/documents/{request.Filename}";
            var containerName = StorageConstants.ClientsContainerName;
            var uploadResponse = await _mediator.Send(new UploadBlob(containerName, blobName, request.ImageFile, request.ImageFileContentType));

            if (!uploadResponse.IsSuccess)
            {
                return CommandResult.Fail("Failed to upload document");
            }

            var dto = new PropertyDocumentDto
            {
                Id = Guid.NewGuid(),
                CompanyName = request.CompanyName,
                ContainerName = containerName,
                BlobName = blobName,
                DocumentDate = request.DocumentDate,
                PropertyDocumentType = request.DocumentType,
                Notes = request.Notes,
                PropertyId = request.PropertyId,
                NextInspection = request.NextInspectionDate,
            };

            await db.InsertAsync(dto);

            _propertyCountsService.ClearPropertyCountsAsync(request.PropertyId);

            return CommandResult.Success();
        }
    }
}

