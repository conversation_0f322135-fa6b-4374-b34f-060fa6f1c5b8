﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Projects.Documents;

namespace Hca.Lib.Features.Projects.Commands
{
    public class DeleteQuote : ICommand
    {
        public DeleteQuote(Guid quoteId)
        {
            QuoteId = quoteId;
        }

        public Guid QuoteId { get; }
    }

    public class DeleteQuoteHandler : DapperRequestHandler<DeleteQuote, CommandResult>
    {
        public DeleteQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteQuote request)
        {
            await db.DeleteAsync<QuoteDocumentDto>("WHERE QuoteId=@QuoteId", request); // todo: change to a job or even make conditional/break relationship?
            await db.DeleteAsync<QuotePropertyDto>("WHERE QuoteId=@QuoteId", request);
            await db.DeleteAsync<QuoteDto>(request.QuoteId);

            return CommandResult.Success();
        }
    }
}
