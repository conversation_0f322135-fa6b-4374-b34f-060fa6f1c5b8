<!-- START Sidebar (left)-->
<div class="aside-inner">
    <nav class="sidebar" data-sidebar-anyclick-close="">
        <!-- START sidebar nav-->
        <ul class="sidebar-nav">
            <!-- START user info-->
            <li class="has-user-block">
                <div id="user-block" class="collapse">
                    <div class="item user-block">
                        <!-- User picture-->
                        <div class="user-block-picture">
                            <div class="user-block-status">
                                <img src="~/images/user/02.jpg" alt="Avatar" width="60" height="60" class="img-thumbnail rounded-circle" />
                                <div class="circle bg-success circle-lg"></div>
                            </div>
                        </div>
                        <!-- Name and Job-->
                        <div class="user-block-info">
                            <span class="user-block-name">Hello, Mike</span>
                            <span class="user-block-role">Designer</span>
                        </div>
                    </div>
                </div>
            </li>
            <!-- END user info-->
            <!-- Iterates over all sidebar items-->
            <li class="nav-heading">
                <span>Main Navigation</span>
            </li>
            <li class="@Html.IsActive(controller: "SingleView")">
                <a href="@Url.Action("Index", "SingleView")" title="SingleView">
                    <em class="far fa-file"></em>
                    <span data-localize="sidebar.nav.SINGLEVIEW">SingleView</span>
                </a>
            </li>
            <li class="@Html.IsActive(controller: "Template")">
                <a href="@Url.Action("Index", "Template")" title="Template">
                    <em class="far fa-file"></em>
                    <span data-localize="sidebar.nav.SINGLEVIEW">Template</span>
                </a>
            </li>
        </ul>
        <!-- END sidebar nav-->
    </nav>
</div>
<!-- END Sidebar (left)-->


