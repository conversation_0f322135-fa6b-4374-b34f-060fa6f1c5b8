﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientPropertyArchiveModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientPropertyArchiveModel(
        ClientService clientService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnPostAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewProperty(Guid.Parse(id), ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        var archiveReason = Request.Form[Constants.ArchiveReasonFormName];

        var deleteResult = await _clientService.ArchivePropertyAsync(Guid.Parse(id), archiveReason, cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.ClientProperties(clientId));
        }

        ErrorMessage = deleteResult.Reason;

        return Page();
    }
}
