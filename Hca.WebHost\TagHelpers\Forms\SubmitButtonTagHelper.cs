﻿using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("submit")]
    public class SubmitButtonTagHelper : TagHelper
    {
        [HtmlAttributeName("submitting-label")]
        public string SubmittingLabel { get; set; } = "Saving…";

        [HtmlAttributeName("label")]
        public string Label { get; set; } = "Save";

        [HtmlAttributeName("class")]
        public string Class { get; set; } = "btn btn-success";

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "input";
            output.Attributes.Add("type", "submit");
            output.Attributes.Add("class", Class);
            output.Attributes.Add("value", Label);
            output.Attributes.Add("onClick", $"this.disabled = true; this.value = '{SubmittingLabel}'; this.form.submit();");

            base.Process(context, output);
        }
    }
}
