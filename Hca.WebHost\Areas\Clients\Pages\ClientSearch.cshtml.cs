﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSearchModel : HcaPageModel
{
    public ClientSearchModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public PagedDtoSet<ClientDto> SearchResults { get; private set; }

    public PagedDtoSet<ClientDto> ArchivedResults { get; private set; }

    public bool ShowArchivedClients { get; private set; }

    // todo: add paging
    public async Task<IActionResult> OnGetAsync(
        [FromQuery] string searchText,
        [FromQuery] int? pageSize,
        [FromQuery] int? pageNum,
        [FromQuery] bool showArchivedClients,
        CancellationToken cancellationToken)
    {
        if (!await IsHcaUser) return Forbid();

        ShowArchivedClients = showArchivedClients;

        if (string.IsNullOrWhiteSpace(searchText))
        {
            SearchResults = await _mediator.Send(new GetAllClientsPagedQuery(pageNum, pageSize), cancellationToken);

            if (ShowArchivedClients)
            {
                ArchivedResults = await _mediator.Send(new GetAllClientsPagedQuery(pageNum, pageSize, true), cancellationToken);
            }
        }
        else
        {
            SearchResults = await _mediator.Send(new ClientSearchQuery(searchText, pageNum, pageSize), cancellationToken);

            if (ShowArchivedClients)
            {
                ArchivedResults = await _mediator.Send(new ClientSearchQuery(searchText, pageNum, pageSize, true), cancellationToken);
            }
        }

        return Page();
    }
}
