﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace Hca.WebHost.Areas.Inspections.Pages.Sample;

[IgnoreAntiforgeryToken]
public class NewModel : HcaPageModel
{
    private readonly ValueListsService _values;
    private readonly InspectionService _inspections;

    public NewModel(
        InspectionService inspections,
        ValueListsService inspectionValuesService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _inspections = inspections;
        _values = inspectionValuesService;
    }

    public IEnumerable<ValueDto> Floors { get; private set; }
    public IEnumerable<ValueDto> FloorPlanColours { get; private set; }
    public IEnumerable<ValueDto> Quantities { get; private set; }
    public IEnumerable<ValueDto> Materials { get; private set; }
    public IEnumerable<ValueDto> Recommendations { get; private set; }
    public IEnumerable<ValueDto> Priorities { get; private set; }

    [BindProperty]
    public PostSampleModel PostModel { get; set; }

    public Guid InspectionId { get; private set; }

    public Task OnGetAsync(Guid inspectionId, CancellationToken cancellationToken)
    {
        InspectionId = inspectionId;

        return GetValueLists(cancellationToken);
    }

    private Task GetValueLists(CancellationToken cancellationToken)
    {
        return Task.WhenAll(new[]
        {
            _values.GetAllAsync(ValueListType.Floors, cancellationToken).ContinueWith(r => Floors = r.Result),
            _values.GetAllAsync(ValueListType.FloorPlanColours, cancellationToken).ContinueWith(r => FloorPlanColours = r.Result),
            _values.GetAllAsync(ValueListType.Quantity, cancellationToken).ContinueWith(r => Quantities = r.Result),
            _values.GetAllAsync(ValueListType.Material, cancellationToken).ContinueWith(r => Materials = r.Result),
            _values.GetAllAsync(ValueListType.Recommendation, cancellationToken).ContinueWith(r => Recommendations = r.Result),
            _values.GetAllAsync(ValueListType.Priority, cancellationToken).ContinueWith(r => Priorities = r.Result),
        });
    }

    public async Task<IActionResult> OnPostAsync(Guid inspectionId, CancellationToken cancellationToken)
    {
        InspectionId = inspectionId;

        if (!ModelState.IsValid)
        {
            await GetValueLists(cancellationToken);
            return Page();
        }

        try
        {
            var sampleId = await _inspections.AddSample(inspectionId, PostModel, cancellationToken);

            foreach(var image in HttpContext.Request.Form.Files)
            {
                await _inspections.AddSampleImage(inspectionId, sampleId, image.FileName, image.OpenReadStream());
            }
        }
        catch (Exception ex)
        {
            ModelState.TryAddModelException("SaveError", ex);
            await GetValueLists(cancellationToken);
            return Page();
        }

        var inspection = await _inspections.GetAsync(inspectionId, cancellationToken);
        return Redirect(Urls.ClientProjectInspection(inspection.ClientId, inspection.ProjectId.Value, inspection.Id));
    }

    public class PostSampleModel
    {
        [Required]
        public string InspectionRecord { get; set; }

        [Required]
        public string Status { get; set; }

        [RequiredIfAsbestosPresumed]
        public Guid? FloorPlanColour { get; set; }

        [Required]
        public Guid Floor { get; set; }

        [Required]
        public string FloorActual { get; set; }

        [Required]
        public string FloorPlanReference { get; set; }

        [Required]
        public string RoomUsage { get; set; }

        [Required]
        public string Location { get; set; }

        [RequiredIfAccessible]
        public Guid? Material { get; set; }

        [RequiredMaterial]
        public string MaterialColour { get; set; }

        [RequiredIfAccessible]
        public string MaterialActual { get; set; }

        [RequiredIfAccessible]
        public string Quantity { get; set; }

        [RequiredIfAccessible]
        public Guid? QuantityUnit { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? ProductType { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? ExtentOfDamage { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? SurfaceTreatment { get; set; }

        public int? AsbestosType { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? OccupantActivity { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? LikelihoodOfDisturbance { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? NumberOfOccupants { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? TypeOfMaintenance { get; set; }

        [RequiredIfAsbestosPresumed]
        public int? Accessibility { get; set; }

        [RequiredIfAsbestosPresumed]
        public Guid? ManagementRecommendation { get; set; }

        [RequiredIfAsbestosPresumed]
        public Guid? Priority { get; set; }

        [Required]
        public bool AsbestosPresumed { get; set; }

        public string AdditionalComments { get; set; }
    }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = true)]
    public class RequiredIfAsbestosPresumedAttribute : ValidationAttribute, IClientModelValidator
    {
        public void AddValidation(ClientModelValidationContext context)
        {
            context.Attributes.Remove("data-val-required");
            context.Attributes.TryAdd("data-val", "true");
            context.Attributes.TryAdd("data-val-if-asbestos-presumed", "Required if asbestos is presumed");
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            ErrorMessage = ErrorMessageString;

            if (validationContext == null)
            {
                throw new ArgumentNullException(nameof(validationContext));
            }

            if (validationContext.ObjectType != typeof(PostSampleModel))
            {
                throw new ArgumentException("RequiredIfAsbestosPresumedAttribute can only be used on PostSampleModel");
            }

            var asbestosPresumedProperty = validationContext.ObjectType.GetProperty(nameof(PostSampleModel.AsbestosPresumed));

            var asbestosPresumed = (bool)asbestosPresumedProperty.GetValue(validationContext.ObjectInstance);

            if (!asbestosPresumed)
            {
                return ValidationResult.Success;
            }

            if (value == null || value == default)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = true)]
    public class RequiredIfAccessibleAttribute : ValidationAttribute, IClientModelValidator
    {
        public void AddValidation(ClientModelValidationContext context)
        {
            context.Attributes.Remove("data-val-required");
            context.Attributes.TryAdd("data-val", "true");
            context.Attributes.TryAdd("data-val-if-accessible", "Required if accessible");
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            ErrorMessage = ErrorMessageString;

            if (validationContext == null)
            {
                throw new ArgumentNullException(nameof(validationContext));
            }

            if (validationContext.ObjectType != typeof(PostSampleModel))
            {
                throw new ArgumentException("RequiredIfCanAccessAttribute can only be used on PostSampleModel");
            }

            var statusProperty = validationContext.ObjectType.GetProperty(nameof(PostSampleModel.Status));

            var statusPropertyValue = (string)statusProperty.GetValue(validationContext.ObjectInstance);

            if (statusPropertyValue == InspectionStatusCodes.NoAccess)
            {
                return ValidationResult.Success;
            }

            if (value == null || value == default)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Parameter, AllowMultiple = true)]
    public class RequiredMaterialAttribute : ValidationAttribute, IClientModelValidator
    {
        public void AddValidation(ClientModelValidationContext context)
        {
            context.Attributes.Remove("data-val-required");
            context.Attributes.TryAdd("data-val", "true");
            context.Attributes.TryAdd("data-val-material-colour", "Material colour required");
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            var valueListService = (ValueListsService)validationContext.GetService(typeof(ValueListsService));

            ErrorMessage = ErrorMessageString;

            if (validationContext == null)
            {
                throw new ArgumentNullException(nameof(validationContext));
            }

            if (validationContext.ObjectType != typeof(PostSampleModel))
            {
                throw new ArgumentException("RequiredMaterialAttribute can only be used on PostSampleModel");
            }

            var materialProperty = validationContext.ObjectType.GetProperty(nameof(PostSampleModel.Material));

            var materialPropertyGuid = (Guid)materialProperty.GetValue(validationContext.ObjectInstance);

            var material = valueListService.GetAsync(materialPropertyGuid, CancellationToken.None).Result;

            if (material.ToString() == "true")
            {
                return ValidationResult.Success;
            }

            if (value == null || value == default)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }
}
