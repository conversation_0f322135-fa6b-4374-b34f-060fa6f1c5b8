﻿using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Config.Commands
{
    public class SetConfig : ICommand
    {
        public SetConfig(
            string configName,
            int? intValue,
            string stringValue)
        {
            ConfigName = configName;
            IntValue = intValue;
            StringValue = stringValue;
        }

        public string ConfigName { get; }
        public int? IntValue { get; }
        public string StringValue { get; }
    }

    public class SetConfigHandler : DapperRequestHandler<SetConfig, CommandResult>
    {
        public SetConfigHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public async override Task<CommandResult> OnHandleAsync(IDbHelper db, SetConfig request)
        {
            var dto = await db.GetAsync<ConfigDto>(request.ConfigName);

            if (dto == null)
            {
                dto = new ConfigDto
                {
                    ConfigName = request.ConfigName
                };
                await db.InsertAsync(dto);
            }

            dto.IntValue = request.IntValue;
            dto.StringValue = request.StringValue;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
