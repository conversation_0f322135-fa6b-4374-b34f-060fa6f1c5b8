﻿using System;
using Dapper;

namespace Hca.Lib.Features.Inspections
{
    [Table("tblInspections")]
    public class InspectionDto
    {
        public Guid Id { get; set; }

        public Guid PropertyId { get; set; }

        public Guid? ProjectId { get; set; }

        public Guid InspectionTypeId { get; set; }

        public DateTime Created { get; set; } = DateTime.UtcNow;
    }

    public class InspectionWithDetailsDto : InspectionDto
    { 
        [IgnoreInsert]
        [IgnoreUpdate]
        public string ClientName { get; set; }

        [IgnoreInsert]
        [IgnoreUpdate]
        public Guid ClientId { get; set; }

        [IgnoreInsert]
        [IgnoreUpdate]
        public string LocationCode { get; set; }
    }
}
