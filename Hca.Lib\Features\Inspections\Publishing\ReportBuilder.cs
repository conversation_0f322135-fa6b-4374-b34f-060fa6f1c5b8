﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Reports.Publishing
{
    public class ReportBuilder
    {
        private readonly DocumentCompiler _documentBuilder;
        private readonly HttpClient _httpClient;

        public ReportBuilder(
            DocumentCompiler documentBuilder,
            IHttpClientFactory httpClientFactory)
        {
            _documentBuilder = documentBuilder;
            _httpClient = httpClientFactory.CreateClient();
        }

        public async Task<byte[]> BuildReportAsync(ReportModel report, CancellationToken cancellationToken)
        {
            var samples = new List<(InspectionSampleDto, byte[])>();
            var tasks = new List<Task>();

            foreach(var (Sample, Images) in report.Samples)
            {
                // todo: maybe tag as "default image" in future
                var image = Images.FirstOrDefault();

                if (image == null)
                {
                    samples.Add((Sample, null));
                }
                else
                {
                    tasks.Add(_httpClient
                        .GetByteArrayAsync(image.ImageUrl, cancellationToken)
                        .ContinueWith(i => samples.Add((Sample, i.Result))));
                }
            }

            await Task.WhenAll(tasks);

            report.DocumentModel.AdditionalTransforms = new Func<CompiledDocument, CompiledDocument>[]
            {
                new Func<CompiledDocument, CompiledDocument>((doc) => doc.AddSamples(samples)),
            };

            return await _documentBuilder.BuildDocument(report.DocumentModel);
        }
    }
}
