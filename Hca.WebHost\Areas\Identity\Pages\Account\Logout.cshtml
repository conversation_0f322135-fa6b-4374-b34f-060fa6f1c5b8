﻿@page
@model Hca.WebHost.Areas.Identity.Pages.Account.LogoutModel
@{
    Layout = "_LayoutHorizontal";
    ViewData["Title"] = "Log out";
}

<header>
    <h1>@ViewData["Title"]</h1>
    @{
        if (User.Identity.IsAuthenticated)
        {
            <p>Logging you out...</p>
            <form id="autoLogoutForm" class="d-none" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Page("/", new { area = "" })" method="post">
                <button type="submit" id="autoLogoutButton">Logout</button>
            </form>
            <script>
                // Auto-submit the logout form immediately
                document.getElementById('autoLogoutButton').click();
            </script>
        }
        else
        {
            <p>You have successfully logged out of the application.</p>
        }
    }
</header>