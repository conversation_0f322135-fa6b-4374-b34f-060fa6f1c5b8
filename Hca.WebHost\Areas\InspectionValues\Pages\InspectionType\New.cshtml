﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.InspectionType.New
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Inspection Type Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Inspection Type",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />