﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class ArchiveProperty : ICommand
    {
        public Guid PropertyId { get; }
        public string ArchiveReason { get; }
        public DateTime? Archived { get; }

        public ArchiveProperty(Guid propertyId, string archiveReason, DateTime? archived = null)
        {
            PropertyId = propertyId;
            ArchiveReason = archiveReason;
            Archived = archived ?? DateTime.UtcNow;
        }
    }

    public class ArchivePropertyHandler : DapperRequestHandler<ArchiveProperty, CommandResult>
    {
        private readonly SiteCountsService _siteCountsService;
        private readonly ClientCountsService _clientCountsService;

        public ArchivePropertyHandler(IDbHelper dbHelper, SiteCountsService siteCountsService, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _siteCountsService = siteCountsService;
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, ArchiveProperty request)
        {
            var property = await db.GetAsync<PropertyDto>(request.PropertyId);

            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET " +
                $"{nameof(PropertyDto.Archived)}=@{nameof(request.Archived)}, " +
                $"{nameof(PropertyDto.ArchiveReason)}=@{nameof(request.ArchiveReason)} " +
                $"WHERE {nameof(PropertyDto.Id)}=@{nameof(request.PropertyId)}", request);

            _clientCountsService.ClearClientCountsAsync(property.ClientId);
            if (property.SiteId.HasValue)
            {
                _siteCountsService.ClearSiteCountsAsync(property.SiteId.Value);
            }

            return CommandResult.Success();
        }
    }
}
