﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Recommendation.Index
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Recommendation Name" }
        },
        Header = "Recomendation",
        UrlPath = Urls.Recommendation,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />