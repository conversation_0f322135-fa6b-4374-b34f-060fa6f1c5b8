﻿@model HomeViewModel
@{
    ViewData["Title"] = "Home Page";
}

<div class="jumbotron">
    <h1>DaV3</h1>
    <p class="lead">Now with added data</p>
    <p><a href="https://asp.net" class="btn btn-primary btn-lg">Learn more &raquo;</a></p>
</div>

<div class="row">
    <div class="col-md-4">
        <h2>Clients</h2>
        @foreach (var client in Model.Clients.Items.OrderBy(_ => _.ClientName))
        {
            <p>@client.ClientName</p>
        }
    </div>
    <div class="col-md-4">
        <h2>Templates</h2>
        @foreach (var template in Model.Templates.Items.OrderBy(_ => _.TemplateName))
        {
            <p>@template.TemplateName</p>
        }
    </div>
    <div class="col-md-4">
        <h2>Something else</h2>
        <p>Would go here</p>
        <p>Hey</p>
    </div>
</div>