﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Queries
{
    public class GetTemplateSectionObject : IQuery<TemplateSection>
    {
        public GetTemplateSectionObject(Guid templateSectionId)
        {
            TemplateSectionId = templateSectionId;
        }

        public Guid TemplateSectionId { get; }
    }

    public class GetTemplateSectionObjectHandler : DapperRequestHandler<GetTemplateSectionObject, TemplateSection>
    {
        public GetTemplateSectionObjectHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<TemplateSection> OnHandleAsync(IDbHelper db, GetTemplateSectionObject request)
        {
            var templateSection = await db.GetAsync<TemplateSectionDto>(request.TemplateSectionId);
            var templateFieldDtos = await db.GetListAsync<TemplateFieldDto>(
                $"WHERE TemplateSectionId = @{nameof(GetTemplateSectionObject.TemplateSectionId)}", request);

            return TemplateUtils.BuildTemplateSection(templateSection, templateFieldDtos);
        }
    }
}
