﻿@page
@model Hca.WebHost.Areas.Search.Pages.IndexModel
@using Hca.Lib.Services
@functions {
    string GroupHeader(SearchEntity searchEntity) =>
        searchEntity switch
        {
            SearchEntity.Client => "Clients",
            SearchEntity.Location => "Properties",
            SearchEntity.Project => "Projects",
            _ => throw new NotImplementedException(),
        };
}

<div class="container container-md">
    <form action="/search" method="get">
        <div class="input-group input-group-lg">
            <input class="form-control form-control-lg rounded-0" type="text" name="searchTerm" placeholder="Search" value="@Model.SearchTerm" />
            <div class="input-group-append">
                <input class="btn btn-info btn-lg b0 rounded-0" type="submit" value="Search">
            </div>
        </div>
    </form>
    <p class="my-3 text-muted text-center">
    </p>
    <div class="card card-default">
        <div class="card-header">
            Search Results
        </div>
        <div class="card-body">
            @foreach (var group in Model.SearchResults.Items.GroupBy(i => i.EntityType))
            {
                <h3>@GroupHeader(group.Key)</h3>
                <ul>
                    @foreach (var entity in group)
                    {
                        <li><a href="@Areas.Search.Pages.IndexModel.BuildRedirectUrl(entity)">@entity.DisplayText</a></li>
                    }
                </ul>
            }
            <br />
            <form method="post"><input asp-for="ShowArchived" onclick="this.form.submit()" /> Show Archived</form>
            @if (Model.ShowArchived)
            {
                <br />            
                foreach (var group in Model.ArchivedResults.Items.GroupBy(i => i.EntityType))
                {
                    <h3>@GroupHeader(group.Key)</h3>
                    <ul>
                        @foreach (var entity in group)
                        {
                            <li><a href="@Areas.Search.Pages.IndexModel.BuildRedirectUrl(entity)">@entity.DisplayText</a></li>
                        }
                    </ul>
                }
            }
        </div>
        <!--<div class="card-footer">
            <div class="d-flex">-->
        @*<button class="btn btn-sm btn-secondary" id="btnClearClientSearch">Clear</button>*@
        <!--<nav class="ml-auto">
                    <ul class="pagination pagination-sm">
        @for (int i = 1; i <= Model.SearchResults.TotalPages; i++)
        {
                                <li class="page-item @(i == Model.SearchResults.CurrentPage ? " active" : "")">
                                    <a class="page-link" href="#">@i</a>
                                </li>
        }
                    </ul>
                </nav>
            </div>
        </div>-->
    </div>
</div>