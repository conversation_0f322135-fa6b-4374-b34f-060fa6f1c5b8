﻿using System;
using Dapper;

namespace Hca.Lib.Features.Templates
{
    [Table(TableNames.Templates)]
    public class TemplateDto
    {
        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string TemplateCode { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string TemplateName { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public TemplateType TemplateType { get; set; }
    }
}
