﻿using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.TagHelpers;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    //[HtmlTargetElement("select", Attributes = "asp-for, row-label")]
    [HtmlTargetElement("input", Attributes = "asp-for, row-label")]
    public class FormRowInputHelper : InputTagHelper
    {
        private readonly HttpContext _httpContext;

        public FormRowInputHelper(
            IHtmlGenerator generator,
            IHttpContextAccessor httpContextAccessor) : base(generator)
        {
            _httpContext = httpContextAccessor.HttpContext;
        }

        [HtmlAttributeName("row-label")]
        public string HcaLabel { get; set; }

        [HtmlAttributeName("no-prompt")]
        public bool NoPrompt { get; set; } = true;

        [HtmlAttributeName("asp-items")]
        public object Items { get; set; }

        [HtmlAttributeName("asp-for")]
        public ModelExpression Source { get; set; }

        [HtmlAttributeName("read-only")]
        public bool ReadOnly { get; set; }

        [HtmlAttributeName("asp-id")]
        public string Id { get; set; }

        [HtmlAttributeName("asp-style")]
        public string Style { get; set; }

        [HtmlAttributeName("asp-single-row")]
        public bool SingleRow { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            var preContent = $"<div class=\"form-group {(SingleRow ? "row" : "")}\" id=\"{Id}\" style=\"{Style}\">" +
                $"<label class=\"{(SingleRow ? "col-xl-2 col-form-label" : "text-bold")}\">{HcaLabel.HtmlEncode()}</label>";
            var postContent = "";

            if (SingleRow) preContent += $"<div class=\"col-xl-10 input-group\">";

            if (context.AllAttributes["type"]?.Value?.ToString() == "checkbox")
            {
                preContent += "<label class=\"form-control c-checkbox\" style=\"padding: 0.375rem 0; border: 0;\">";
                postContent += "<span class=\"fa fa-check\"></span></label>";
            }

            postContent += SingleRow ? "</div></div>" : "</div>";

            if (ReadOnly || !(_httpContext.IsEditMode() || _httpContext.IsNewMode()))
            {
                if (context.AllAttributes["type"]?.Value?.ToString() == "checkbox")
                {
                    // disable the checkbox, add the additional content and stop processing
                    output.Attributes.SetAttribute("disabled", "disabled");
                    output.PreElement.SetHtmlContent(preContent);
                    output.PostElement.SetHtmlContent(postContent);
                    return;
                }
                else
                {
                    var value = output.Attributes["value"]?.Value?.ToString();
                    var name = output.Attributes["name"]?.Value?.ToString();
                    var id = output.Attributes["id"]?.Value?.ToString();

                    // change the input to hidden
                    output.Reinitialize("input", TagMode.StartTagAndEndTag);
                    output.Attributes.SetAttribute("type", "hidden");
                    output.Attributes.SetAttribute("value", value);
                    output.Attributes.SetAttribute("name", name);
                    output.Attributes.SetAttribute("id", id);

                    // append a label
                    preContent += $"<label class=\"form-control\">{value}</label>";

                    // set the content and stop processing
                    output.PreElement.SetHtmlContent(preContent);
                    output.PostElement.SetHtmlContent(postContent);
                    return;
                }
            }

            var className = "form-control";
            if (output.Attributes.Any(a => a.Name == "class"))
            {
                className += $" {output.Attributes["class"].Value}";
                output.Attributes.Remove(output.Attributes.First(a => a.Name == "class"));
            }
            output.Attributes.Add("class", className);

            if (NoPrompt)
            {
                output.Attributes.Add("autocomplete", "off");
            }

            output.PreElement.SetHtmlContent(preContent);
            output.PostElement.SetHtmlContent(postContent);
        }
    }
}
