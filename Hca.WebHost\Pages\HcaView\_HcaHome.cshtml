﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager

    <form action="/search" method="get">
        <div class="input-group input-group-lg">
            <input class="form-control form-control-lg rounded-0" type="text" name="searchTerm" placeholder="Search" />
            <div class="input-group-append">
                <input class="btn btn-info btn-lg b0 rounded-0" type="submit" value="Search">
            </div>
        </div>
    </form>
    <p class="my-3 text-muted text-center">
    </p>
    <div class="row">
        <div class="col-lg-4">
            <div class="card b">
                <div class="card-body text-center">
                    <a class="link-unstyled text-warning" href="@Urls.Clients">
                        <em class="fa-5x far fa-gem mb-3"></em>
                        <br />
                        <span class="h4">Clients</span>
                        <br />
                        <div class="text-sm text-muted">View all &rarr;</div>
                    </a>
                </div>
            </div>
        </div>
        @if (await ViewManager.IsAdminUser)
        {
            <div class="col-lg-4">
                <div class="card b">
                    <div class="card-body text-center">
                        <a class="link-unstyled text-dark" href="/admin">
                            <em class="fa-5x fa fa-cogs mb-3"></em>
                            <br />
                            <span class="h4">Admin</span>
                            <br />
                            <div class="text-sm text-muted">View all &rarr;</div>
                        </a>
                    </div>
                </div>
            </div>
        }
    </div>