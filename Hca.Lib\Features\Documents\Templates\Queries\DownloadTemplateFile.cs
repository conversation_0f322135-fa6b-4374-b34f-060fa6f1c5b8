﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;
using MediatR;

namespace Hca.Lib.Features.TemplateFiles.Templates.Queries
{
    public class DownloadTemplateFile : IRequest<byte[]>
    {
        public DownloadTemplateFile(Guid templateId, Guid fileId)
        {
            TemplateId = templateId;
            FileId = fileId;
        }

        public Guid TemplateId { get; }
        public Guid FileId { get; }
    }

    public class RetrieveTemplateFileHandler : DapperRequestHandler<DownloadTemplateFile, byte[]>
    {
        private readonly IMediator _mediator;

        public RetrieveTemplateFileHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<byte[]> OnHandleAsync(IDbHelper db, DownloadTemplateFile request)
        {
            var dto = await db.GetAsync<TemplateFileDto>(request.FileId);
            return await _mediator.Send(new DownloadBlob(
                    StorageConstants.TemplateContainerName,
                    $"{dto.TemplateId}/{dto.FileName}"
                ));
        }
    }
}
