﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Priority.Index
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Priority Name" }
        },
        Header = "Priority",
        UrlPath = Urls.Priority,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />