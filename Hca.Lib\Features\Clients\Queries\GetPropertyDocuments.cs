﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using OpenXmlPowerTools;

namespace Hca.Lib.Features.Clients.Queries;

public class GetPropertyDocuments : IQueryMany<PropertyDocumentDto>
{
    public GetPropertyDocuments(Guid clientId, Guid propertyId, bool? hasQrCode = null)
    {
        ClientId = clientId;
        PropertyId = propertyId;
        HasQrCode = hasQrCode;
    }

    public Guid ClientId { get; }
    public Guid PropertyId { get; }
    public bool? HasQrCode { get; }
}

public class GetPropertyDocumentsHandler : DapperRequestHandler<GetPropertyDocuments, DtoSet<PropertyDocumentDto>>
{
    public GetPropertyDocumentsHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<DtoSet<PropertyDocumentDto>> OnHandleAsync(IDbHelper db, GetPropertyDocuments request)
    {
        var sql = $"WHERE {nameof(PropertyDocumentDto.PropertyId)} = @{nameof(GetPropertyDocuments.PropertyId)}";
        if (request.HasQrCode.HasValue) sql += $" AND {nameof(PropertyDocumentDto.QrCodeId)} IS {(request.HasQrCode.Value ? "NOT" : "")} NULL";

        var dtos = await db.GetListAsync<PropertyDocumentDto>(sql, request);

        foreach (var doc in dtos)
        {
            if (doc.PropertyDocumentType == PropertyDocumentType.Unspecified &&
                !string.IsNullOrWhiteSpace(doc.DocumentType))
            {
                doc.PropertyDocumentType = JsonTool.ParseDocumentType(doc.DocumentType);
            }
        }

        return DtoSet.From(dtos.OrderByDescending(d => d.DocumentDate));
    }
}
