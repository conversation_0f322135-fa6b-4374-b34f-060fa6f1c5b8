﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Commands
{
    public class RemoveFieldFromTemplateSection : ICommand
    {
        public RemoveFieldFromTemplateSection(
            Guid templateId,
            Guid sectionId,
            Guid fieldId)
        {
            TemplateId = templateId;
            SectionId = sectionId;
            FieldId = fieldId;
        }

        public Guid TemplateId { get; }
        public Guid SectionId { get; }
        public Guid FieldId { get; }
    }

    public class RemoveFieldFromTemplateSectionHandler : DapperRequestHandler<RemoveFieldFromTemplateSection, CommandResult>
    {
        public RemoveFieldFromTemplateSectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, RemoveFieldFromTemplateSection request)
        {
            await db.DeleteAsync<TemplateFieldDto>(request.FieldId);

            return CommandResult.Success();
        }
    }
}
