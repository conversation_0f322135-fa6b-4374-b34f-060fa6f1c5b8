﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;

namespace Hca.Lib.Data.Core
{
    public abstract class DapperPagedQueryHandler<TQuery, TResult> : DapperRequestHandler<TQuery, PagedDtoSet<TResult>>
        where TQuery: PagedQuery<TResult> where TResult: class, new()
    {
        public DapperPagedQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        protected abstract (string sql, object args) Build(TQuery request);

        public override async Task<PagedDtoSet<TResult>> OnHandleAsync(IDbHelper db, TQuery request)
        {
            (string sql, object args) = Build(request);

            var (Items, Total) = await db.QueryPageAsync<TResult>(
                sql,
                request.Page,
                request.PageSize,
                args);

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
