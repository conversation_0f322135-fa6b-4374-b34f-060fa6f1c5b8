﻿@using Hca.WebHost.Areas.Inspections.Models

@model InspectionWizardModel

<style>
    .wizard > .content > .body {
        position: relative !important;
    }

    #divInspectionWizard div.card {
        height: 100%;
    }
</style>

<div id="divInspectionWizard">

    <h3>Details</h3>
    @if (Model.IsNew)
    {
        // for a new inspection there is no report, this form allows one to be created
        <section>
            <fieldset>
                <div class="form-group row">
                    <label class="col-md-2 col-form-label">Client</label>
                    <div class="col-md-10">
                        <p class="form-control-plaintext">@Model.Inspection.ClientName</p>
                    </div>
                </div>
            </fieldset>
            <fieldset>
                <div class="form-group row">
                    <label class="col-md-2 col-form-label">Location</label>
                    <div class="col-md-10">
                        <p class="form-control-plaintext">@Model.Inspection.PropertyCode</p>
                    </div>
                </div>
            </fieldset>
            <fieldset>
                <div class="form-group row">
                    <label class="col-md-2 col-form-label">Inspection Type</label>
                    <div class="col-md-10">
                        <select id="ddlInspectionType" class="form-control">
                            <option selected="selected" value="">Please select</option>
                            @*<option value="@Hca.Lib.Features.Inspections.InspectionType.AsbestosReinspection">Asbestos Reinspection</option>
                            <option value="@Hca.Lib.Features.Inspections.InspectionType.AsbestosManagementSurvey">Asbestos Management Survey</option>
                            <option value="@Hca.Lib.Features.Inspections.InspectionType.AsbestosRefurbishmentSurvey">Asbestos Refurbishment Survey</option>
                            <option value="@Hca.Lib.Features.Inspections.InspectionType.AsbestosDemolitionSurvey">Asbestos Demolition Survey</option>*@
                        </select>
                    </div>
                </div>
            </fieldset>
        </section>
    }
    else
    {
        // if a report has already been created it then continue
        <section data-mode="async" data-url="/inspections/@(Model.Inspection.Id)/DetailsStep"></section>
    }

    <h3>Samples</h3>
    <section data-mode="async" data-url="/inspections/@(Model.Inspection.Id)/SamplesStep"></section>

    <h3>Reports</h3>
    <section data-mode="async" data-url="/inspections/@(Model.Inspection.Id)/ReportStep"></section>

    <h3>Publish</h3>
    <section data-mode="async" data-url="/inspections/@(Model.Inspection.Id)/PublishStep"></section>
</div>

@this.ScriptBlock(
                @<script type='text/javascript'>

                    const disableNextStep = () => {
                        $("#divInspectionWizard div.actions ul").hide();
                    }
                    const enableNextStep = () => {
                        $("#divInspectionWizard div.actions ul").show();
                    }

                    const onStepChanging = async (event, currentIndex, newIndex) => {
                        var firstStep = $("#divInspectionWizard").steps("getStep", 0);

                        if (firstStep.contentMode === 0 && currentIndex == 0) {
                            // if the first step is submitted and is inline then create new inspection here
                            // then reload the page? bit clunky but whatevs for now
                            var model = {
                                id: '@(Model.Inspection.Id)',
                                clientId: '@(Model.Inspection.ClientId)',
                                propertyId: '@(Model.Inspection.PropertyId)',
                            };

                            $.ajax({
                                type: 'POST',
                                url: '/inspections',
                                dataType: 'json',
                                contentType: 'application/json; charset=utf-8',
                                data: JSON.stringify(model),
                                async: false
                            });

                            firstStep.contentMode = "async";
                            firstStep.contentUrl = "/inspections/@(Model.Inspection.Id)/DetailsStep";
                        }

                        return true;
                    };

                    var wizard = (startIndex) => {
                        $("#divInspectionWizard").steps({
                            headerTag: "h3",
                            bodyTag: "section",
                            transitionEffect: "slideLeft",
                            enableKeyNavigation: false,
                            @if (Model.IsNew)
                            {
                                <text>onStepChanging,</text>
                            }
                        })
                    };

                    $(document).ready(function () {
                        wizard(@((int)Model.Stage));
                    });
</script>)
