﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class DeleteSitePlan : ICommand
    {
        public DeleteSitePlan(Guid planId)
        {
            PlanId = planId;
        }

        public Guid PlanId { get; }
    }

    public class DeleteSitePlanHandler : DapperRequestHandler<DeleteSitePlan, CommandResult>
    {
        private readonly SiteCountsService _siteCountsService;

        public DeleteSitePlanHandler(IDbHelper dbHelper, SiteCountsService siteCountsService) : base(dbHelper)
        {
            _siteCountsService = siteCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteSitePlan request)
        {
            var plan = await db.GetAsync<SitePlanDto>(request.PlanId);
            await db.DeleteAsync<SitePlanDto>(request.PlanId);
            _siteCountsService.ClearSiteCountsAsync(plan.SiteId);
            return CommandResult.Success();
        }
    }
}
