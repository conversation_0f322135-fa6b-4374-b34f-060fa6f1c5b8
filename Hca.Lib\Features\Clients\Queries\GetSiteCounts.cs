﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries;

public class GetSiteCounts : IQuery<SiteCountsModel>
{
    public Guid ClientId { get; set; }
    public Guid SiteId { get; set; }
    public GetSiteCounts(Guid clientId, Guid siteId)
    {
        ClientId = clientId;
        SiteId = siteId;
    }
}

public class GetSiteCountsHandler : DapperRequestHandler<GetSiteCounts, SiteCountsModel>
{
    public GetSiteCountsHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override Task<SiteCountsModel> OnHandleAsync(IDbHelper db, GetSiteCounts request)
    {
        var propertyTask = db.CountAsync<PropertyDto>($"WHERE {nameof(PropertyDto.SiteId)} = @{nameof(GetSiteCounts.SiteId)} AND {nameof(PropertyDto.Archived)} IS NULL", request);
        var sitePlanTask = db.CountAsync<SitePlanDto>($"WHERE {nameof(SitePlanDto.SiteId)} = @{nameof(GetSiteCounts.SiteId)}", request);

        return Task.WhenAll(propertyTask, sitePlanTask).ContinueWith(_ =>
        {
            return new SiteCountsModel
            {
                PropertyCount = propertyTask.Result,
                SitePlanCount = sitePlanTask.Result
            };
        });
    }
}