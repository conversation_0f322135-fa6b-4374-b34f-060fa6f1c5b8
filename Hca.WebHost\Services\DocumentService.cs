﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Documents.Commands;
using Hca.Lib.Features.Documents.Queries;
using MediatR;

namespace Hca.WebHost.Services
{
    public class DocumentService
    {
        private readonly IMediator _mediator;

        public DocumentService(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task SetFieldTextAsync(
            Guid documentId,
            Guid sectionId,
            Guid fieldId,
            string text,
            CancellationToken cancellationToken) =>
            _mediator.Send(new SetDocumentTextField(
                    documentId,
                    sectionId,
                    fieldId,
                    text),
                cancellationToken);

        public Task SetFieldChoicesAsync(
            Guid documentId,
            Guid sectionId,
            Guid fieldId,
            IEnumerable<Guid> chosen,
            CancellationToken cancellationToken) =>
            _mediator.Send(new SetDocumentChoicesField(
                    documentId,
                    sectionId,
                    fieldId,
                    chosen),
                cancellationToken);

        public Task<byte[]> RetrieveDocument(Guid documentId) => _mediator.Send(new DownloadDocument(documentId));

        public Task<IEnumerable<DocumentSectionDto>> GetDocumentSectionsAsync(Guid documentId, CancellationToken cancellationToken) =>
            _mediator.Send(new FindDocumentSections(documentId), cancellationToken).ContinueWith(t => t.Result.Items);
    }
}
