﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Features.Clients.Domain.Inspections.Commands;
using MediatR;

namespace Hca.Lib.Features.Clients.Services
{
    public class UploadSampleImage : IRequest<bool>
    {
        public UploadSampleImage(
            Guid inspectionId,
            Guid sampleId,
            string filename,
            byte[] imageData)
        {
            InspectionId = inspectionId;
            SampleId = sampleId;
            Filename = filename;
            ImageData = imageData;
        }

        public Guid InspectionId { get; }
        public Guid SampleId { get; }
        public string Filename { get; }
        public byte[] ImageData { get; }
    }

    public class SampleImageUploadService : BlobRequestHandler<UploadSampleImage, bool>
    {
        private readonly IMediator _mediator;

        public SampleImageUploadService(
            IMediator mediator,
            BlobServiceClient client) : base(client, StorageConstants.SampleImagesContainerName) {
            _mediator = mediator;
        }

        public override async Task<bool> OnHandleAsync(
            BlobContainerClient client,
            UploadSampleImage request,
            CancellationToken cancellationToken)
        {
            var blobName = $"{request.InspectionId}/{request.SampleId}/{request.Filename}";
            var blob = client.GetBlobClient(blobName);
            var i = 1;
            var lastDot = blobName.LastIndexOf('.');

            while (await blob.ExistsAsync(cancellationToken))
            {
                var newName = $"{blobName[..lastDot]} ({i}){blobName[lastDot..]}";
                blob = client.GetBlobClient(newName);
                i++;
            }

            using var stream = new MemoryStream();
            await stream.WriteAsync(request.ImageData.AsMemory(0, request.ImageData.Length), cancellationToken);
            stream.Seek(0, SeekOrigin.Begin);
            await blob.UploadAsync(stream, cancellationToken);

            await _mediator.Send(new AddImageToSample(
                request.InspectionId,
                request.SampleId,
                blob.Uri), cancellationToken);

            return true;
        }
    }
}
