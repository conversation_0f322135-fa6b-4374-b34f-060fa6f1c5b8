﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class FindTemplates : IQueryMany<TemplateDto>
    {
        public FindTemplates(TemplateType? type)
        {
            Type = type;
        }

        public TemplateType? Type { get; }
    }

    public class FindTemplatesHandler : <PERSON><PERSON><PERSON><PERSON>questHandler<FindTemplates, DtoSet<TemplateDto>>
    {
        public FindTemplatesHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<TemplateDto>> OnHandleAsync(IDbHelper db, FindTemplates request)
        {
            if (request.Type.HasValue)
            {
                return DtoSet.From(await db.GetListAsync<TemplateDto>($"WHERE TemplateType = @{nameof(FindTemplates.Type)}", request));
            }

            return DtoSet.From(await db.GetListAsync<TemplateDto>());
        }
    }
}
