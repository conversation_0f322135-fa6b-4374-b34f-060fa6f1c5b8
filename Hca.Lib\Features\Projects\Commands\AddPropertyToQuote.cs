﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Commands
{
    public class AddPropertyToQuote : ICommand
    {
        public AddPropertyToQuote(Guid quoteId, Guid propertyId, double proposedFee, string notes)
        {
            QuoteId = quoteId;
            PropertyId = propertyId;
            ProposedFee = proposedFee;
            Notes = notes;
        }

        public Guid QuoteId { get; }
        public Guid PropertyId { get; }
        public double ProposedFee { get; }
        public string Notes { get; }
    }

    public class AddPropertyToQuoteHandler : DapperRequestHandler<AddPropertyToQuote, CommandResult>
    {
        public AddPropertyToQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddPropertyToQuote request)
        {
            await db.InsertAsync(new QuotePropertyDto
            {
                Id = Guid.NewGuid(),
                QuoteId = request.QuoteId,
                PropertyId = request.PropertyId,
                Notes = request.Notes,
                ProposedFee = request.ProposedFee,
            });

            return CommandResult.Success();
        }
    }
}
