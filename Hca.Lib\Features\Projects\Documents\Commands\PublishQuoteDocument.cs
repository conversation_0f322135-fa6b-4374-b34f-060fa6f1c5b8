﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Documents.Commands;
using Hca.Lib.Features.Documents.Queries;
using Hca.Lib.Features.Projects.Documents.Publishing;
using Hca.Lib.Features.Projects.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Documents.Commands
{
    public class PublishQuoteDocument : ICommand
    {
        public PublishQuoteDocument(
            Guid documentId,
            Guid quoteId)
        {
            DocumentId = documentId;
            QuoteId = quoteId;
        }

        public Guid DocumentId { get; }
        public Guid QuoteId { get; }
    }

    public class PublishQuoteDocumentHandler : IRequestHandler<PublishQuoteDocument, CommandResult>
    {
        private readonly IMediator _mediator;
        private readonly QuoteDocumentBuilder _documentBuilder;

        public PublishQuoteDocumentHandler(
            IMediator mediator,
            QuoteDocumentBuilder documentBuilder)
        {
            _mediator = mediator;
            _documentBuilder = documentBuilder;
        }

        public async Task<CommandResult> Handle(PublishQuoteDocument request, CancellationToken cancellationToken)
        {
            var quoteTemplate = await _mediator.Send(new GetQuoteTemplate(), cancellationToken);
            var documentModel = await _mediator.Send(new GetDocumentModel(request.DocumentId, quoteTemplate), cancellationToken);
            var quote = await _mediator.Send(new GetQuote(request.QuoteId), cancellationToken);
            var client = await _mediator.Send(new GetClientByIdQuery(quote.ClientId), cancellationToken);
            var properties = await _mediator.Send(new GetQuoteProperties(request.QuoteId), cancellationToken);
            var headOfficeAddress = client.AddressId.HasValue
                ? await _mediator.Send(new GetAddress(client.AddressId.Value), cancellationToken)
                : null;
            var contact = quote.ContactId.HasValue 
                ? (await _mediator.Send(new GetContactsByClientId(quote.ClientId), cancellationToken)).Items.Single(c => c.Id == quote.ContactId)
                : null;

            var model = new QuoteDocumentModel
            {
                DocumentModel = documentModel,
                Quote = quote,
                Client = client,
                Contact = contact,
                Properties = properties,
                HeadOfficeAddress = headOfficeAddress
            };

            var file = await _documentBuilder.BuildDocument(model);

            await _mediator.Send(new PublishDocument(
                request.DocumentId,
                    "clients",
                    $"{client.Id}/quotes/{quote.Id}/documents/{request.DocumentId}/{DateTime.UtcNow:yyyy_MM_dd_HH_mm_ss}.docx",
                    file,
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                ), cancellationToken);

            return CommandResult.Success();
        }
    }
}
