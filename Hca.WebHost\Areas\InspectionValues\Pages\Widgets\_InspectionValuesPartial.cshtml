﻿@model Hca.WebHost.Areas.InspectionValues.Models.InspectionValuesModel
@using Hca.Lib.Features.Inspections

<style>
    td {
        vertical-align: middle !important;
    }
</style>

<div class="card card-default">
    <div class="card-header">
        <h5>@Model.Header.ToUpper()</h5>
        <small>@Model.SubHeader</small>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <thead class="thead-dark">
                    <tr>
                        <th width="18"></th>
                        <th>@Model.ColumnNames[nameof(ValueDto.DisplayText)]</th>
                        @if (Model.ColumnNames.ContainsKey(nameof(ValueDto.HintText)))
                        {
                            <th>@Model.ColumnNames[nameof(ValueDto.HintText)]</th>
                        }
                        <th></th>
                    </tr>
                </thead>
                <tbody class="sortable">
                    @foreach (var value in Model.InspectionValues)
                    {
                        <tr data-sort-url="@Model.UrlPath" data-sort-id="@value.Id">
                            <td><em class="fas fa-bars fa-fw text-muted mr-3"></em></td>
                            <td>@value.DisplayText</td>
                            @if (Model.ColumnNames.ContainsKey(nameof(ValueDto.HintText)))
                            {
                                <td>@(value.HintText[0..50])...</td>
                            }
                            <td style="display: inline-flex;">
                                <a class="btn btn-sm btn-info mr-2 command-edit" href="@Model.UrlPath/@value.Id?@Urls.EditMode">
                                    <em class="fa fa-edit fa-fw"></em>edit
                                </a>
                                <delete action="@Model.UrlPath/@value.Id?handler=delete"
                                        item-name="&quot;@value.DisplayText&quot;"></delete>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <a class="btn btn-success" href="@Model.UrlPath/new?@Urls.NewMode">New</a>
    </div>
</div>
