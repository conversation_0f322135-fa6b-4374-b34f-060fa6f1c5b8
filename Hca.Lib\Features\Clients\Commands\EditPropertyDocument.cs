﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class EditPropertyDocument : ICommand
    {
        public EditPropertyDocument(
            Guid documentId,
            DateTime documentDate,
            PropertyDocumentType documentType,
            string companyName,
            string notes,
            DateTime? nextInspectionDate)
        {
            DocumentId = documentId;
            DocumentDate = documentDate;
            CompanyName = companyName;
            DocumentType = documentType;
            Notes = notes;
            NextInspectionDate = nextInspectionDate;
        }

        public Guid DocumentId { get; }
        public DateTime DocumentDate { get; }
        public string CompanyName { get; }
        public PropertyDocumentType DocumentType { get; }
        public string Notes { get; }
        public DateTime? NextInspectionDate { get; }
    }

    public class EditPropertyDocumentHandler : DapperRequestHandler<EditPropertyDocument, CommandResult>
    {
        public EditPropertyDocumentHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EditPropertyDocument request)
        {
            var dto = await db.GetAsync<PropertyDocumentDto>(request.DocumentId);
            dto.CompanyName = request.CompanyName;
            dto.DocumentDate = request.DocumentDate;
            dto.PropertyDocumentType = request.DocumentType;
            dto.Notes = request.Notes;
            dto.NextInspection = request.NextInspectionDate;

            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}

