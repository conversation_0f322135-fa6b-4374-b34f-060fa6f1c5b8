﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetProperty : IQuery<PropertyDtoExtended>
    {
        public GetProperty(Guid propertyId)
        {
            PropertyId = propertyId;
        }

        public Guid PropertyId { get; }
    }

    public class GetPropertyHandler : DapperRequestHandler<GetProperty, PropertyDtoExtended>
    {
        public GetPropertyHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PropertyDtoExtended> OnHandleAsync(IDbHelper db, GetProperty request)
        {
            var sql = $"SELECT " +
                $"  {TableNames.Properties}.*, " +
                $"  {nameof(PropertyDtoExtended.SiteName)}, " +
                $"  (SELECT " +
                $"      MAX({nameof(PropertyDocumentDto.NextInspection)}) " +
                $"      FROM {TableNames.PropertyDocuments} " +
                $"      WHERE {nameof(PropertyDocumentDto.PropertyId)} = {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)}) " +
                $"      AS {nameof(PropertyDtoExtended.NextInspection)} " +
                $"FROM {TableNames.Properties} " +
                $"LEFT JOIN {TableNames.Sites} " +
                $"  ON {TableNames.Sites}.{nameof(SiteDtoExtended.Id)} = {nameof(PropertyDtoExtended.SiteId)} " +
                $"WHERE {TableNames.Properties}.{nameof(PropertyDtoExtended.Id)} = @{nameof(GetProperty.PropertyId)}";

            var dto = await db.QuerySingleOrDefaultAsync<PropertyDtoExtended>(sql, request);

            return dto;
        }
    }
}
