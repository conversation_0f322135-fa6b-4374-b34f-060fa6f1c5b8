@inject Hca.WebHost.Pipeline.ViewManager ViewManager
<!-- START Sidebar (left)-->
<div class="aside-inner">
    <nav class="sidebar" data-sidebar-anyclick-close="">
        <!-- START sidebar nav-->
        <ul class="sidebar-nav">
            <!-- START user info-->
           @*  <li class="has-user-block">
                <div id="user-block" class="collapse">
                    <div class="item user-block">
                        <!-- User picture-->
                        <div class="user-block-picture">
                            <div class="user-block-status">
                                <img src="~/images/user/02.jpg" alt="Avatar" width="60" height="60" class="img-thumbnail rounded-circle" />
                                <div class="circle bg-success circle-lg"></div>
                            </div>
                        </div>
                        <!-- Name and Job-->
                        <div class="user-block-info">
                            <span class="user-block-name">Hello, <PERSON></span>
                            <span class="user-block-role">Designer</span>
                        </div>
                    </div>
                </div>
            </li> *@
            <!-- END user info-->
            <!-- Iterates over all sidebar items-->
            <li class="nav-heading">
                <span>Admin Navigation</span>
            </li>

            <li class="@Html.IsActive(area: "Users")">
                <a href="@Urls.Users" title="Users">
                    <em class="fa fa-user"></em>
                    <span data-localize="sidebar.nav.SINGLEVIEW">Users</span>
                </a>
            </li>
            <li class="@Html.IsActive(area: "InspectionValues")">
                <a href="#inspectionValueLists" title="Value Lists" data-toggle="collapse">
                    <em class="fa fa-map"></em>
                    <span data-localize="sidebar.nav.inspectionValueLists.INSPECTIONVALUES">Value Lists</span>
                </a>
                <ul id="inspectionValueLists" class="sidebar-nav sidebar-subnav collapse">
                    @*collapse*@
                    <li class="sidebar-subnav-header">Value Lists</li>
                    @* <li class="@Html.IsActive("InspectionType", "InspectionValues")">
                        <a href="@Urls.InspectionType" title="Inspection Types">
                            <span data-localize="sidebar.nav.inspectionValueLists.INSPECTIONTYPE">Inspection Types</span>
                        </a>
                    </li> *@
                    <li class="@Html.IsActive("Floors", "InspectionValues")">
                        <a href="@Urls.Floors" title="Floors">
                            <span data-localize="sidebar.nav.inspectionValueLists.FLOORS">Floors</span>
                        </a>
                    </li>
                    @* <li class="@Html.IsActive("FloorPlanColours", "InspectionValues")">
                        <a href="@Urls.FloorPlanColours" title="Floor Plan Colours">
                            <span data-localize="sidebar.nav.inspectionValueLists.FLOORPLANCOLOURS">Floor Plan Colours</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Material", "InspectionValues")">
                        <a href="@Urls.Material" title="Material">
                            <span data-localize="sidebar.nav.inspectionValueLists.MATERIAL">Material</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Quantity", "InspectionValues")">
                        <a href="@Urls.Quantity" title="Quantity">
                            <span data-localize="sidebar.nav.inspectionValueLists.QUANTITY">Quantity</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Recommendation", "InspectionValues")">
                        <a href="@Urls.Recommendation" title="Recommendation">
                            <span data-localize="sidebar.nav.inspectionValueLists.RECOMMENDATION">Recommendation</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Priority", "InspectionValues")">
                        <a href="@Urls.Priority" title="Priority">
                            <span data-localize="sidebar.nav.inspectionValueLists.PRIORITY">Priority</span>
                        </a>
                    </li> *@
                </ul>
            </li>
            <li class="@Html.IsActive(area: "DeletedData")">
                <a href="#deletedData" title="Deleted Data" data-toggle="collapse">
                    <em class="fa fa-box"></em>
                    <span data-localize="sidebar.nav.deletedData.ARCHIVEDDATA">Deleted Data</span>
                </a>
                <ul id="deletedData" class="sidebar-nav sidebar-subnav collapse">
                    @*collapse*@
                    <li class="sidebar-subnav-header">Deleted Data</li>
                    <li class="@Html.IsActive("Clients", "DeletedData")">
                        <a href="@Urls.DeletedClients" title="Clients">
                            <span data-localize="sidebar.nav.deletedData.CLIENTS">Clients</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Sites", "DeletedData")">
                        <a href="@Urls.DeletedSites" title="Sites">
                            <span data-localize="sidebar.nav.deletedData.SITES">Sites</span>
                        </a>
                    </li>
                    <li class="@Html.IsActive("Properties", "DeletedData")">
                        <a href="@Urls.DeletedProperties" title="Properties">
                            <span data-localize="sidebar.nav.deletedData.PROPERTIES">Properties</span>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
        <!-- END sidebar nav-->
    </nav>
</div>
<!-- END Sidebar (left)-->
