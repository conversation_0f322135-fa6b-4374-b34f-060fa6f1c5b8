﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Inspections.Commands
{
    public class SetInspectionDefaultTemplate : IRequest<CommandResult>
    {
        public SetInspectionDefaultTemplate(
            Guid inspectionTypeId,
            Guid templateId)
        {
            InspectionTypeId = inspectionTypeId;
            TemplateId = templateId;
        }

        public Guid InspectionTypeId { get; }
        public Guid TemplateId { get; }
    }

    public class SetInspectionDefaultTemplateHandler : DapperRequestHandler<SetInspectionDefaultTemplate, CommandResult>
    {
        public SetInspectionDefaultTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetInspectionDefaultTemplate request)
        {
            await db.ExecuteAsync(
                $"UPDATE {TableNames.ValueLists} " +
                $"SET InspectionTypeDefaultTemplateId = @{nameof(request.TemplateId)} " +
                $"WHERE Id = @{nameof(request.InspectionTypeId)}",
                request);

            return CommandResult.Success();
        }
    }
}
