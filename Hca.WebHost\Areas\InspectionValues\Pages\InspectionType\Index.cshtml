﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.InspectionType.Index

<style>
    td {
        vertical-align: middle !important;
    }
</style>

<div class="content-heading">
    <div>
        Inspection Types
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered table-hover">
                <thead class="thead-dark">
                    <tr>
                        <th width="18"></th>
                        <th>Inspection Type Name</th>
                        <th>Default Report Template</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody class="sortable">
                    @foreach (var value in Model.Values)
                    {
                        <tr data-sort-url="@Urls.InspectionType" data-sort-id="@value.Id">
                            <td><em class="fas fa-bars fa-fw text-muted mr-3"></em></td>
                            <td>@value.DisplayText</td>
                            <td>@Model.GetDefaultTemplateNameAsync(value.Id).Result</td>
                            <td style="display: inline-flex;">
                                <a class="btn btn-sm btn-info mr-2 command-edit" href="@Urls.InspectionType/@value.Id?@Urls.EditMode">
                                    <em class="fa fa-edit fa-fw"></em>edit
                                </a>
                                <delete action="@Urls.InspectionType/@value.Id?handler=delete"
                                        item-name="&quot;@value.DisplayText&quot;"></delete>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <a class="btn btn-success" href="@Urls.InspectionType/new?@Urls.NewMode">New</a>
    </div>
</div>
