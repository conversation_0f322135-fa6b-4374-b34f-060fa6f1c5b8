{"name": "hcaportal", "version": "0.0.0", "description": "Home Counties Asbestos Ltd - Customer Portal", "author": "@themicon_co", "license": "https://wrapbootstrap.com/help/licenses", "private": true, "scripts": {"start": "gulp serve", "modernizr": "modernizr -c modernizr-config.json -d node_modules/modernizr/modernizr.custom.js", "postinstall": "npm run modernizr"}, "dependencies": {"@fortawesome/fontawesome-free": "5.5.0", "@fullcalendar/bootstrap": "4.3.0", "@fullcalendar/core": "4.3.1", "@fullcalendar/daygrid": "4.3.0", "@fullcalendar/interaction": "4.3.0", "@fullcalendar/list": "4.3.0", "@fullcalendar/timegrid": "4.3.0", "@ttskch/select2-bootstrap4-theme": "1.0.4", "animate.css": "3.7.0", "bootstrap": "4.5.0", "bootstrap-colorpicker": "3.3.0", "bootstrap-datepicker": "1.8.0", "bootstrap-filestyle": "github:markusslima/bootstrap-filestyle#v2.1.0", "bootstrap-slider": "10.2.3", "bootstrap-tagsinput": "github:bootstrap-tagsinput/bootstrap-tagsinput#0.8.0", "bootstrap-wysiwyg": "2.0.1", "chart.js": "^2.9.4", "chartist": "0.11.0", "chosen-js": "1.8.7", "components-jqueryui": "github:components/jqueryui", "cropper": "4.0.0", "d3": "3.5.16", "datatables.net": "^1.11.3", "datatables.net-bs": "^1.11.3", "datatables.net-bs4": "^1.11.3", "datatables.net-buttons": "1.5.4", "datatables.net-buttons-bs": "1.5.4", "datatables.net-keytable": "2.5.0", "datatables.net-keytable-bs": "2.5.0", "datatables.net-responsive": "2.2.3", "datatables.net-responsive-bs": "2.2.3", "dropzone": "5.5.1", "easy-pie-chart": "2.1.7", "fastclick": "1.0.6", "flot": "github:themicon/flot", "html5sortable": "0.9.8", "i18next": "12.0.0", "i18next-xhr-backend": "1.5.1", "ika.jvectormap": "themicon/ika.jvectormap", "inputmask": "4.0.3", "jqcloud2": "2.0.3", "jquery": "3.5.1", "jquery-bootgrid": "1.3.1", "jquery-knob": "1.2.11", "jquery-slimscroll": "1.3.8", "jquery-sparkline": "2.4.0", "jquery-steps": "1.1.0", "jquery-ui-touch-punch": "0.2.3", "jquery-validation": "^1.19.3", "jquery.easing": "1.4.1", "jquery.flot.spline": "github:themicon/jquery.flot.spline", "jquery.flot.tooltip": "github:krzysu/flot.tooltip", "jquery.gmap": "github:marioestrada/jQuery-gMap", "js-storage": "1.0.4", "jszip": "^3.7.1", "loaders.css": "0.1.2", "matchmedia": "0.1.2", "modernizr": "^3.11.8", "moment": "2.22.2", "morris.js.so": "0.5.1", "nestable": "github:themicon/nestable", "parsleyjs": "2.8.1", "pdfmake": "0.1.56", "popper.js": "1.16.0", "raphael": "2.2.7", "rickshaw": "1.6.6", "screenfull": "3.3.3", "select2": "4.0.6-rc.1", "simple-line-icons": "2.4.1", "spinkit": "1.2.5", "summernote": "^0.8.18", "sweetalert": "2.1.2", "weather-icons": "github:erikflowers/weather-icons", "whirl": "github:themicon/whirl", "x-editable": "1.5.1"}, "devDependencies": {"@babel/core": "7.7.4", "@babel/preset-env": "7.7.4", "cssnano": "^5.0.12", "del": "3.0.0", "gulp": "^4.0.2", "gulp-babel": "8.0.0", "gulp-concat": "2.6.1", "gulp-filter": "5.1.0", "gulp-load-plugins": "1.6.0", "gulp-postcss": "9.0.1", "gulp-rename": "1.4.0", "gulp-rtlcss": "1.3.0", "gulp-sass": "^5.0.0", "gulp-sourcemaps": "2.6.4", "gulp-uglify": "3.0.2", "postcss": "^8.5.6", "sass": "^1.44.0", "uglify-save-license": "0.4.1"}}