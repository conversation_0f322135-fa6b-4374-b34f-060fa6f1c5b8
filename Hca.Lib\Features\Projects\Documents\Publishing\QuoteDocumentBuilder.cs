﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Projects.Queries;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Projects.Documents.Publishing
{
    public class QuoteDocumentBuilder
    {
        private readonly DocumentCompiler _documentBuilder;

        public QuoteDocumentBuilder(DocumentCompiler documentBuilder)
        {
            _documentBuilder = documentBuilder;
        }

        public Task<byte[]> BuildDocument(QuoteDocumentModel model)
        {
            model.DocumentModel.Tags =
                TagFactory.CreateStandardTags()
                .Concat(TagFactory.CreateStandardTags(model.Client))
                .Concat(BuildQuoteTags(model.Quote, model.Properties))
                .Concat(BuildContactTags(model.Contact))
                .Concat(TagFactory.CreateStandardTags(model.Client, model.HeadOfficeAddress, "HeadOffice"));

            return _documentBuilder.BuildDocument(model.DocumentModel);
        }

        private IEnumerable<(string Tag, string Text)> BuildContactTags(ContactDto contact)
        {
            return new List<(string, string)>
            {
                ("ContactFirstName", contact?.FirstName),
            };
        }

        private IEnumerable<(string Tag, string Text)> BuildQuoteTags(QuoteDto quote, DtoSet<QuotePropertyWithAddressDto> properties)
        {
            return new List<(string, string)>
            {
                ("QuoteNumber", quote.QuoteNumber),
                ("BaseFee", quote.BaseFee.ToString()),
                ("ProposedFee", (quote.BaseFee + properties.Items.Sum(p => p.ProposedFee)).ToString()),
            };
        }
    }
}
