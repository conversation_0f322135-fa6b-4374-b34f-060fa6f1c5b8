﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;

namespace Hca.WebHost.Areas.InspectionValues.Pages;

public class FloorModel : InspectionValueEditPage
{
    public FloorModel(
        ValueListsService inspectionValuesService,
        IMediator mediator,
        ViewManager viewManager) : base(inspectionValuesService, mediator, viewManager)
    {
    }

    public override ValueListType ValueListType => ValueListType.Floors;

    public override string IndexUrl => Urls.Floors;
}
