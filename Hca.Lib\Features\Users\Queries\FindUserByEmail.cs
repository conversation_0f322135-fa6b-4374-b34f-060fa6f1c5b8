﻿using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Queries
{
    public class FindUserByEmail : IQuery<UserDto>
    {
        public FindUserByEmail(string email)
        {
            Email = email;
        }

        public string Email { get; }
    }

    public class FindUserByEmailHandler : DapperRequestHandler<FindUserByEmail, UserDto>
    {
        public FindUserByEmailHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<UserDto> OnHandleAsync(IDbHelper db, FindUserByEmail request)
        {
            var users = await db.GetListAsync<UserDto>("WHERE Email = @Email", request);
            return users.FirstOrDefault();
        }
    }
}
