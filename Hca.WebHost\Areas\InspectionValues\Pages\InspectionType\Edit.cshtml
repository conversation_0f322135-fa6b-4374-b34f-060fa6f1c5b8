﻿@page "/InspectionValues/InspectionType/{id:guid}"
@model Hca.WebHost.Areas.InspectionValues.Pages.InspectionType.Edit
@using Hca.Lib.Features.Templates
<div class="content-heading">
    <div>
        Inspection Type
    </div>
</div>

<div class="card card-default">
    <form method="post">
        <div class="card-body">
            <input asp-for="Value.DisplayText" row-label="Inspection Type Name" />
            <input type="text" asp-for="Value.HintText" row-label="Hint Text" />
            <select asp-for="DefaultTemplateId"
                    asp-items="@(new SelectList(Model.Templates, nameof(TemplateDto.Id), nameof(TemplateDto.TemplateName)))"
                    row-label="Default Report Template">
                <option selected="selected" value="">Please select...</option>
            </select>
        </div>
        <div class="card-footer">
            <input class="btn btn-success" type="submit" value="Save" />
        </div>
    </form>
</div>
