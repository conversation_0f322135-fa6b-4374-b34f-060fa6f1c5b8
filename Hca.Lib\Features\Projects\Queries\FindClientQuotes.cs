﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Queries
{
    public class FindClientQuotes : PagedQuery<QuoteDto>
    {
        public FindClientQuotes(
            Guid clientId)
        {
            ClientId = clientId;
        }

        public Guid ClientId { get; }
    }

    public class FindClientQuotesHandler : DapperRequestHandler<FindClientQuotes, PagedDtoSet<QuoteDto>>
    {
        public FindClientQuotesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<PagedDtoSet<QuoteDto>> OnHandleAsync(IDbHelper db, FindClientQuotes request)
        {
            var whereClause = string.IsNullOrWhiteSpace("") // request.SearchText
                ? ""
                : "AND Code LIKE '%' + @SearchText + '%' ";

            var sql = @"
SELECT *
FROM tblQuotes
WHERE ClientId=@ClientId " + where<PERSON>lause + @"
ORDER BY Created DESC
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblQuotes
WHERE ClientId=@ClientId " + whereClause;

            var (Items, Total) = await db.QueryPageAsync<QuoteDto>(
                sql,
                request.Page,
                request.PageSize,
                new
                {
                    request.ClientId,
                   // request.SearchText
                });

            return PagedDtoSet.From(Items, request.Page, request.PageSize, Total);
        }
    }
}
