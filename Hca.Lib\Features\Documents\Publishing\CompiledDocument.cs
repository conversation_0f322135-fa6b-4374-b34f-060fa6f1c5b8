﻿using System;
using System.IO;
using DocumentFormat.OpenXml.Packaging;
using OpenXmlPowerTools;

namespace Hca.Lib.Features.Documents.Publishing
{
    public sealed class CompiledDocument : IDisposable
    {
        public MemoryStream MemoryStream { get; private set; }

        public CompiledDocument(string path)
        {
            var bytes = File.ReadAllBytes(path);
            CreateMemoryStream(bytes);
        }

        public CompiledDocument(byte[] bytes)
        {
            CreateMemoryStream(bytes);
        }

        private MemoryStream CreateMemoryStream(byte[] bytes)
        {
            //Do not use byte array constructor as this is not resizable i.e. does not handle change.
            MemoryStream = new MemoryStream();
            MemoryStream.Write(bytes, 0, bytes.Length);
            return MemoryStream;
        }

        // these must be disposed after use to repopulate the memorystream
        public WordprocessingDocument AsWordProcessingDocument => WordprocessingDocument.Open(MemoryStream, true);

        public WmlDocument AsWmlDocument => new("dummy", MemoryStream.ToArray());

        public void Dispose()
        {
            MemoryStream.Dispose();
        }
    }
}
