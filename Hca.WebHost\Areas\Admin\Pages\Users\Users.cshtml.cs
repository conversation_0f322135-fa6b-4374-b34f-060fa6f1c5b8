﻿using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Users;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Identity.Pages;

public class UsersModel : HcaPageModel
{
    private readonly UserService _userService;

    public UsersModel(UserService userService, ViewManager viewManager, IMediator mediator) : base(mediator, viewManager)
    {
        _userService = userService;
    }

    public IEnumerable<UserDto> Users { get; private set; }

    public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        Users = await _userService.GetHcaUsersAsync(cancellationToken);
        return Page();
    }
}
