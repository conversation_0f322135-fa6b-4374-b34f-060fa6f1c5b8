﻿using Hca.Lib.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Hca.WebHost.Controllers;

[ApiController]
public class PropertiesController : Controller
{
    private readonly PropertyCountsService _propertyChildCountsService;

    public PropertiesController(PropertyCountsService propertyChildCountsService)
    {
        _propertyChildCountsService = propertyChildCountsService;
    }

    [HttpGet("api/{clientId}/properties/{propertyId}/child-counts")]
    public async Task<IActionResult> GetPropertyChildCounts(Guid clientId, Guid propertyId)
    {
        var counts = await _propertyChildCountsService.GetPropertyChildCountsAsync(clientId, propertyId);
        return Ok(counts);
    }
}
