﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries;
using MediatR;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class SetPropertyAddress : ICommand
    {
        public SetPropertyAddress(
            Guid propertyId,
            string unit,
            string street,
            string town,
            string city,
            string county,
            string country,
            string postcode,
            double? lat,
            double? lon)
        {
            PropertyId = propertyId;
            Unit = unit;
            Street = street;
            Town = town;
            City = city;
            County = county;
            Country = country;
            Postcode = postcode;
            Lat = lat;
            Lon = lon;
        }

        public Guid PropertyId { get; }

        public string Unit { get; }
        public string Street { get; }
        public string Town { get; }
        public string City { get; }
        public string County { get; }
        public string Country { get; }
        public string Postcode { get; }
        public double? Lat { get; }
        public double? Lon { get; }
    }

    public class SetPropertyAddressHandler : DapperRequestHandler<SetPropertyAddress, CommandResult>
    {
        private readonly IMediator _mediator;

        public SetPropertyAddressHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetPropertyAddress request)
        {
            var propertyDto = await _mediator.Send(new GetProperty(request.PropertyId));
            AddressDto addressDto;

            if (propertyDto.AddressId.HasValue)
            {
                addressDto = await db.GetAsync<AddressDto>(propertyDto.AddressId.Value);
            }
            else
            {
                addressDto = new AddressDto { Id = Guid.NewGuid() };
            }

            propertyDto.Unit = request.Unit;
            addressDto.StreetName = request.Street;
            addressDto.Town = request.Town;
            addressDto.City = request.City;
            addressDto.County = request.County;
            addressDto.Country = request.Country;
            addressDto.Postcode = request.Postcode;
            addressDto.Lat = request.Lat;
            addressDto.Lon = request.Lon;

            if (propertyDto.AddressId.HasValue)
            {
                await db.UpdateAsync(addressDto);
            }
            else
            {
                await db.InsertAsync(addressDto);
                propertyDto.AddressId = addressDto.Id;
            }

            await db.UpdateAsync(propertyDto);

            return CommandResult.Success();
        }
    }
}
