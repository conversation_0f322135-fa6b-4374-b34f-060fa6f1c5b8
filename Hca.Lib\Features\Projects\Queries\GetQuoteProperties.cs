﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Queries
{

    public class GetQuoteProperties : IQueryMany<QuotePropertyWithAddressDto>
    {
        public GetQuoteProperties(Guid quoteId)
        {
            QuoteId = quoteId;
        }

        public Guid QuoteId { get; }
    }

    public class GetQuotePropertiesHandler : DapperRequestHandler<GetQuoteProperties, DtoSet<QuotePropertyWithAddressDto>>
    {
        public GetQuotePropertiesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<QuotePropertyWithAddressDto>> OnHandleAsync(IDbHelper db, GetQuoteProperties request) =>
            DtoSet.From(await db.QueryAsync<QuotePropertyWithAddressDto>(
                "SELECT tblQuoteProperties.*, PropertyCode, StreetName, Town " +
                "FROM tblQuoteProperties " +
                "JOIN tblProperties ON PropertyId = tblProperties.Id " +
                "LEFT JOIN tblAddresses ON AddressId = tblAddresses.Id " +
                "WHERE QuoteId=@QuoteId",
                request));
    }
}
