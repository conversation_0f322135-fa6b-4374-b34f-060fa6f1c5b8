﻿using System.Collections.Generic;
using Hca.Lib.Features.Inspections;

namespace Hca.WebHost.Areas.InspectionValues.Models
{
    public class InspectionValuesModel
    {
        public IEnumerable<ValueDto> InspectionValues { get; set; }

        public Dictionary<string, string> ColumnNames { get; set; }

        public string Header { get; set; }

        public string SubHeader { get; set; }

        public string UrlPath { get; set; }
    }
}
