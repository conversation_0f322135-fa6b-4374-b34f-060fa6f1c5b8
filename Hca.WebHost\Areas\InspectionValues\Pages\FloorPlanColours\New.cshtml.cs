﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.FloorPlanColours
{
    public class NewModel : InspectionValueCreatePage
    {
        public NewModel(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override ValueListType ValueListType => ValueListType.FloorPlanColours;

        public override string IndexUrl => Urls.FloorPlanColours;
    }
}
