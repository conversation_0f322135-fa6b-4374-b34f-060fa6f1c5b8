﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Quantity;

public class Index : InspectionValueListPage
{
    public Index(
        ValueListsService inspectionValuesService,
        ViewManager viewManager,
        IMediator mediator) : base(inspectionValuesService, viewManager, mediator)
    {
    }

    public override   ValueListType  ValueListType =>   ValueListType.Quantity;
}
