﻿using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using MediatR;

namespace Hca.Lib.Azure.Blob;

public class OpenBlobStream : IRequest<(Stream Stream, string ContentType)>
{
    public OpenBlobStream(
        string containerName,
        string blobName)
    {
        ContainerName = containerName;
        BlobName = blobName;
    }

    public string ContainerName { get; }
    public string BlobName { get; }
}

public class OpenBlobStreamHandler : IRequestHandler<OpenBlobStream, (Stream Stream, string ContentType)>
{
    private readonly BlobServiceClient _client;
    public OpenBlobStreamHandler(BlobServiceClient blobClient) { _client = blobClient; }

    public async Task<(Stream Stream, string ContentType)> Handle(OpenBlobStream request, CancellationToken cancellationToken)
    {
        var containerClient = _client.GetBlobContainerClient(request.ContainerName);
        var blobClient = containerClient.GetBlobClient(request.BlobName);
        var properties = await blobClient.GetPropertiesAsync(cancellationToken: cancellationToken);
        var stream = await blobClient.OpenReadAsync(cancellationToken: cancellationToken);
        return (stream, properties.Value.ContentType);
    }
}
