﻿using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace Hca.Lib.Features.Documents.Fields
{
    public class TextField : Field
    {
        private static readonly string PLACEHOLDER = "placeholder";
        private static readonly string ACTUAL = "actual";
        private static readonly string IS_HTML = "ishtml";

        internal TextField(int fieldOrder, bool isOptional, string serialisedContent) :
            base(FieldType.Text, fieldOrder, isOptional, serialisedContent) { }

        //internal TextField(string hint = null, bool isOptional = false) : base(FieldType.Text, hint, isOptional) { }

        internal TextField(
            string placeholder,
            bool isHtml = false,
            string hint = null,
            bool isOptional = false) : base(FieldType.Text, isOptional, hint)
        {
            _fieldContent.Add(PLACEHOLDER, placeholder);
            _fieldContent.Add(IS_HTML, isHtml);
        }

        public string Actual
        {
            get => _fieldContent[ACTUAL]?.ToString();
            set
            {
                if (!_fieldContent.TryAdd(ACTUAL, JToken.FromObject(value ?? "")))
                {
                    _fieldContent.Remove(ACTUAL);
                    _fieldContent.Add(ACTUAL, JToken.FromObject(value ?? ""));
                }
            }
        }

        public string Placeholder
        {
            get => _fieldContent[PLACEHOLDER]?.ToString();
            set
            {
                if (!_fieldContent.TryAdd(PLACEHOLDER, JToken.FromObject(value ?? "")))
                {
                    _fieldContent.Remove(PLACEHOLDER);
                    _fieldContent.Add(PLACEHOLDER, JToken.FromObject(value ?? ""));
                }
            }
        }

        public bool IsHtml
        {
            get
            {
                return bool.TryParse(_fieldContent[IS_HTML]?.ToString(), out var isHtml) && isHtml;
            }
        }
    }
}
