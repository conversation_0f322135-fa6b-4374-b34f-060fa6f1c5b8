﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries;

public class GetClientCounts : IQuery<ClientCountsModel>
{
    public GetClientCounts(Guid clientId)
    {
        ClientId = clientId;
    }

    public Guid ClientId { get; }
}

public class GetClientCountsHandler : DapperRequestHandler<GetClientCounts, ClientCountsModel>
{
    public GetClientCountsHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override Task<ClientCountsModel> OnHandleAsync(IDbHelper db, GetClientCounts request)
    {
        var siteTask = db.CountAsync<SiteDto>($"WHERE {nameof(SiteDto.ClientId)} = @{nameof(GetClientCounts.ClientId)} AND {nameof(SiteDto.Archived)} IS NULL", request);
        var propertyTask = db.CountAsync<PropertyDto>($"WHERE {nameof(PropertyDto.ClientId)} = @{nameof(GetClientCounts.ClientId)} AND {nameof(PropertyDto.Archived)} IS NULL", request);
        var contactTask = db.CountAsync<ContactDto>($"WHERE {nameof(ContactDto.ClientId)} = @{nameof(GetClientCounts.ClientId)}", request);

        return Task.WhenAll(siteTask, propertyTask, contactTask).ContinueWith(_ =>
        {
            return new ClientCountsModel
            {
                SiteCount = siteTask.Result,
                PropertyCount = propertyTask.Result,
                ContactCount = contactTask.Result
            };
        });
    }
}
