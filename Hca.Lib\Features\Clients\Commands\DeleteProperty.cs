﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class DeleteProperty : ICommand
    {
        public DeleteProperty(Guid clientId, Guid propertyId, DateTime? deleted = null)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            Deleted = deleted ?? DateTime.UtcNow;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public DateTime? Deleted { get; }
    }

    public class DeletePropertyHandler : DapperRequestHandler<DeleteProperty, CommandResult>
    {
        private readonly SiteCountsService _siteCountsService;
        private readonly ClientCountsService _clientCountsService;

        public DeletePropertyHandler(IDbHelper dbHelper, SiteCountsService siteCountsService, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _siteCountsService = siteCountsService;
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteProperty request)
        {
            var property = await db.GetAsync<PropertyDto>(request.PropertyId);
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)}=@{nameof(request.Deleted)} " +
                $"WHERE {nameof(PropertyDto.Id)}=@{nameof(request.PropertyId)}", request);
            _clientCountsService.ClearClientCountsAsync(property.ClientId);
            if (property.SiteId.HasValue)
            {
                _siteCountsService.ClearSiteCountsAsync(property.SiteId.Value);
            }
            return CommandResult.Success();
        }
    }
}
