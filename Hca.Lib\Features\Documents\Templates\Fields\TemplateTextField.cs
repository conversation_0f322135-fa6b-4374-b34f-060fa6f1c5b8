﻿using System;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Documents.Templates.Fields
{
    public class TemplateTextField : TemplateField<TextField>
    {
        protected TemplateTextField(Guid templateFieldId, int fieldOrder, bool isOptional, string serialisedContent) : base(templateFieldId)
        {
            Value = new TextField(fieldOrder, isOptional, serialisedContent);
        }

        protected TemplateTextField(
            string placeholder,
            bool isHtml = false,
            string hint = null,
            bool isOptional = false) : base(Guid.NewGuid())
        {
            Value = new TextField(placeholder, isHtml, hint, isOptional);
        }

        public static TemplateTextField Rehydrate(Guid templateFieldId, int fieldOrder, bool isOptional, string serialisedContent) =>
            new(templateFieldId, fieldOrder, isOptional, serialisedContent);

        public static TemplateTextField Create(
            string placeholder,
            bool isHtml,
            string hint = null,
            bool isOptional = false) => new(placeholder, isHtml, hint, isOptional);

        public static TemplateTextField Create(
            string placeholder,
            string hint = null,
            bool isOptional = false) => new(placeholder, false, hint, isOptional);
    }
}