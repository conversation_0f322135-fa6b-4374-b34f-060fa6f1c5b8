﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace Hca.Lib.Data
{
    public interface IDbHelper
    {
        Task<IDbConnection> CreateConnectionAsync();
        Task<int> ExecuteAsync(string sql, object param = null);
        Task<T> ExecuteScalarAsync<T>(string sql, object param = null);
        Task<T> GetAsync<T>(Guid key);
        Task<T> GetAsync<T>(string key);
        Task<T> GetAsync<T>(string whereClause, object param = null);
        Task<IEnumerable<T>> GetListAsync<T>(string whereClause = null, object param = null);
        Task<int> CountAsync<T>(string whereClause = null, object param = null);
        Task<T> QuerySingleOrDefaultAsync<T>(string sql, object param = null);
        Task<IEnumerable<T>> QueryAsync<T>(string sql, object param = null);
        Task<int> DeleteAsync<T>(Guid id);
        Task InsertAsync<T>(T dto);
        Task UpdateAsync<T>(T dto);
        Task<(IEnumerable<T> Items, int Total)> QueryPageAsync<T>(string query, int page, int pageSize, object param = null);
        Task DeleteAsync<T>(string whereClause, object param = null);
        Task<IEnumerable<T>> QueryAsync<T, TChild>(string sql, Func<T, TChild, T> map, string splitOn = "Id", object param = null);
    }
}
