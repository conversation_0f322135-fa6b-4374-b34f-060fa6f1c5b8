﻿using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Commands;
using Hca.Lib.Features.Clients.Queries.Models;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Services.QRFileSpot;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Clients.Pages.Property;

[IgnoreAntiforgeryToken]
public class PropertyIndexModel : HcaPageModel
{
    private readonly ClientService _clientService;
    private readonly ValueListsService _valueListsService;

    public PropertyIndexModel(
        ClientService clientService,
        ViewManager viewManager,
        ValueListsService valueListsService,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
        _valueListsService = valueListsService;
    }

    // note that OnGetAddressAsync carries out access permission checks
    public Task OnGetAsync(Guid propertyId, CancellationToken cancellationToken) =>
        OnGetAddressAsync(propertyId, cancellationToken);

    [BindProperty]
    public AddressDto Address { get; set; }
    [BindProperty]
    public PropertyDtoExtended Property { get; set; }
    [BindProperty]
    public ClientDto Client { get; set; }

    public PropertyPageSection Section { get; private set; }

    [BindProperty]
    public IFormFile UploadFile { get; set; }

    [BindProperty]
    public DocumentModel Document { get; set; }
    public IEnumerable<PropertyDocumentDto> Table1Documents { get; private set; }
    public IEnumerable<PropertyDocumentDto> Table2Documents { get; private set; }

    [BindProperty]
    public FloorPlanModel FloorPlan { get; set; }
    public IEnumerable<FloorPlanDto> FloorPlans { get; private set; }
    public IEnumerable<ValueDto> Floors { get; private set; }
    public IEnumerable<SiteDtoExtended> Sites { get; private set; }

    public PropertyCountsModel Counts { get; private set; }
    public SiteDtoExtended Site { get; private set; }

    #region Address
    public async Task<IActionResult> OnGetAddressAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Section = PropertyPageSection.AddressHome;

        await PopulateModel(propertyId, cancellationToken);

        Sites = (await _clientService.GetSitesAsync(Client.Id, null, cancellationToken)).Items;

        return Page();
    }

    public async Task<IActionResult> OnPostAddressAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        await _clientService.UpdatePropertyAsync(Client.Id, Property, Address, cancellationToken);

        return await OnGetAddressAsync(propertyId, cancellationToken);
    }
    #endregion

    #region Photo
    public async Task<IActionResult> OnGetAddPhotoAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Section = PropertyPageSection.AddPhoto;

        await PopulateModel(propertyId, cancellationToken);

        return Page();
    }

    public async Task<string> GetPhotoUrl(PropertyDtoExtended photo) => await this.GetBlobUrl(photo.ImageContainerName, photo.ImageBlobName);

    public async Task<IActionResult> OnPostAddPhotoAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Section = PropertyPageSection.AddPhoto;

        if (UploadFile != null)
        {
            using var ms = new MemoryStream();
            UploadFile.CopyTo(ms);

            var property = await _clientService.GetPropertyAsync(propertyId, cancellationToken);
            await _clientService.UploadPropertyImageAsync(
                property.ClientId,
                propertyId,
                ms.ToArray(),
                UploadFile.ContentType,
                UploadFile.FileName,
                cancellationToken);
        }

        return Redirect(Urls.ClientProperty(Client.Id, propertyId));
    }
    #endregion

    #region Documents
    public async Task<string> GetDocumentUrl(PropertyDocumentDto photo) => await this.GetBlobUrl(photo.ContainerName, photo.BlobName);

    public async Task<IActionResult> OnGetDocumentsAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Section = PropertyPageSection.Documents;

        await PopulateModel(propertyId, cancellationToken);
        
        var allDocuments = await _clientService.GetPropertyDocumentsAsync(Property.ClientId, propertyId, cancellationToken);

        Table1Documents = allDocuments.Where(d => (int)d.PropertyDocumentType < 200 && d.PropertyDocumentType != PropertyDocumentType.Unspecified);
        Table2Documents = allDocuments.Where(d => (int)d.PropertyDocumentType > 200  || d.PropertyDocumentType == PropertyDocumentType.Unspecified);

        return Page();
    }

    public async Task<IActionResult> OnGetUploadDocumentAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        Section = PropertyPageSection.UploadDocument;
        Document = new();
        await PopulateModel(propertyId, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnPostUploadDocumentAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        Section = PropertyPageSection.UploadDocument;

        if (!ModelState.IsValid && UploadFile == null)
        {
            ModelState.AddModelError("", "Must select a file for upload");

            if (propertyId == Guid.Empty && Request.RouteValues.ContainsKey("propertyId"))
            {
                propertyId = Guid.Parse(Request.RouteValues["propertyId"].ToString());
            }

            await PopulateModel(propertyId, cancellationToken);

            return Page();
        }

        var property = await _clientService.GetPropertyAsync(propertyId, cancellationToken);
        await _clientService.UploadPropertyDocumentAsync(
            property.ClientId,
            propertyId,
            UploadFile.OpenReadStream(),
            UploadFile.ContentType,
            UploadFile.FileName,
            Document.DocumentDate,
            Document.PropertyDocumentType,
            Document.CompanyName,
            Document.Notes,
            Document.NextInspectionDate,
            cancellationToken);

        return Redirect(Urls.ClientPropertyDocuments(Client.Id, propertyId));
    }

    public async Task<IActionResult> OnGetEditDocumentAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        if (!Guid.TryParse(Request.Query["documentId"], out var documentId))
        {
            return Redirect(Urls.ClientPropertyDocuments(clientId, propertyId));
        }

        Section = PropertyPageSection.DocumentEdit;

        await PopulateModel(propertyId, cancellationToken);

        var document = await _clientService.GetPropertyDocumentAsync(clientId, propertyId, documentId, cancellationToken);

        Document = new DocumentModel
        {
            Id = document.Id,
            CompanyName = document.CompanyName,
            DocumentDate = document.DocumentDate,
            PropertyDocumentType = document.PropertyDocumentType,
            Notes = document.Notes,
            NextInspectionDate = document.NextInspection,
            QrImageUrl = QrFileSpotConfig.GetDocumentQrCodeImageUrl(document.QrCodeId),
            QrDocumentUrl = QrFileSpotConfig.GetDocumentQrCodeDocumentUrl(document.QrCodeId),
        };

        return Page();
    }

    public async Task<IActionResult> OnPostEditDocumentAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        if (!Guid.TryParse(Request.Query["documentId"], out var _))
        {
            return Redirect(Urls.ClientPropertyDocuments(clientId, propertyId));
        }

        await _clientService.EditPropertyDocumentAsync(
            Document.Id,
            Document.PropertyDocumentType,
            Document.DocumentDate,
            Document.CompanyName,
            Document.Notes,
            Document.NextInspectionDate,
            cancellationToken);

        return Redirect(Urls.ClientPropertyDocuments(clientId, propertyId));
    }

    public async Task<IActionResult> OnPostCreateQrCodeAsync(Guid clientId, Guid propertyId, CancellationToken ct)
    {
        if (!await IsAdminUser) return Forbid();

        if (!Guid.TryParse(Request.Query["documentId"], out var documentId))
        {
            return Redirect(Urls.ClientPropertyDocuments(clientId, propertyId));
        }

        await _mediator.Send(new CreatePropertyDocumentQrCode(clientId, propertyId, documentId, "", "document"), ct);

        var document = await _clientService.GetPropertyDocumentAsync(clientId, propertyId, documentId, ct);

        return Partial("_QrCodePostResult", QrFileSpotConfig.GetDocumentQrCodeImageUrl(document.QrCodeId));
    }

    #endregion

    #region Plans
    public Task<string> GetFloorPlanUrl(FloorPlanDto floorPlan) => this.GetBlobUrl(floorPlan.ContainerName, floorPlan.BlobName);
    public Task<string> GetFloorPlanThumbnailUrl(FloorPlanDto floorPlan) => this.GetBlobUrl(floorPlan.ContainerName, floorPlan.ThumbnailName);

    public async Task<IActionResult> OnGetFloorPlansAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Section = PropertyPageSection.FloorPlans;

        await PopulateModel(propertyId, cancellationToken);
        FloorPlans = await _clientService.GetFloorPlansAsync(Property.ClientId, propertyId, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnGetUploadFloorPlanAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        Section = PropertyPageSection.UploadFloorPlan;

        await PopulateModel(propertyId, cancellationToken);
        Floors = await _valueListsService.GetAllAsync(ValueListType.Floors, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnGetEditFloorPlanAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        if (!Guid.TryParse(Request.Query["floorPlanId"], out var floorPlanId))
        {
            return Redirect(Urls.FloorPlans(clientId, propertyId));
        }

        Section = PropertyPageSection.FloorPlanEdit;

        await PopulateModel(propertyId, cancellationToken);
        Floors = await _valueListsService.GetAllAsync(ValueListType.Floors, cancellationToken);

        var floorPlan = (await _clientService.GetFloorPlansAsync(clientId, propertyId, cancellationToken))
            .SingleOrDefault(d => d.Id == floorPlanId);

        FloorPlan = new FloorPlanModel
        {
            Id = floorPlan.Id,
            Floor = floorPlan.FloorId.Value,
            Notes = floorPlan.Notes,
        };

        return Page();
    }

    public async Task<IActionResult> OnPostEditFloorPlanAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        if (!Guid.TryParse(Request.Query["floorPlanId"], out var _))
        {
            return Redirect(Urls.FloorPlans(clientId, propertyId));
        }

        await _clientService.EditFloorPlanAsync(
            FloorPlan.Id,
            FloorPlan.Floor,
            FloorPlan.Notes,
            cancellationToken);

        return Redirect(Urls.FloorPlans(clientId, propertyId));
    }

    public async Task<IActionResult> OnPostUploadFloorPlanAsync(Guid propertyId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
            if (!valid.IsValid)
                return Forbid();
        }

        Section = PropertyPageSection.UploadFloorPlan;

        if ((!ModelState.IsValid && 
                (ModelState["FloorPlan.Notes"].ValidationState == ModelValidationState.Invalid || 
                ModelState["FloorPlan.Floor"].ValidationState == ModelValidationState.Invalid)) 
            || UploadFile == null)
        {
            ModelState.AddModelError("", "Must select a file for upload");
            await PopulateModel(propertyId, cancellationToken);
            Floors = await _valueListsService.GetAllAsync(ValueListType.Floors, cancellationToken);

            return Page();
        }

        var property = await _clientService.GetPropertyAsync(propertyId, cancellationToken);
        await _clientService.UploadFloorPlanAsync(
            property.ClientId,
            propertyId,
            UploadFile.OpenReadStream(),
            UploadFile.ContentType,
            UploadFile.FileName,
            FloorPlan.Floor,
            FloorPlan.Notes,
            cancellationToken);

        return Redirect(Urls.FloorPlans(Client.Id, propertyId));
    }
    #endregion

    private async Task PopulateModel(Guid propertyId, CancellationToken cancellationToken)
    {
        Property = await _clientService.GetPropertyAsync(propertyId, cancellationToken);
        Client = await _clientService.GetClientAsync(Property.ClientId, cancellationToken);
        
        if (Property.SiteId.HasValue)
        {
            Site = await _clientService.GetSiteAsync(Client.Id, Property.SiteId.Value, cancellationToken);
            Address = await _clientService.GetAddressAsync(Site.AddressId.Value, cancellationToken);
        }
        else
        {
            if (Property.AddressId.HasValue)
            {
                Address = await _clientService.GetAddressAsync(Property.AddressId.Value, cancellationToken);
            }
            else
            {
                Address = new AddressDto();
            }
        }
    }
}

public enum PropertyPageSection { AddressHome, AddPhoto, UploadDocument, Documents, DocumentEdit, UploadFloorPlan, FloorPlans, FloorPlanEdit }

public class DocumentModel
{
    [Required(ErrorMessage = "Must select a document date")]
    [DataType(DataType.Date)]
    [DisplayFormat(ApplyFormatInEditMode = true, DataFormatString = "{0:dd/MM/yyyy}")]
    public DateTime DocumentDate { get; set; } = DateTime.UtcNow;

    [DataType(DataType.Date)]
    public DateTime? NextInspectionDate { get; set; }

    [Required(ErrorMessage = "Must select a company name")]
    public string CompanyName { get; set; }

    [Range(1, int.MaxValue, ErrorMessage = "Must choose a valid document type")]
    public PropertyDocumentType PropertyDocumentType { get; set; }

    public string Notes { get; set; }

    public Guid Id { get; set; }

    public string QrImageUrl { get; internal set; }

    public string QrDocumentUrl { get; internal set; }
}

public class FloorPlanModel
{
    [Required(ErrorMessage = "Must select a floor")]
    public Guid Floor { get; set; }

    public string Notes { get; set; }

    public Guid Id { get; set; }
}
