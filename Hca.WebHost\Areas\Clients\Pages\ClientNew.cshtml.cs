﻿using System;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientNewModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientNewModel(
        ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { 
        _clientService = clientService; 
    }

    [Required]
    [BindProperty]
    public ClientDto Client { get; set; }
    [Required]
    [BindProperty]
    public AddressDto Address { get; set; }
    [BindProperty]
    public IFormFile Logo { get; set; }

    public void OnGet()
    {
        Address = new AddressDto { Id = Guid.NewGuid() };
        Client = new ClientDto { Id = Guid.NewGuid(), AddressId = Address.Id };
    }

    public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser)
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        await CreateClient(cancellationToken);
        return Redirect($"{Urls.Clients}/{Client.Id}");
    }

    public async Task<IActionResult> OnPostAnotherAsync(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser)
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        await CreateClient(cancellationToken);
        return Redirect(Urls.ClientsNew.AddNewMode());
    }

    private async Task<IActionResult> CreateClient(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        await _clientService.AddClientAsync(Client, Address, cancellationToken);

        if (Logo != null)
        {
            using var ms = new MemoryStream();
            Logo.CopyTo(ms);
            Client.LogoUrl = (await _clientService.UpdateClientLogoAsync(Client.Id, Logo.FileName, ms.ToArray(), cancellationToken)).ToString();
        }

        return Page();
    }
}

[JsonObject]
public class AddClientModel
{
    [JsonProperty(Required = Required.Always, PropertyName = "clientName")]
    public string ClientName { get; set; }
}
