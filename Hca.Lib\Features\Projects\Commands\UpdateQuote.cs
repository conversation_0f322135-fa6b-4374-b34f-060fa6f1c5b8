﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Projects.Commands
{
    public class UpdateQuote : ICommand
    {
        public UpdateQuote(Guid quoteId, Guid? contactId, Guid inspectionTypeId, string notes, double baseFee)
        {
            QuoteId = quoteId;
            ContactId = contactId;
            Notes = notes;
            InspectionTypeId = inspectionTypeId;
            BaseFee = baseFee;
        }

        public Guid QuoteId { get; }
        public Guid? ContactId { get; }
        public string Notes { get; }
        public Guid InspectionTypeId { get; }
        public double BaseFee { get; }
    }

    public class UpdateQuoteHandler : <PERSON>pperRequestHandler<UpdateQuote, CommandResult>
    {
        public UpdateQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateQuote request)
        {
            var dto = await db.GetAsync<QuoteDto>(request.QuoteId);
            dto.Notes = request.Notes;
            dto.InspectionTypeId = request.InspectionTypeId;
            dto.BaseFee = request.BaseFee;
            dto.ContactId = request.ContactId;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
