﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Features.Users;
using Hca.Lib.Features.Users.Commands;
using Hca.Lib.Features.Users.Queries;
using Hca.Lib.Identity.Services;
using MediatR;

namespace Hca.WebHost.Services
{
    public class UserService
    {
        private readonly NewUserService _newUserService;
        private readonly IMediator _mediator;
        public UserService(
            IMediator mediator,
            NewUserService newUserService)
        {
            _newUserService = newUserService;
            _mediator = mediator;
        }

        public async Task<IEnumerable<UserDto>> GetHcaUsersAsync(CancellationToken cancellationToken)
        {
            return (await _mediator.Send(new FindUsersByRole(UserRole.Hca, UserRole.Admin), cancellationToken)).Items;
        }

        public async Task SaveUserAsync(UserDto user, CancellationToken cancellationToken)
        {
            var dto = await _mediator.Send(new GetUser(user.Id), cancellationToken);

            // new user
            if (dto == null)
            {
                var newUser = await _newUserService.Create(user.Email, user.Password, UserRole.Hca, cancellationToken);
                dto = new UserDto
                {
                    Id = newUser.UserId,
                    Password = newUser.PasswordHash,
                    Email = newUser.Email,
                    Role = newUser.Role,
                };
            }

            // keep the password if a new one not provided
            if (!string.IsNullOrWhiteSpace(user.Password))
            {
                dto.Password = user.Password;
            }

            dto.FirstName = user.FirstName;
            dto.LastName = user.LastName;
            dto.Email = user.Email;
            dto.Position = user.Position;
            dto.OfficePhone = user.OfficePhone;
            dto.MobilePhone = user.MobilePhone;
            dto.Permissions = user.Permissions;
            dto.Role = user.Role;

            await _mediator.Send(new EditUser(dto), cancellationToken);
        }

        public Task DeleteUserAsync(Guid userId, CancellationToken cancellationToken) =>
            _mediator.Send(new DeleteUser(userId), cancellationToken);

        public Task UploadSignatureImageFile(Guid userId, byte[] file, string contentType, string filename) =>
            _mediator.Send(new SetUserSignature(userId, file, filename, contentType));

        public Task<string> GetSignatureUrlAsync(Guid userId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetUserSignature(userId), cancellationToken);

        public Task UploadProfileImageFile(Guid userId, byte[] file, string contentType, string filename) =>
            _mediator.Send(new SetUserProfileImage(userId, file, contentType, filename));

        public Task<string> GetProfileImageUrlAsync(Guid userId, CancellationToken cancellationToken) =>
            _mediator.Send(new GetUserProfileImage(userId), cancellationToken);
    }
}
