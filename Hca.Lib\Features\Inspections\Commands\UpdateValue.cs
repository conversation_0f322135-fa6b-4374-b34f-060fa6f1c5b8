﻿using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class UpdateValue : ICommand
    {
        public UpdateValue(ValueDto dto) 
        {
            Dto = dto;
        }

        public ValueDto Dto { get; }
    }

    public class UpdateValueHandler : DapperRequestHandler<UpdateValue, CommandResult>
    {
        public UpdateValueHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateValue request)
        {
            await db.UpdateAsync(request.Dto);
            return CommandResult.Success();
        }
    }
}
