﻿using DocumentFormat.OpenXml;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class DocumentExtensions
    {
        public static CompiledDocument ConvertTemplateToDocument(this CompiledDocument doc)
        {
            using var wordDoc = doc.AsWordProcessingDocument;
            if (wordDoc.DocumentType is not WordprocessingDocumentType.Template
                and not WordprocessingDocumentType.MacroEnabledTemplate)
            {
                return doc;
                //throw new ApplicationException("Document is not a template and cannot be converted");
                // or maybe just return, will see...
            }

            wordDoc.ChangeDocumentType(WordprocessingDocumentType.Document);

            //Create an AttachedTemplate object
            //var attachedTemplate1 = new AttachedTemplate() { Id = templateId.ToString() };

            ////Append the AttachedTemplateobject to the DocumentSettingsPart.Settings.
            //var mainPart = wordDoc.MainDocumentPart;
            //var documentSettingsPart1 = mainPart.DocumentSettingsPart;
            //documentSettingsPart1.Settings.Append(attachedTemplate1);

            ////Add an External Relationship of type AttachedTemplate to the DocumentSettingsPart.Settings.
            //documentSettingsPart1.AddExternalRelationship(
            //    "http://schemas.openxmlformats.org/officeDocument/2006/relationships/attachedTemplate",
            //    new Uri("./template.dotx", UriKind.Absolute),
            //    templateId.ToString());

            //Save the document.
            //mainPart.Document.Save();

            return doc;
        }
    }
}
