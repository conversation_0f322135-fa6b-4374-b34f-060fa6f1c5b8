﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Models;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Admin.Pages;

public class DeletedClientsModel : HcaPageModel
{
    public DeletedClientsModel(IMediator mediator, ViewManager viewManager) : base(mediator, viewManager)
    {
    }

    public PagedDtoSet<DeletedClientModel> Clients { get; private set; }

    public async Task<IActionResult> OnGetAsync([FromQuery] int? pageNum = 1, CancellationToken cancellationToken = default)
    {
        if (!await IsAdminUser) return Forbid();

        Clients = await _mediator.Send(new GetDeletedClientsPagedQuery(pageNum, 50), cancellationToken);

        return Page();
    }
}
