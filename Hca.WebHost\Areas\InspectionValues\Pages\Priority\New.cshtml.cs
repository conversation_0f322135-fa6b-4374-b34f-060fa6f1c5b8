﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Priority
{
    public class New : InspectionValueCreatePage
    {
        public New(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override   ValueListType  ValueListType =>   ValueListType.Priority;

        public override string IndexUrl => Urls.Priority;
    }
}
