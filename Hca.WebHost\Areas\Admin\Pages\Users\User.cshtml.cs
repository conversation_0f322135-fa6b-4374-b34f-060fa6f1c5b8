﻿using System;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Users;
using Hca.Lib.Identity;
using Hca.WebHost.Identity;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Identity.Pages;

public class UserModel : HcaPageModel
{
    private readonly IPasswordHasher<HcaUser> _passwordHasher;
    private readonly UserService _userService;

    public UserModel(
        UserService userService, 
        IPasswordHasher<HcaUser> passwordHasher, 
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _userService = userService;
        _passwordHasher = passwordHasher;
    }

    [BindProperty]
    public UserDto UserDto { get; set; }
    [BindProperty]
    public bool Admin { get; set; }
    [BindProperty]
    public IFormFile Signature { get; set; }
    [BindProperty]
    public IFormFile ProfileImage { get; set; }
    [BindProperty]
    public PermissionsModel UserPermissions { get; set; }

    public bool NewUser { get; set; }

    public string SignatureUrl { get; private set; }
    public string ProfileImageUrl { get; private set; }

    public async Task<IActionResult> OnGetAsync(string id, CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        if (Guid.TryParse(id, out var userId))
        {
            UserDto = (await _userService.GetHcaUsersAsync(cancellationToken)).Single(u => u.Id == userId);
            Admin = UserDto.Role == UserRole.Admin;
            UserPermissions = new PermissionsModel
            {
                CanDelete = UserDto.Permissions?.Contains(Permissions.CANDELETE) ?? false,
            };
            NewUser = false;

            if (!string.IsNullOrWhiteSpace(UserDto.SignatureFileName))
            {
                SignatureUrl = await _userService.GetSignatureUrlAsync(userId, cancellationToken);
            }

            if (!string.IsNullOrWhiteSpace(UserDto.ProfileImageFileName))
            {
                ProfileImageUrl = await _userService.GetProfileImageUrlAsync(userId, cancellationToken);
            }
        }
        else
        {
            UserDto = new UserDto { Id = Guid.NewGuid(), Role = UserRole.Hca };
            UserPermissions = new PermissionsModel();
            NewUser = true;
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(string id, CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        if (UserDto.Password.IsPopulated()) UserDto.Password = _passwordHasher.HashPassword(null, UserDto.Password);

        UserDto.Role = Admin ? UserRole.Admin : UserRole.Hca;
        if (UserPermissions.CanDelete) UserDto.Permissions = Permissions.CANDELETE;
        await _userService.SaveUserAsync(UserDto, cancellationToken);

        if (Signature != null)
        {
            using var ms = new MemoryStream();
            Signature.CopyTo(ms);
            await _userService.UploadSignatureImageFile(UserDto.Id, ms.ToArray(), Signature.ContentType, Signature.FileName);
        }

        if (ProfileImage != null)
        {
            using var ms = new MemoryStream();
            ProfileImage.CopyTo(ms);
            await _userService.UploadProfileImageFile(UserDto.Id, ms.ToArray(), ProfileImage.ContentType, ProfileImage.FileName);
        }

        return Redirect(Urls.Users);
    }

    public async Task<IActionResult> OnPostDeleteAsync(string id, CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        if (!Guid.TryParse(id, out var userId))
        {
            throw new ApplicationException("Not a valid user ID");
        }

        await _userService.DeleteUserAsync(userId, cancellationToken);
        return Redirect(Urls.Users);
    }

    public class PermissionsModel
    {
        public bool CanDelete { get; set; }
    }
}
