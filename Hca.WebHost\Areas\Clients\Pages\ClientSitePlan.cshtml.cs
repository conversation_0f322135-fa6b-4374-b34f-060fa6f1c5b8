﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSitePlanModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSitePlanModel(
        ClientService clientService,
        IMediator mediator,
        ViewManager viewManager) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    [BindProperty]
    public SitePlanDto Plan { get; set; }
    public SiteDtoExtended Site { get; private set; }
    public ClientDto Client { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, Guid siteId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);

        if (Guid.TryParse(id, out var planId))
        {
            Plan = (await _clientService.GetSitePlansAsync(clientId, siteId, cancellationToken))
                .Items
                .Single(p => p.Id == planId);
        }
        else
        {
            Plan = new SitePlanDto();
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, Guid siteId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (!Guid.TryParse(id, out var planId))
        {
            return Redirect(Urls.ClientSitePlans(clientId, siteId));
        }

        await _clientService.EditSitePlanAsync(
            planId,
            Plan.Notes,
            cancellationToken);

        return Redirect(Urls.ClientSitePlans(clientId, siteId));
    }
}
