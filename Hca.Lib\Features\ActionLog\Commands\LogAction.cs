﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.ActionLog.Commands;

public class LogAction : ICommand
{
    public LogAction(string actionName, Guid? userId, Guid? contactId)
    {
        UserId = userId;
        ContactId = contactId;
        ActionName = actionName;
    }

    public Guid? UserId { get; }
    public Guid? ContactId { get; }
    public string ActionName { get; }
}

public class LogActionHandler : DapperRequestHandler<LogAction, CommandResult>
{
    public LogActionHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<CommandResult> OnHandleAsync(IDbHelper db, LogAction request)
    {
        var sql = @"
    INSERT INTO [dbo].[tblActionLog] ([UserId], [ContactId], [Timestamp], [ActionName])
    VALUES (@UserId, @ContactId, @Timestamp, @ActionName)";

        await db.ExecuteAsync(sql, new
        {
            UserId = request.UserId,
            ContactId = request.ContactId,
            Timestamp = DateTime.UtcNow,
            ActionName = request.ActionName
        });

        return CommandResult.Success();
    }
}