﻿using System;
using System.Security.Policy;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSiteArchiveModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSiteArchiveModel(
        ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnPostAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!Guid.TryParse(id, out var siteId)) return BadRequest();

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        var archiveReason = Request.Form[Constants.ArchiveReasonFormName];

        var deleteResult = await _clientService.ArchiveSiteAsync(clientId, siteId, archiveReason, cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.ClientSites(clientId));
        }

        ErrorMessage = deleteResult.Reason;
        return Page();
    }
}
