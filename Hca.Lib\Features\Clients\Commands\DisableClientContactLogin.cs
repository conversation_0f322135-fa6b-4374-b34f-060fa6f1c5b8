﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class DisableClientContactLogin : ICommand
    {
        public DisableClientContactLogin(Guid contactId)
        {
            ContactId = contactId;
        }

        public Guid ContactId { get; }
    }

    public class DisableClientContactLoginHandler : DapperRequestHandler<DisableClientContactLogin, CommandResult>
    {
        public DisableClientContactLoginHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbH<PERSON>per db, DisableClientContactLogin request)
        {
            var contact = await db.GetAsync<ContactDto>(request.ContactId);
            contact.Password = null;
            await db.UpdateAsync(contact);
            return CommandResult.Success();
        }
    }
}
