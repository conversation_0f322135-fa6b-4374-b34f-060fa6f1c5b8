﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientByIdQuery : IQuery<ClientDto>
    {
        public GetClientByIdQuery(Guid clientId)
        {
            ClientId = clientId;
        }

        public Guid ClientId { get; }
    }

    public class GetClientByIdQueryHandler : DapperRequestHandler<GetClientByIdQuery, ClientDto>
    {
        public GetClientByIdQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<ClientDto> OnHandleAsync(IDb<PERSON><PERSON>per db, GetClientByIdQuery request)
        {
            return await db.GetAsync<ClientDto>(request.ClientId);
        }
    }
}
