﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Features.Config.Commands;
using MediatR;

namespace Hca.Lib.Features.Projects.Documents.Commands
{
    public class SetQuoteTemplate : ICommand
    {
        public SetQuoteTemplate(Guid templateId)
        {
            TemplateId = templateId;
        }

        public Guid TemplateId { get; }
    }

    public class SetQuoteTemplateHandler : IRequestHandler<SetQuoteTemplate, CommandResult>
    {
        private readonly IMediator _mediator;

        public SetQuoteTemplateHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<CommandResult> Handle(SetQuoteTemplate request, CancellationToken cancellationToken)
        {
            await _mediator.Send(new SetConfig("QuoteTemplate", null, request.TemplateId.ToString()), cancellationToken);
            return CommandResult.Success();
        }
    }
}
