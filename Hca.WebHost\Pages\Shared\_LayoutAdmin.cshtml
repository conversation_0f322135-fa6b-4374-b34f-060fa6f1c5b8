﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{ Layout = "_LayoutSidebar"; }

@section scripts {
    @RenderSection("scripts", required: false)
}

@section Styles {
    @RenderSection("Styles", required: false)
}

@section BodyArea {
    @RenderSection("BodyArea", required: false)
}

@if (await ViewManager.IsAdminUser)
{
    @section Sidebar {
        <partial name="_SidebarAdmin" />
    }

    <div>@RenderBody()</div>
}