﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.ActionLog.Commands;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Users;
using Hca.Lib.Identity;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;

namespace Hca.WebHost.Areas.Identity.Pages.Account
{
    [AllowAnonymous]
    public class LoginModel : PageModel
    {
        private readonly SignInManager<HcaUser> _signInManager;
        private readonly ILogger<LoginModel> _logger;
        private readonly ViewManager _viewManager;
        private readonly UserManager<HcaUser> _userManager;
        private readonly IMediator _mediator;

        public LoginModel(SignInManager<HcaUser> signInManager,
            ILogger<LoginModel> logger,
            ViewManager viewManager,
            UserManager<HcaUser> userManager,
            IMediator mediator)
        {
            _signInManager = signInManager;
            _logger = logger;
            _viewManager = viewManager;
            _userManager = userManager;
            _mediator = mediator;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public IList<AuthenticationScheme> ExternalLogins { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class InputModel
        {
            [Required]
            [EmailAddress]
            public string Email { get; set; }

            [Required]
            [DataType(DataType.Password)]
            public string Password { get; set; }

            [Display(Name = "Remember me?")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(CancellationToken cancellationToken = default)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ExternalLogins = (await _signInManager.GetExternalAuthenticationSchemesAsync()).ToList();
        }

        public async Task<IActionResult> OnPostAsync(string returnUrl = null, CancellationToken cancellationToken = default)
        {
            returnUrl ??= "/";

            if (ModelState.IsValid)
            {
                // This doesn't count login failures towards account lockout
                // To enable password failures to trigger account lockout, set lockoutOnFailure: true
                var result = await _signInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User logged in.");

                    var user = await _userManager.FindByNameAsync(Input.Email);
                    var claimsPrincipal = await _signInManager.CreateUserPrincipalAsync(user);
                    var clientIdClaim = claimsPrincipal.Claims.SingleOrDefault(c => c.Type == HcaClaimTypes.CLIENTID);

                    if (clientIdClaim != null)
                    {
                        var clientId = Guid.Parse(clientIdClaim.Value);
                        var client = await _mediator.Send(new GetClientByIdQuery(clientId), cancellationToken);
                        returnUrl = Path.Join($"/clients/{client.UrlSafeClientName}", returnUrl);
                    }

                    var actionLogRequest = new LogAction(
                        "Login", 
                        user.Role == UserRole.Hca || user.Role == UserRole.Admin ? user.UserId : null, 
                        user.Role == UserRole.Client ? user.UserId : null);
                    await _mediator.Send(actionLogRequest, cancellationToken);

                    return LocalRedirect(returnUrl);
                }
                if (result.RequiresTwoFactor)
                {
                    return RedirectToPage("./LoginWith2fa", new { ReturnUrl = returnUrl, Input.RememberMe });
                }
                if (result.IsLockedOut)
                {
                    _logger.LogWarning("User account locked out.");
                    return RedirectToPage("./Lockout");
                }
                else
                {
                    ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                    return Page();
                }
            }

            // If we got this far, something failed, redisplay form
            return Page();
        }
    }
}
