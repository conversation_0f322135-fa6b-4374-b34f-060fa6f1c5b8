{"Logging": {"LogLevel": {"Default": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"DbConnectionString": "Server=tcp:localhost,1433;Initial Catalog=HCA;Persist Security Info=False;User ID=sa;Password=********;MultipleActiveResultSets=False;Encrypt=False;TrustServerCertificate=False;Connection Timeout=30;"}, "AzureDefaults": {"Retry": {"maxTries": 3}}, "KeyVault": {"VaultUri": "https://hca-production.vault.azure.net/", "DataProtectionPath": "keys/dataprotection-dev"}, "BlobStorage": {"ServiceUri": "https://hcastore.blob.core.windows.net/", "DataProtectionPath": "dataprotection/hca_dev.xml"}, "QrFileSpot": {"ServiceUri": "https://api.qrfilespot.com", "ClientId": "c0125f94-70e4-4e4f-bce4-f8265199f3ee", "ClientSecret": "DinKohGP9Fg5fz472yglGbOknfhixQL4H3xmT8457Yk="}}