﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Hca.Lib.Identity;
using Hca.Lib.Services;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Hca.WebHost.Areas.Identity.Pages.Account
{
    public class ForgotPasswordModel : PageModel
    {
        private readonly UserManager<HcaUser> _userManager;
        private readonly IMediator _mediator;

        public ForgotPasswordModel(UserManager<HcaUser> userManager, IMediator mediator)
        {
            _userManager = userManager;
            _mediator = mediator;
        }

        [BindProperty]
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
                return Page();

            var user = await _userManager.FindByEmailAsync(Email);

            if (user == null) return Redirect(Urls.ForgotPasswordConfirmation);

            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            var callback = $"{Request.Scheme}://{Request.Host}{Urls.ResetPassword(Email, token)}";

            await _mediator.Send(new SendEmail(
                user.Email,
                "Spotlite Compliance password reset request",
                $"Hi {user.UserName}<br /><br />A password reset has been requested for your account.  " +
                $"If you did not request this then please report <NAME_EMAIL>.<br /><br />" +
                $"To reset your password please complete the new password form by visiting " +
                $"{callback}"));

            return Redirect(Urls.ForgotPasswordConfirmation);
        }
    }
}
