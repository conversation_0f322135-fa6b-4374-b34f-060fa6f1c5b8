﻿using Hca.WebHost.Pipeline;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("anchor")]
    public class Anchor : TagHelper
    {
        private readonly ViewManager _viewManager;

        public Anchor(ViewManager viewManager) : base()
        {
            _viewManager = viewManager;
        }

        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("text")]
        public string Text { get; set; }

        [HtmlAttributeName("style")]
        public string Style { get; set; } = "width: 100%; margin: 5px 0;";

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "a";
            output.TagMode = TagMode.StartTagAndEndTag;
            output.Attributes.Add("href", Action);
            output.Attributes.Add("class", "btn btn-info");
            output.Attributes.Add("style", Style);

            output.Content.SetContent(Text ?? "link");
        }
    }
}
