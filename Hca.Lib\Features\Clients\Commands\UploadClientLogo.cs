﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using MediatR;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UploadClientLogo : IRequest<Uri>
    {
        public UploadClientLogo(
            Guid clientId,
            string filename,
            byte[] imageData)
        {
            ClientId = clientId;
            Filename = filename;
            ImageData = imageData;
        }

        public Guid ClientId { get; }
        public string Filename { get; }
        public byte[] ImageData { get; }
    }

    public class UploadClientLogoHandler : BlobRequestHandler<UploadClientLogo, Uri>
    {
        private readonly IMediator _mediator;

        public UploadClientLogoHandler(
            IMediator mediator,
            BlobServiceClient client) : base(client, StorageConstants.ClientsContainerName)
        {
            _mediator = mediator;
        }

        public override async Task<Uri> OnHandleAsync(
            BlobContainerClient client,
            UploadClientLogo request,
            CancellationToken cancellationToken)
        {
            var blobName = $"{request.ClientId}/{request.Filename}";
            var blob = client.GetBlobClient(blobName);
            var i = 1;
            var lastDot = blobName.LastIndexOf('.');

            while (await blob.ExistsAsync(cancellationToken))
            {
                var newName = $"{blobName[..lastDot]} ({i}){blobName[lastDot..]}";
                blob = client.GetBlobClient(newName);
                i++;
            }

            using var stream = new MemoryStream();
            await stream.WriteAsync(request.ImageData.AsMemory(0, request.ImageData.Length), cancellationToken);
            stream.Seek(0, SeekOrigin.Begin);
            await blob.UploadAsync(stream, cancellationToken);

            return blob.Uri;
        }
    }
}
