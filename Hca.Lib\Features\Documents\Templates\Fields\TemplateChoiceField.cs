﻿using System;
using System.Collections.Generic;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Documents.Templates.Fields
{
    public class TemplateChoiceField : TemplateField<ChoiceField>
    {
        // existing fields
        protected TemplateChoiceField(Guid documentFieldId, int fieldOrder, bool isOptional, string serialisedContent) : base(documentFieldId)
        {
            Value = new ChoiceField(fieldOrder, isOptional, serialisedContent);
        }

        protected TemplateChoiceField(
            IEnumerable<Choice> choices,
            int? maxChoices = null,
            int? minChoices = null,
            string hint = null) : base(Guid.NewGuid())
        {
            Value = new ChoiceField(choices, maxChoices, minChoices, hint);
        }

        public static TemplateChoiceField Create(
            IEnumerable<Choice> choices,
            int? maxChoices = null,
            int? minChoices = null,
            string hint = null) => new(choices, maxChoices, minChoices, hint);

        public static TemplateChoiceField Rehydrate(Guid documentFieldId, int fieldOrder, bool isOptional, string serialisedContent) =>
            new(documentFieldId, fieldOrder, isOptional, serialisedContent);
    }
}