﻿//using System.Threading;
//using System.Threading.Tasks;
//using Hca.Lib.Azure.Blob;
//using MediatR;

//namespace Hca.WebHost.Services
//{
//    public class PageService
//    {
//        private readonly IMediator _mediator;

//        public PageService(IMediator mediator)
//        {
//            _mediator = mediator;
//        }

//        public Task<string> GetSasUrl(
//            string containerName,
//            string blobName,
//            bool canWrite = false,
//            CancellationToken cancellationToken = default) =>
//            _mediator
//                .Send(new GetSasUrl(containerName, blobName, canWrite), cancellationToken)
//                .ContinueWith(r => r.Result.AbsoluteUri.ToString());
//    }
//}
