﻿using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries;
using MediatR;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UploadPropertyPhoto : ICommand
    {
        public UploadPropertyPhoto(
            Guid clientId,
            Guid propertyId,
            byte[] imageFile,
            string contentType,
            string filename)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            ImageFile = imageFile;
            ContentType = contentType;
            Filename = filename;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public byte[] ImageFile { get; }
        public string ContentType { get; }
        public string Filename { get; }
    }

    public class UploadPropertyPhotoHandler : DapperRequestHandler<UploadPropertyPhoto, CommandResult>
    {
        private readonly IMediator _mediator;

        public UploadPropertyPhotoHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UploadPropertyPhoto request)
        {
            var blobName = $"{request.ClientId}/properties/{request.PropertyId}/photos/{request.Filename}";
            var containerName = StorageConstants.ClientsContainerName;
            var uploadResponse = await _mediator.Send(new UploadBlob(containerName, blobName, request.ImageFile, request.ContentType));

            if (!uploadResponse.IsSuccess)
            {
                return CommandResult.Fail("Failed to upload photo");
            }

            //var thumbnail = await _mediator.Send(new GetImageThumbnail(request.ImageFile, 256, 256));
            //var thumbnailName = $"{request.ClientId}/properties/{request.PropertyId}/photos/256_{request.Filename}";
            //var uploadThumbnailResponse = await _mediator.Send(new UploadBlob(containerName, thumbnailName, thumbnail));

            //if (!uploadThumbnailResponse.IsSuccess)
            //{
            //    return CommandResult.Fail("Failed to upload photo thumbnail");
            //}

            var dto = await _mediator.Send(new GetProperty(request.PropertyId));

            dto.ImageContainerName = containerName;
            dto.ImageBlobName = blobName;

            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}

