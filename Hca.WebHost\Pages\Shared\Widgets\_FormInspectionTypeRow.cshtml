﻿@*@model Models.SelectRowModel
@using Hca.Lib.Features.Inspections
@{
    var unselected = string.IsNullOrWhiteSpace(Model.SelectedValue) ? "selected" : null;
}
@{ #pragma warning disable 1998 }
@functions {
    string Selected(Hca.Lib.Features.Inspections.InspectionType value) =>
        Model.SelectedValue == ((int)value).ToString() ? "selected" : null;

    async Task RenderOption(InspectionType inspectionType)
    {
        <option selected="@Selected(inspectionType)" value="@inspectionType">@inspectionType.DisplayName()</option>
    }
}
<div class="form-group row">
    <label class="col-md-2 col-form-label">Inspection Type</label>
    <div class="col-md-10">
        <select id="@Model.Id" name="@Model.Name" class="form-control">
            <option selected="@unselected" value="">Please select</option>
            @RenderOption(InspectionType.Type1AsbestosSurvey)
            @RenderOption(InspectionType.Type2AsbestosSurvey)
            @RenderOption(InspectionType.Type3AsbestosSurvey)
            @RenderOption(InspectionType.AsbestosReinspection)
            @RenderOption(InspectionType.AsbestosManagementSurvey)
            @RenderOption(InspectionType.AsbestosRefurbishmentSurvey)
            @RenderOption(InspectionType.AsbestosDemolitionSurvey)
        </select>
    </div>
</div>*@