﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Queries
{
    public class UpdateTemplate : ICommand
    {
        public UpdateTemplate(
            Guid templateId,
            string templateCode,
            TemplateType templateType,
            string templateName)
        {
            TemplateId = templateId;
            TemplateCode = templateCode;
            TemplateName = templateName;
            TemplateType = templateType;
        }

        public Guid TemplateId { get; }
        public string TemplateCode { get; }
        public string TemplateName { get; }
        public TemplateType TemplateType { get; }
    }

    public class UpdateTemplateHandler : DapperRequestHandler<UpdateTemplate, CommandResult>
    {
        public UpdateTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateTemplate request)
        {
            var template = await db.GetAsync<TemplateDto>(request.TemplateId);
            template.TemplateCode = request.TemplateCode;
            template.TemplateName = request.TemplateName;
            template.TemplateType = request.TemplateType;
            await db.UpdateAsync(template);

            return CommandResult.Success();
        }
    }
}
