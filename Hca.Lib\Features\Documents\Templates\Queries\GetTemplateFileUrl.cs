﻿using System;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateFileUrl : IRequest<Uri>
    {
        public GetTemplateFileUrl(Guid templateId, Guid templateFileId)
        {
            TemplateId = templateId;
            TemplateFileId = templateFileId;
        }

        public Guid TemplateId { get; }
        public Guid TemplateFileId { get; }
    }

    public class GetTemplateFileUrlHandler : DapperRequestHandler<GetTemplateFileUrl, Uri>
    {
        private readonly BlobServiceClient _blobServiceClient;
        private readonly IDbHelper _dbHelper;

        public GetTemplateFileUrlHandler(
            IDbHelper dbHelper,
            BlobServiceClient blobServiceClient) : base(dbHelper)
        {
            _blobServiceClient = blobServiceClient;
            _dbHelper = dbHelper;
        }

        public override async Task<Uri> OnHandleAsync(IDbHelper client, GetTemplateFileUrl request)
        {
            var templateDto = await _dbHelper.GetAsync<TemplateDto>(request.TemplateId);
            if (templateDto == null) return null;

            var templateFileDto = await _dbHelper.GetAsync<TemplateFileDto>(request.TemplateFileId);
            if (templateFileDto == null) return null;

            var userDelegationKey = await _blobServiceClient.GetUserDelegationKeyAsync(
                DateTime.UtcNow.AddMinutes(-2),
                DateTime.UtcNow.AddMinutes(5));
            var filePath = $"{request.TemplateId}/{templateFileDto.FileName}";

            var sasBuilder = new BlobSasBuilder()
            {
                BlobContainerName = Azure.StorageConstants.TemplateContainerName,
                BlobName = filePath,
                Resource = "b",
                ExpiresOn = DateTimeOffset.UtcNow.AddMinutes(5)
            };

            sasBuilder.SetPermissions(BlobSasPermissions.Read);

            var containerClient = _blobServiceClient.GetBlobContainerClient(Azure.StorageConstants.TemplateContainerName);
            var blobClient = containerClient.GetBlobClient(filePath);
            var blobUriBuilder = new BlobUriBuilder(blobClient.Uri)
            {
                Sas = sasBuilder.ToSasQueryParameters(userDelegationKey, _blobServiceClient.AccountName)
            };

            return blobUriBuilder.ToUri();
        }
    }
}
