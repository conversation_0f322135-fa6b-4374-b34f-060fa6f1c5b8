﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Commands
{
    public class RemoveSectionFromTemplate : ICommand
    {
        public RemoveSectionFromTemplate(Guid templateId, Guid sectionId)
        {
            TemplateId = templateId;
            SectionId = sectionId;
        }

        public Guid TemplateId { get; }
        public Guid SectionId { get; }
    }

    public class RemoveSectionFromTemplateHandler : DapperRequestHandler<RemoveSectionFromTemplate, CommandResult>
    {
        public RemoveSectionFromTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, RemoveSectionFromTemplate request)
        {
            await db.DeleteAsync<TemplateFieldDto>($"WHERE TemplateSectionId = @{nameof(RemoveSectionFromTemplate.SectionId)}", request);
            await db.DeleteAsync<TemplateSectionDto>(request.SectionId);

            return CommandResult.Success();
        }
    }
}
