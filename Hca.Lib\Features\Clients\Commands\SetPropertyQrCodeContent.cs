﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Services.QRFileSpot;
using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands;

public class SetPropertyQrCodeContent : ICommand
{
    public SetPropertyQrCodeContent(
        Guid clientId, 
        Guid propertyId, 
        Guid documentId,
        string categoryName)
    {
        ClientId = clientId;
        PropertyId = propertyId;
        DocumentId = documentId;
        CategoryName = categoryName;
    }

    public Guid ClientId { get; }
    public Guid PropertyId { get; }
    public Guid DocumentId { get; }
    public string CategoryName { get; }
}

public class SetPropertyQrCodeContentHandler : DapperRequestHandler<SetPropertyQrCodeContent, CommandResult>
{
    private readonly IMediator _mediator;
    private readonly IQrFileSpotService _qrFileSpotService;

    public SetPropertyQrCodeContentHandler(
        IDbHelper dbHelper,
        IQrFileSpotService qrFileSpotService,
        IMediator mediator) : base(dbHelper)
    {
        _qrFileSpotService = qrFileSpotService;
        _mediator = mediator;
    }

    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetPropertyQrCodeContent request)
    {
        var property = await _mediator.Send(new GetProperty(request.PropertyId));
        if (property == null) return CommandResult.Fail("Could not find property");

        var document = await _mediator.Send(new GetPropertyDocument(request.ClientId, request.PropertyId, request.DocumentId));
        if (document == null) return CommandResult.Fail("Could not find document");

        if (property.QrCodeId.IsPopulated())
        {
            await _qrFileSpotService.PutDocumentAsync(
                property.QrCodeId,
                null,
                request.CategoryName,
                "alias",
                document.QrCodeId,
                CancellationToken.None);
        }
        else
        {
            var qrDocument = await _qrFileSpotService.PostDocumentAsync(
                null,
                request.CategoryName,
                "alias",
                document.QrCodeId,
                CancellationToken.None);

            property.QrCodeId = qrDocument.Id;
        }

        property.QrCodeDocumentId = document.QrCodeId;
        await db.UpdateAsync(property);

        return CommandResult.Success();
    }
}
