﻿using OpenXmlPowerTools;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class UserExtensions
    {
        public static CompiledDocument AddSignature(
            this CompiledDocument doc,
            byte[] signature,
            string filename)
        {
            using var wordDoc = doc.AsWordProcessingDocument;
            var mainPart = wordDoc.MainDocumentPart;

            var tagName = "{{UserSignature}}";

            // consolidate into a single run if already across several runs
            TextReplacer.SearchAndReplace(wordDoc, tagName, tagName, true);

            var tagToReplace = mainPart.FindFirstTextElement(tagName);
            if (tagToReplace == null) return doc;

            var imageReference = mainPart.CreateImage(signature, filename);

            tagToReplace.Parent.Parent.InsertAfterSelf(OpenXmlFactory.CreateDrawing(imageReference));

            mainPart.Document.Body.RemoveChild(tagToReplace.Parent.Parent);

            mainPart.Document.Save();

            return doc;
        }
    }
}
