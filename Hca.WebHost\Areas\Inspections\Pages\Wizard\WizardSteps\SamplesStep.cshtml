﻿@page "/inspections/{inspectionId:guid}/samplesstep"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.SamplesModel
@{
    Layout = null;

    // todo: put the sample list into a child and refresh on event, display "no samples" message when empty
    // adding a sample in the simple form immediately refreshes
    // add a spinner for loading
    // pencil edit to mini-wizard, next step is images
}

<div class="row">
    <div class="col-lg-4">
        <div class="card card-default">
            <div class="card-body align-items-center justify-content-center" id="divSampleList"></div>

            <div class="card-body align-items-center justify-content-center" id="divSampleSpinner">
                <div class="sk-cube-grid">
                    <div class="sk-cube sk-cube1"></div>
                    <div class="sk-cube sk-cube2"></div>
                    <div class="sk-cube sk-cube3"></div>
                    <div class="sk-cube sk-cube4"></div>
                    <div class="sk-cube sk-cube5"></div>
                    <div class="sk-cube sk-cube6"></div>
                    <div class="sk-cube sk-cube7"></div>
                    <div class="sk-cube sk-cube8"></div>
                    <div class="sk-cube sk-cube9"></div>
                </div>
            </div>

            <div class="card-footer">
                <button class="btn btn-success" type="button" id="btnNewSample">New Sample</button>
            </div>
        </div>
    </div>
    <div class="col-lg-8">
        <div class="card card-default" id="crdNewSample">
            <div class="card-header">Add a sample</div>
            <div class="card-body">
                <div class="form-group">
                    <label>Sample Reference</label>
                    <input class="form-control" type="text" id="txtNewSampleReference">
                </div>
                <div class="form-group">
                    <label>Sample Notes</label>
                    <textarea class="form-control note-editor" rows="5" id="txtNewSampleNotes"></textarea>
                </div>
                <button class="btn btn-sm btn-secondary" id="btnAddNewSample">Add</button>
            </div>
        </div>
        <div class="card card-default" id="crdEditSample" style="display:none;">
            <div class="card-header">Edit Sample</div>
            <div class="card-body">
                <div id="divEditSample"></div>

                <div class="dropzone mb-3 d-flex flex-row justify-content-center flex-wrap" id="dropzone-area"></div>

                <div class="row" id="divSampleImages"></div>
            </div>
        </div>
    </div>
</div>

<script>
    (() => {
        var dropzoneArea;

        var setSampleImagesContent = (sampleId) => {
            let imagesUrl = "/inspections/@Model.InspectionId/samples/" + sampleId + "/images";

            if (dropzoneArea) {
                dropzoneArea.options.url = imagesUrl;
            } else {
                let dropzoneOptions = {
                    dictDefaultMessage: '<em class="fa fa-upload text-muted"></em><br>Drop images here to upload',
                    paramName: 'Upload',
                    url: imagesUrl,
                    init: function () {
                        var dzHandler = this;

                        this.on("complete", function (file) {
                            this.removeFile(file);

                            $('#divSampleImages').load(imagesUrl);
                            loadSamples(sampleId);
                        });
                    }
                };
                dropzoneArea = new Dropzone('#dropzone-area', dropzoneOptions);
            }

            $('#divSampleImages').load(imagesUrl);
        };

        var selectSampleRow = (e) => {
            $('#crdNewSample').hide();
            $('#crdEditSample').show();

            var sampleId = $(e.currentTarget).data("row-id");
            loadSamples(sampleId);
            $('#divEditSample').load("/inspections/@Model.InspectionId/samples/" + sampleId + "/details/");
            setSampleImagesContent(sampleId);
        };

        var loadSamples = (selected) => {
            $('#divSampleList').hide();
            $('#divSampleSpinner').show();
            $('#divSampleList').load("/inspections/@Model.InspectionId/samples?selected=" + selected, () => {
                $('#divSampleList').show();
                $('#divSampleSpinner').hide();
                $('#tblSamples tr').on('click', selectSampleRow);
            });
        };

        const addNewSample = async () => {
            let model = {
                sampleReference: $('#txtNewSampleReference').val(),
                sampleNotes: $('#txtNewSampleNotes').val()
            };

            let [result, err1, err2, err3] = await to($.ajax({
                type: 'POST',
                url: '/inspections/@Model.InspectionId/samples',
                dataType: 'json',
                contentType: 'application/json; charset=utf-8',
                data: JSON.stringify(model)
            }));

            if (err1) {
                console.log(`Do something with error ${err1}`);
                //return;
            }

            $('#crdNewSample').hide();
            $('#crdEditSample').show();
            loadSamples(result.id);
        };

        let showNewSampleCard = () => {
            $('#crdNewSample').show();
            $('#crdEditSample').hide();
        };

        $('#btnNewSample').on('click', showNewSampleCard);
        $('#btnAddNewSample').on('click', addNewSample);
        loadSamples();
    })();
</script>