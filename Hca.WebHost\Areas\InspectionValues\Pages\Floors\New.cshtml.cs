﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages
{
    public class FloorNewModel : InspectionValueCreatePage
    {
        public FloorNewModel(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override ValueListType ValueListType => ValueListType.Floors;

        public override string IndexUrl => Urls.Floors;
    }
}
