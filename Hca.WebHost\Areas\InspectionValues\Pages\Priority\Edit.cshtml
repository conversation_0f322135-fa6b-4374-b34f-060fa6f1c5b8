﻿@page "/InspectionValues/Priority/{id:guid}"
@model Hca.WebHost.Areas.InspectionValues.Pages.Priority.Edit
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Priority Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Material",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />