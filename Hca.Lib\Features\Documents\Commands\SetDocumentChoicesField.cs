﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Events;
using Hca.Lib.Features.Documents.Fields;
using MediatR;

namespace Hca.Lib.Features.Documents.Commands
{
    public class SetDocumentChoicesField : IRequest<bool>
    {
        public SetDocumentChoicesField(
            Guid documentId,
            Guid sectionId,
            Guid fieldId,
            IEnumerable<Guid> choices)
        {
            DocumentId = documentId;
            SectionId = sectionId;
            FieldId = fieldId;
            Choices = choices;
        }

        public Guid DocumentId { get; }
        public Guid SectionId { get; }
        public Guid FieldId { get; }
        public IEnumerable<Guid> Choices { get; }
    }

    public class SetDocumentChoicesFieldHandler : DapperRequestHandler<SetDocumentChoicesField, bool>
    {
        private readonly IMediator _mediator;
        public SetDocumentChoicesFieldHandler(IMediator mediator, IDbHelper dbHelper) : base(dbHelper) { _mediator = mediator; }

        public override async Task<bool> OnHandleAsync(IDbHelper db, SetDocumentChoicesField request)
        {
            var dto = await db.GetAsync<DocumentFieldDto>(request.FieldId);
            var field = new DocumentChoiceField(dto.Id, dto.FieldOrder, dto.IsOptional, dto.FieldContent);

            foreach (var choice in field.Value.Choices)
            {
                choice.Chosen = request.Choices.Contains(choice.Id);
            }

            var chosenCount = field.Value.Choices.Count(c => c.Chosen);
            dto.IsComplete =
                (!field.Value.MaxChoices.HasValue || chosenCount <= field.Value.MaxChoices.Value)
                && (!field.Value.MinChoices.HasValue || chosenCount >= field.Value.MinChoices.Value);

            dto.FieldContent = field.Value.FieldContent;
            await db.UpdateAsync(dto);

            if (dto.IsComplete)
            {
                await _mediator.Publish(new DocumentFieldCompleted(request.DocumentId, request.SectionId, request.FieldId));
            }

            return true;
        }
    }
}
