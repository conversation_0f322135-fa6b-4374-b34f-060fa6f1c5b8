﻿using System;
using System.Web;

namespace Hca.WebHost
{
    public static class Urls
    {
        public static string EditMode => $"{Constants.QuerystringModeKey}={Constants.EditMode}";
        public static string AddEditMode(this string url) => url.Contains('?') ? $"{url}&{EditMode}" : $"{url}?{EditMode}";

        public static string NewMode => $"{Constants.QuerystringModeKey}={Constants.NewMode}";
        public static string AddNewMode(this string url) => url.Contains('?') ? $"{url}&{NewMode}" : $"{url}?{NewMode}";

        public const string Clients = "/Clients";
        private const string Quotes = "/Quotes";
        public const string Properties = "/Properties";
        public const string Sites = "/Sites";
        public const string Projects = "/Projects";
        public const string Templates = "/Templates";
        public const string QuoteTemplates = "/QuoteTemplates";
        public const string Users = "/Users";
        public const string Contacts = "/Contacts";
        private const string Inspections = "/Inspections";
        private const string InspectionValues = "/InspectionValues";
        public const string DeletedClients = "/DeletedClients";
        public const string DeletedSites = "/DeletedSites";
        public const string DeletedProperties = "/DeletedProperties";

        public const string Query_SiteId = "siteId";

        public static string Floors => $"{InspectionValues}/Floors";
        public static string Material => $"{InspectionValues}/Material";
        public static string MaterialColours => $"{InspectionValues}/MaterialColours";
        public static string Priority => $"{InspectionValues}/Priority";
        public static string Quantity => $"{InspectionValues}/Quantity";
        public static string Recommendation => $"{InspectionValues}/Recommendation";
        public static string FloorPlanColours => $"{InspectionValues}/FloorPlanColours";
        public static string InspectionType => $"{InspectionValues}/InspectionType";

        public static string ProjectsAll => $"{Projects}/all";
        public static string ProjectsNew => $"{Projects}/new";

        public static string User(Guid userId) => $"{Users}/{userId}";
        public static string UserEdit(Guid userId) => $"{Users}/{userId}?mode=edit";
        public static string UserDelete(Guid userId) => $"{Users}/{userId}?handler=delete";

        public static string ClientsNew => $"{Clients}/new";

        public static string Client(Guid clientId) => $"{Clients}/{clientId}";
        public static string ClientDelete(Guid clientId) => $"{Clients}/{clientId}/delete";
        public static string ClientUndelete(Guid clientId) => $"{Clients}/{clientId}/undelete";
        public static string ClientReinstate(Guid clientId) => $"{Clients}/{clientId}/reinstate";
        public static string ClientEdit(Guid clientId) => $"{Client(clientId)}?handler=Edit";
        public static string ClientArchive(Guid clientId) => $"{Client(clientId)}?handler=Archive";
        public static string ClientLogo(Guid clientId) => $"{Client(clientId)}/Logo";

        public static string ClientSites(Guid clientId) => $"{Client(clientId)}{Sites}";
        public static string ClientSite(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}";
        public static string ClientSiteArchive(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/archive";
        public static string ClientSiteDelete(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/delete";
        public static string ClientSiteUndelete(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/undelete";
        public static string ClientSiteReinstate(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/reinstate";
        public static string ClientSiteProperties(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/Properties";
        public static string ClientSitePlans(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/Plans";
        public static string ClientSitePlan(Guid clientId, Guid siteId, Guid planId) => $"{Client(clientId)}{Sites}/{siteId}/Plans/{planId}";
        public static string ClientSitePlanUpload(Guid clientId, Guid siteId) => $"{Client(clientId)}{Sites}/{siteId}/Plans/New";
        public static string ClientSitePlanDelete(Guid clientId, Guid siteId, Guid planId) => $"{Client(clientId)}{Sites}/{siteId}/Plans/{planId}/delete";
        public static string ClientSiteNew(Guid clientId) => $"{Client(clientId)}{Sites}/new";

        public static string ClientProperties(Guid clientId) => $"{Client(clientId)}{Properties}";
        public static string ClientPropertyNew(Guid clientId) => $"{ClientProperties(clientId)}/new";
        public static string ClientPropertyNew(Guid clientId, Guid siteId) => $"{ClientProperties(clientId)}/new?{Query_SiteId}={siteId}";
        public static string ClientProperty(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}";
        public static string ClientPropertyAddPhoto(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}?handler=addphoto";
        public static string ClientPropertyArchive(Guid clientId, Guid propertyId) => $"{ClientProperties(clientId)}/{propertyId}/archive";
        public static string ClientPropertyDelete(Guid clientId, Guid propertyId) => $"{ClientProperties(clientId)}/{propertyId}/delete";
        public static string ClientPropertyUndelete(Guid clientId, Guid propertyId) => $"{ClientProperties(clientId)}/{propertyId}/undelete";
        public static string ClientPropertyReinstate(Guid clientId, Guid propertyId) => $"{ClientProperties(clientId)}/{propertyId}/reinstate";

        public static string ClientPropertyQrCode(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}/qrcode";
        public static string ClientPropertyQrCodeGetDocuments(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}/qrcode?handler=Documents";

        public static string ClientPropertyDocuments(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}?handler=documents";
        public static string ClientPropertyDocumentUpload(Guid clientId, Guid propertyId) =>
            $"{Properties}/{propertyId}?handler=uploaddocument".AddNewMode();
        public static string ClientPropertyDocument(Guid clientId, Guid propertyId, Guid documentId) =>
            $"{Properties}/{propertyId}?handler=editdocument&documentId={documentId}";
        public static string ClientPropertyDocumentDelete(Guid clientId, Guid propertyId, Guid documentId) =>
            $"{Properties}/{propertyId}/Documents/{documentId}/delete";

        public static string FloorPlans(Guid clientId, Guid propertyId) => $"{Properties}/{propertyId}?handler=floorplans";
        public static string FloorPlanUpload(Guid clientId, Guid propertyId) =>
            $"{Properties}/{propertyId}?handler=uploadfloorplan".AddNewMode();
        public static string FloorPlan(Guid clientId, Guid propertyId, Guid floorPlanId) =>
            $"{Properties}/{propertyId}?handler=editfloorplan&floorPlanId={floorPlanId}";
        public static string FloorPlanDelete(Guid clientId, Guid propertyId, Guid floorPlanId) =>
            $"{Properties}/{propertyId}/FloorPlans/{floorPlanId}/delete";

        public static string ClientContacts(Guid clientId) => $"{Client(clientId)}{Contacts}";
        public static string ClientContactNew(Guid clientId) => $"{ClientContacts(clientId)}/new";
        public static string ClientContact(Guid clientId, Guid contactId) => $"{ClientContacts(clientId)}/{contactId}";
        public static string ClientContactDelete(Guid clientId, Guid contactId) => $"{ClientContact(clientId, contactId)}?handler=DeleteContact";

        public static string ClientQuotes(Guid clientId) => $"{Client(clientId)}{Quotes}";
        public static string ClientQuoteNew(Guid clientId) => $"{ClientQuotes(clientId)}/new";
        public static string ClientQuote(Guid clientId, Guid quoteId) => $"{ClientQuotes(clientId)}/{quoteId}";
        public static string ClientQuoteDelete(Guid clientId, Guid quoteId) => $"{ClientQuote(clientId, quoteId)}?handler=Delete";

        public static string ClientQuoteProperty(Guid clientId, Guid quoteId, Guid propertyId) => $"{ClientQuote(clientId, quoteId)}{Properties}/{propertyId}";
        public static string ClientQuotePropertyNew(Guid clientId, Guid quoteId) => $"{ClientQuote(clientId, quoteId)}{Properties}/new";

        public static string ClientQuoteToProject(Guid clientId, Guid quoteId) => $"{ClientQuotes(clientId)}/{quoteId}?handler=CreateProject";
        public static string ClientProject(Guid clientId, Guid projectId) => $"{Client(clientId)}{Projects}/{projectId}";
        public static string ClientProjectDelete(Guid clientId, Guid projectId) => $"{Client(clientId)}{Projects}/{projectId}?handler=delete";
        public static string ClientProjects(Guid clientId) => $"{Client(clientId)}{Projects}";

        //public static string QuoteTemplateNew => $"{QuoteTemplates}/new";
        //public static string QuoteTemplate(Guid quoteTemplateId) => $"{QuoteTemplates}/{quoteTemplateId}";

        public static string ClientProjectInspections(Guid clientId, Guid projectId) => $"{ClientProject(clientId, projectId)}{Inspections}";
        public static string ClientProjectInspection(Guid clientId, Guid projectId, Guid inspectionId) => $"{ClientProjectInspections(clientId, projectId)}/{inspectionId}";
        public static string ClientProjectInspectionsNew(Guid clientId, Guid projectId) => $"{ClientProjectInspections(clientId, projectId)}/new";

        public static string UniqueInspectionRecordNew(Guid inspectionId) => $"{Inspections}/{inspectionId}/records/new";

        public static string TemplateNew => $"{Templates}/new";
        public static string Template(Guid templateId) => $"{Templates}/{templateId}";

        public static string TemplateFiles(Guid templateId) => $"{Template(templateId)}/files";
        public static string TemplateFilesSort(Guid templateId) => $"{Template(templateId)}/files?handler=sort";
        public static string TemplateFileDelete(Guid templateId, Guid fileId) => $"{Template(templateId)}/files/{fileId}?handler=delete";
        public static string TemplateFileDownload(Guid templateId, Guid fileId) => $"{Template(templateId)}/files/{fileId}?handler=download";

        public static string TemplateSections(Guid templateId) => $"{Template(templateId)}/sections";
        public static string TemplateSection(Guid templateId, Guid sectionId) => $"{Template(templateId)}/sections/{sectionId}";
        public static string TemplateSectionDelete(Guid templateId, Guid sectionId) => $"{Template(templateId)}/sections/{sectionId}?handler=delete";
        public static string TemplateSectionNew(Guid templateId) => $"{Template(templateId)}/sections/new";

        public static string TemplateFields(Guid templateId, Guid sectionId) => $"{TemplateSection(templateId, sectionId)}/fields";
        public static string TemplateField(Guid templateId, Guid sectionId, Guid fieldId) => $"{TemplateSection(templateId, sectionId)}/fields/{fieldId}";
        public static string TemplateFieldDelete(Guid templateId, Guid sectionId, Guid fieldId) => $"{TemplateSection(templateId, sectionId)}/fields/{fieldId}?handler=delete";
        public static string TemplateFieldNewText(Guid templateId, Guid sectionId) => $"{TemplateSection(templateId, sectionId)}/addTextField";
        public static string TemplateFieldNewChoice(Guid templateId, Guid sectionId) => $"{TemplateSection(templateId, sectionId)}/addChoiceField";

        public static string Login => "/Identity/Account/Login";
        public static string Logout => "/Identity/Account/Logout";
        public static string AccessDenied => "/Identity/Account/AccessDenied";
        public static string ForgotPassword => "/Identity/Account/ForgotPassword";
        public static string ForgotPasswordConfirmation => "/Identity/Account/ForgotPasswordConfirmation";
        public static string ResetPassword(string email, string token) => $"/Identity/Account/ResetPassword?email={email}&token={HttpUtility.UrlEncode(token)}";
        public static string ResetPasswordConfirmation => "/Identity/Account/ResetPasswordConfirmation";
    }
}
