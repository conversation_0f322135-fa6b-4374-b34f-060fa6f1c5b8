﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;

namespace Hca.Lib.Features.Templates.Commands
{
    public class AddTextFieldToTemplateSection : TemplateCommand
    {
        public AddTextFieldToTemplateSection(
            Guid templateId,
            Guid sectionId,
            TemplateTextField textField) : base(templateId)
        { 
            SectionId = sectionId;
            TextField = textField;
        }

        public Guid SectionId { get; }
        public TemplateTextField TextField { get; }
    }

    public class AddTextFieldToTemplateSectionHandler : DapperRequestHandler<AddTextFieldToTemplateSection, CommandResult>
    {
        public AddTextFieldToTemplateSectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddTextFieldToTemplateSection request)
        {
            if (request.TextField.Value.FieldOrder == 0)
            {
                var sectionFieldCount = await db.ExecuteScalarAsync<int>(
                    "SELECT COUNT(1) FROM tblTemplateFields WHERE TemplateSectionId=@SectionId",
                    new { request.SectionId });

                request.TextField.Value.FieldOrder = sectionFieldCount + 1;
            }

            await db.InsertAsync(new TemplateFieldDto
            {
                Id = request.TextField.TemplateFieldId,
                TemplateSectionId = request.SectionId,
                FieldOrder = request.TextField.Value.FieldOrder,
                FieldType = FieldType.Text,
                Optional = request.TextField.Value.IsOptional,
                FieldContent = request.TextField.Value.FieldContent,
            });

            return CommandResult.Success();
        }
    }
}
