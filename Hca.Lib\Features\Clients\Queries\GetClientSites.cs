﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientSites : IQueryMany<SiteDtoExtended>
    {
        public GetClientSites(Guid clientId, string searchText, bool isArchived = false, bool isDeleted = false) 
        {
            ClientId = clientId;
            SearchText = searchText;
            IsArchived = isArchived;
            IsDeleted = isDeleted;
        }

        public Guid ClientId { get; }
        public string SearchText { get; }
        public bool IsArchived { get; }
        public bool IsDeleted { get; }
    }

    public class GetClientSitesHandler : DapperRequestHandler<GetClientSites, DtoSet<SiteDtoExtended>>
    {
        public GetClientSitesHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<SiteDtoExtended>> OnHandleAsync(IDbHelper db, GetClientSites request)
        {
            var items = await db.QueryAsync<SiteDtoExtended, AddressDto>(
                $"SELECT {TableNames.Sites}.*, " +
                $"(SELECT COUNT(1) FROM {TableNames.Properties} " +
                $"  WHERE {nameof(PropertyDto.SiteId)} = {TableNames.Sites}.{nameof(SiteDto.Id)}" +
                $"  AND Archived IS NULL" +
                $"  AND Deleted IS NULL) AS PropertyCount, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Id)} AS pkAddressId, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.StreetName)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Town)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.City)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.County)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Postcode)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Country)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Lat)}, " +
                $"{TableNames.Addresses}.{nameof(AddressDto.Lon)} " +
                $"FROM {TableNames.Sites} " +
                $"LEFT JOIN {TableNames.Addresses} " +
                $"ON {nameof(SiteDtoExtended.AddressId)} = {TableNames.Addresses}.{nameof(AddressDto.Id)} " +
                $"WHERE {nameof(SiteDtoExtended.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND Archived IS {(request.IsArchived ? "NOT NULL" : "NULL")} " +
                $"AND Deleted IS {(request.IsDeleted ? "NOT NULL" : "NULL")} " +
                $"ORDER BY SiteName, StreetName, Town",
                (p, a) => { p.Address = a; return p; },
                "pkAddressId",
                request);

            if (!request.SearchText.IsNullOrWhiteSpace())
            {
                items = items.Where(i => i.GetDisplayText().Contains(request.SearchText, StringComparison.OrdinalIgnoreCase));
            }

            return DtoSet.From(items);
        }
    }
}
