﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientSite : IQuery<SiteDtoExtended>
    {
        public GetClientSite(Guid clientId, Guid siteId) 
        {
            ClientId = clientId;
            SiteId = siteId;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
    }

    public class GetClientSiteHandler : DapperRequestHandler<GetClientSite, SiteDtoExtended>
    {
        public GetClientSiteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public async override Task<SiteDtoExtended> OnHandleAsync(IDbHelper db, GetClientSite request)
        {
            var site = await db.QuerySingleOrDefaultAsync<SiteDtoExtended>(
                $"SELECT *, " +
                $"  (SELECT COUNT(1) FROM {TableNames.Properties} " +
                $"      WHERE {nameof(PropertyDto.SiteId)} = {TableNames.Sites}.{nameof(SiteDto.Id)}) AS PropertyCount " +
                $"FROM {TableNames.Sites} " +
                $"WHERE {TableNames.Sites}.{nameof(SiteDto.Id)} = @SiteId", request);
            if (site == null) return null;

            if (site.AddressId.HasValue)
            {
                site.Address = await db.GetAsync<AddressDto>(site.AddressId.Value);
            }

            return site;
        }
    }
}
