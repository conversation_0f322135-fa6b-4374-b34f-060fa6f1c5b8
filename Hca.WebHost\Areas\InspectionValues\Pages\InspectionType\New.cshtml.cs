﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.InspectionType
{
    public class New : InspectionValueCreatePage
    {
        public New(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override ValueListType ValueListType => ValueListType.InspectionType;

        public override string IndexUrl => Urls.InspectionType;
    }
}
