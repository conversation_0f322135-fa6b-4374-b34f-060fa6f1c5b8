﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using MediatR;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UploadSitePlan : ICommand
    {
        public UploadSitePlan(
            Guid clientId,
            Guid siteId,
            byte[] imageFile,
            string imageFileContentType,
            string filename,
            string notes)
        {
            ClientId = clientId;
            ImageFile = imageFile;
            ImageFileContentType = imageFileContentType;
            Filename = filename;
            SiteId = siteId;
            Notes = notes;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
        public byte[] ImageFile { get; }
        public string ImageFileContentType { get; }
        public string Filename { get; }
        public string Notes { get; }
    }

    public class UploadSitePlanHandler : DapperRequestHandler<UploadSitePlan, CommandResult>
    {
        private readonly IMediator _mediator;
        private readonly SiteCountsService _siteCountsService;

        public UploadSitePlanHandler(IDbHelper dbHelper, IMediator mediator, SiteCountsService siteCountsService) : base(dbHelper)
        {
            _mediator = mediator;
            _siteCountsService = siteCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UploadSitePlan request)
        {
            var blobName = $"{request.ClientId}/sites/{request.SiteId}/plans/{request.Filename}";
            var containerName = StorageConstants.ClientsContainerName;
            var uploadResponse = await _mediator.Send(new UploadBlob(containerName, blobName, request.ImageFile, request.ImageFileContentType));

            if (!uploadResponse.IsSuccess)
            {
                return CommandResult.Fail("Failed to upload site plan");
            }

            var thumbnail = await _mediator.Send(new GetImageJpegThumbnail(request.ImageFile, 0, 100));
            string thumbnailName = null;

            if (thumbnail != null)
            {
                thumbnailName = $"{request.ClientId}/sites/{request.SiteId}/plans/100_{request.Filename}";
                var uploadThumbnailResponse = await _mediator.Send(new UploadBlob(containerName, thumbnailName, thumbnail, "image/jpeg"));

                if (!uploadThumbnailResponse.IsSuccess)
                {
                    return CommandResult.Fail("Failed to upload site plan thumbnail");
                }
            }

            var dto = new SitePlanDto
            {
                Id = Guid.NewGuid(),
                ContainerName = containerName,
                BlobName = blobName,
                ThumbnailName = thumbnailName,
                SiteId = request.SiteId,
                Notes = request.Notes,
                Created = DateTime.UtcNow,
            };

            await db.InsertAsync(dto);

            _siteCountsService.ClearSiteCountsAsync(request.SiteId);

            return CommandResult.Success();
        }
    }
}

