﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClients : IQueryMany<ClientDto>
    {
        public GetClients(IEnumerable<Guid> clientId)
        {
            ClientId = clientId;
        }

        public IEnumerable<Guid> ClientId { get; }
    }

    public class GetClientsHandler : DapperRequestHandler<GetClients, DtoSet<ClientDto>>
    {
        public GetClientsHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<DtoSet<ClientDto>> OnHandleAsync(IDbHelper db, GetClients request)
        {
            var clientIds = string.Join(",", request.ClientId.Select(id => $"'{id}'"));

            return request.ClientId.Any()
                ? db.GetListAsync<ClientDto>(
                    $"WHERE {nameof(ClientDto.Id)} IN ({clientIds})",
                    request)
                    .ContinueWith(r => DtoSet.From(r.Result))
                : Task.FromResult(DtoSet.From(Enumerable.Empty<ClientDto>()));
        }
    }
}
