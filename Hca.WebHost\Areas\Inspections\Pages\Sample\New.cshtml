﻿@page "/Inspections/{inspectionId:guid}/records/new"
@model Hca.WebHost.Areas.Inspections.Pages.Sample.NewModel
@using Hca.Lib.Features.Inspections
@{
    ViewData["Title"] = "New UIR";
}

<style>
    .wide-prepend {
        width: 215px;
    }

    .narrow-prepend {
        width: 160px;
    }

    .greyBackground {
        background-color: lightgrey;
    }

    .bg-success-dark {
        background-color: #1e983b !important;
    }

    .bg-danger-dark {
        background-color: #ec2121 !important;
    }

    .fa-check:not(:checked) {
        background-color: white;
    }

    .form-control:disabled, .form-control[readonly] {
        background-color: grey;
        opacity: 1;
    }
</style>

<div class="content-heading">
    <div>
        Add Unique Inspection Record
        <small></small>
    </div>
</div>

<div class="card card-default greyBackground">
    <form method="post" id="frmSample" action="@Urls.UniqueInspectionRecordNew(Model.InspectionId)">
        <div class="card-body">

            <!-- ROW 1 -->
            <div class="form-row align-items-center">
                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="inspection-record">Inspection Record</span>
                        </div>
                        <input class="form-control" type="text" aria-label="Inspection Record" aria-describedby="inspection-record" asp-for="PostModel.InspectionRecord">
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="status">Status</span>
                        </div>
                        <select class="chosen-select form-control" id="ddlStatus" aria-label="Status" aria-describedby="status" asp-for="PostModel.Status">
                            <option value="">Select</option>
                            <option value="@InspectionStatusCodes.Sampled">Sampled</option>
                            <option value="@InspectionStatusCodes.VisuallyIdentical">Visually Identical</option>
                            <option value="@InspectionStatusCodes.NoSample">No Sample</option>
                            <option value="@InspectionStatusCodes.NoAccess">No Access</option>
                        </select>
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="floorplan-colour">Floor Plan Colour</span>
                        </div>
                        <select class="chosen-select form-control invert-text"
                                id="ddlFloorColour"
                                aria-label="Floor Plan Colour"
                                aria-describedby="floorplan-colour"
                                asp-for="PostModel.FloorPlanColour">
                            <option value="">Select</option>
                            @foreach (var colour in Model.FloorPlanColours)
                            {
                                <option data-hex-colour="@colour.Value" title="@colour.HintText" value="@colour.Id">@colour.DisplayText</option>
                            }
                        </select>
                    </div>
                </div>
            </div>

            <!-- SAMPLE IMAGE -->
            <div class="row">
                <div class="col" id="divImages">
                    <div class="mb-3 d-flex flex-row justify-content-center flex-wrap dropzone" id="divImagesPreview">

                    </div>
                </div>
            </div>

            <!-- ROW 2 -->
            <div class="form-row align-items-center">
                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="floor">Floor</span>
                        </div>

                        <select class="chosen-select form-control" id="ddlFloor" aria-label="Floor" asp-for="PostModel.Floor">
                            <option value="">Select</option>
                            @foreach (var floor in Model.Floors)
                            {
                                <option title="@floor.HintText" value="@floor.Id">@floor.DisplayText</option>
                            }
                        </select>
                        <input class="form-control" style="display: none;" id="txtFloor" aria-label="Floor" asp-for="PostModel.FloorActual" />
                        <span class="input-group-append input-group-addon" id="btnFloorReset" style="display: none;">
                            <span class="input-group-text far fa-times-circle"></span>
                        </span>
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="floorplan-reference">Room Reference</span>
                        </div>
                        <input class="form-control" type="text" aria-label="Floor Plan Reference" aria-describedby="floorplan-reference" asp-for="PostModel.FloorPlanReference">

                    </div>
                </div>
            </div>

            <!-- ROW 3 -->
            <div class="form-row align-items-center">
                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="room-usage">Room Usage</span>
                        </div>
                        <input class="form-control" type="text" aria-label="Room Usage" aria-describedby="room-usage" asp-for="PostModel.RoomUsage">
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="location">Location</span>
                        </div>
                        <input class="form-control" type="text" aria-label="Location" aria-describedby="location" asp-for="PostModel.Location">
                    </div>
                </div>
            </div>

            <!-- ROW 4 -->
            <div class="form-row align-items-center">
                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="material">Material</span>
                        </div>

                        <select class="chosen-select form-control" id="ddlMaterial" aria-label="Material" aria-describedby="material" asp-for="PostModel.Material">
                            <option value="">Select</option>
                            @foreach (var value in Model.Materials)
                            {
                                <option title="@value.HintText" value="@value.Id">@value.DisplayText</option>
                            }
                        </select>
                        <input class="form-control" style="display: none;" id="txtMaterial" aria-label="Material" asp-for="PostModel.MaterialActual" />
                        <span class="input-group-append input-group-addon" id="btnMaterialReset" style="display: none;">
                            <span class="input-group-text far fa-times-circle"></span>
                        </span>
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="material-colour">Material Colour</span>
                        </div>
                        <input class="form-control" type="text" aria-label="Material Colour" aria-describedby="material-colour" asp-for="PostModel.MaterialColour">
                    </div>
                </div>

                <div class="col">
                    <div class="input-group mb-3">
                        <div class="input-group-prepend">
                            <span class="input-group-text narrow-prepend bg-green-light" id="quantity">Quantity</span>
                        </div>
                        <input class="form-control" id="txtQuantity" type="text" aria-label="Quantity" aria-describedby="quantity" asp-for="PostModel.Quantity">
                        <select class="chosen-select form-control input-group-append" id="ddlQuantity" aria-label="Quantity" aria-describedby="quantity" asp-for="PostModel.QuantityUnit">
                            <option value="">Select</option>
                            @foreach (var value in Model.Quantities)
                            {
                                <option title="@value.HintText" value="@value.Id">@value.DisplayText</option>
                            }
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="col">
                    <div class="checkbox c-checkbox">
                        <label>
                            <input type="checkbox" asp-for="PostModel.AsbestosPresumed" id="chkAsbestosPresumed">
                            <span class="fa fa-check"></span> Asbestos detected or presumed
                        </label>
                    </div>
                </div>
            </div>

            <div id="divAsbestosPresumed" style="display: none;">

                <!-- Material Assessment & Priority Assessment -->
                <div class="form-row align-items-center">
                    <div class="col">
                        <!-- Material -->
                        <div class="text-center d-flex">
                            <div class="b0 w-100 bg-green" style="padding: 7px;">Material</div>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="product-type">Product Type (1-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlMaterialProductType" aria-label="Product Type (1-3)" aria-describedby="product-type" asp-for="PostModel.ProductType">
                                <option value="">Select</option>
                                <option value="1" title="Asbestos reinforced plastics, composites, resins, mastics, putties, felts, bitumen, floor tiles, textured coatings, cement.">1</option>
                                <option value="2" title="Asbestos insulating board, millboard, textiles, ropes, gaskets, paper and cardboard.">2</option>
                                <option value="3" title="Asbestos loose fill, sprayed coating and insulation.">3</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="damage">Extent of Damage (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlMaterialDamage" aria-label="Extent of Damage (0-3)" aria-describedby="damage" asp-for="PostModel.ExtentOfDamage">
                                <option value="">Select</option>
                                <option value="0" title="Good condition, no visible damage at all, completely sealed or enclosed.">0</option>
                                <option value="1" title="Low damage, a few scratches, surface marks or some visble unsealed areas.">1</option>
                                <option value="2" title="Medium damage, visible cracking or breakage.">2</option>
                                <option value="3" title="High damage resulting in hanging or loose asbestos debris.">3</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="surface">Surface Treatment (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlMaterialSurface" aria-label="Surface Treatment (0-3)" aria-describedby="surface" asp-for="PostModel.SurfaceTreatment">
                                <option value="">Select</option>
                                <option value="0" title="Asbestos reinforced plastics, composites, resins, mastics, felts, floor tiles and linoleum (if not paper backed).">0</option>
                                <option value="1" title="Enclosed sprays and lagging, sealed asbestos insulating board (with exposed face emcapsulated or enclosed), encapsulated or enclosed textiles, cement products, textured coatings, completely concealed gaskets.">1</option>
                                <option value="2" title="Exposed or partially exposed asbestos insulating board, exposed textiles, paper, cardboard, encapsulated or enclosed sprayed coatings and insulation, exposed gaskets.">2</option>
                                <option value="3" title="Unsealed sprayed coating and insulation.">3</option>
                            </select>
                        </div>
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="asbestos-type">Asbestos Type (1-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlMaterialAsbestosType" aria-label="Asbestos Type (1-3)" aria-describedby="asbestos-type" asp-for="PostModel.AsbestosType">
                                <option value="">Select</option>
                                <option value="1" title="Chrysotile (white) asbestos.">1</option>
                                <option value="2" title="Amosite (brown) asbestos, and asbestiform anthophyllite, tremolite or actinolite.">2</option>
                                <option value="3" title="Crocidolite (blue) asbestos.">3</option>
                            </select>
                        </div>
                    </div>

                    <div class="col" style="top: -8px;">
                        <!-- Priority -->
                        <div class="text-center d-flex">
                            <div class="b0 w-100 bg-green" style="padding: 7px;">Priority</div>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="activity">Occupant Activity (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlPriorityActivity" aria-label="Occupant Activity (0-3)" aria-describedby="activity" asp-for="PostModel.OccupantActivity">
                                <option value="">Select</option>
                                <option value="0" title="Never or rarely occupied, such as vacant property, risers, plant rooms, structural voids and high level external areas.">0</option>
                                <option value="1" title="Usually occupied, such as offices, kitchens, WCs, stores and stairwells.">1</option>
                                <option value="2" title="Nearly always occupied such as call centres, train stations, shopping malls, distribution warehouses and factories.">2</option>
                                <option value="3" title="Constant occupation.">3</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="disturbance">Likelihood of Disturbance (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlPriorityDisturbance" aria-label="Likelihood of Disturbance (0-3)" aria-describedby="disturbance" asp-for="PostModel.LikelihoodOfDisturbance">
                                <option value="">Select</option>
                                <option value="0" title="Usually inaccessible, will not be disturbed under normal conditions.">0</option>
                                <option value="1" title="Occasionally likely to be disturbed.">1</option>
                                <option value="2" title="Easily disturbed.">2</option>
                                <option value="3" title="Routinely disturbed.">3</option>
                            </select>
                        </div>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="occupants">Number of Occupants (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlPriorityOccupants" aria-label="Number of Occupants (0-3)" aria-describedby="occupants" asp-for="PostModel.NumberOfOccupants">
                                <option value="">Select</option>
                                <option value="0" title="No occupants at all.">0</option>
                                <option value="1" title="1-3 occupants.">1</option>
                                <option value="2" title="4-10 occupants.">2</option>
                                <option value="3" title="More than 10 occupants.">3</option>
                            </select>
                        </div>
                        <div class="input-group b-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="maintenance-type">Type of Maintenance (0-3)</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlPriorityMaintenance" aria-label="Type of Maintenance (0-3)" aria-describedby="maintenance-type" asp-for="PostModel.TypeOfMaintenance">
                                <option value="">Select</option>
                                <option value="0" title="Unlikely possiblity of contact during maintenance or repair works.">0</option>
                                <option value="1" title="Possible contact during maintenance or repair works.">1</option>
                                <option value="2" title="Probable contact resulting in potential movement of the asbestos containing material during maintenance or repair works.">2</option>
                                <option value="3" title="Definite removal required during normal maintenance or repair works, particularly relevant where the asbestos containing material forms a key component.">3</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!--Total score-->
                <div class="form-row align-items-center">
                    <div class="col">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="risk-score">Total Risk Assessment Score</span>
                            </div>
                            <input id="txtTotalRiskScore" readonly class="form-control" type="text" aria-label="Total Risk Assessment Score" aria-describedby="risk-score">
                        </div>
                    </div>

                    <div class="col">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend">
                                <span class="input-group-text wide-prepend bg-green-light" id="risk-level">Risk Level Posed</span>
                            </div>
                            <input id="txtRiskLevel" readonly class="form-control" type="text" aria-label="Risk Level Posed" aria-describedby="risk-level">
                        </div>
                    </div>
                </div>

                <!-- Accessibility -->
                <div class="form-row align-items-center">
                    <div class="col">
                        <div class="input-group mb-3">
                            <div class="input-group-prepend" style="width: 50%;">
                                <span class="input-group-text bg-green-light" style="width: 100%;" id="accessibility">Accessibility of the Asbestos Containing Material</span>
                            </div>
                            <select class="chosen-select form-control" id="ddlAccessibility" aria-label="Accessibility of the Asbestos Containing Material" aria-describedby="accessibility" asp-for="PostModel.Accessibility">
                                <option value="">Select</option>
                                <option value="1" title="Exposed and can be touched without access equipment.">Easy</option>
                                <option value="2" title="Either enclosed and/or stepladders needed.">Medium</option>
                                <option value="3" title="Destructive techniques and/or specialist access equipment required.">Difficult</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Recommendation -->
                <div class="form-row align-items-center">
                    <div class="col">
                        <div class="input-group">
                            <div class="input-group-prepend" style="width: 50%;">
                                <span class="input-group-text bg-green-light" style="width: 100%;" id="recommendation">Management Recommendation</span>
                            </div>
                            <div class="input-group-prepend" style="width: 50%;">
                                <span class="input-group-text bg-green-light" style="width: 100%;" id="priority">Priority</span>
                            </div>
                        </div>
                        <div class="input-group mb-3">
                            <select class="chosen-select form-control" id="ddlRecommendation" aria-label="Management Recommendation" aria-describedby="recommendation" asp-for="PostModel.ManagementRecommendation">
                                <option value="">Select</option>
                                @foreach (var value in Model.Recommendations)
                                {
                                    <option title="@value.HintText" value="@value.Id">@value.DisplayText</option>
                                }
                            </select>
                            <select class="chosen-select form-control" id="ddlPriority" aria-label="Priority" aria-describedby="priority" asp-for="PostModel.Priority">
                                <option value="">Select</option>
                                @foreach (var value in Model.Priorities)
                                {
                                    <option title="@value.HintText" value="@value.Id">@value.DisplayText</option>
                                }
                            </select>
                        </div>
                    </div>
                </div>

            </div>

            <!-- Additional Comments -->
            <div class="form-row align-items-center">
                <div class="col">
                    <div class="input-group">
                        <div class="input-group-prepend" style="width: 100%;">
                            <span class="input-group-text bg-green-light" style="width: 100%;" id="comments">Additional Comments</span>
                        </div>
                    </div>
                    <div class="input-group mb-3">
                        <textarea rows="6" class="form-control" aria-label="Additional Comments" aria-describedby="comments" asp-for="PostModel.AdditionalComments"></textarea>
                    </div>
                </div>
            </div>

        </div>
        <div class="card-footer">
            <input class="btn btn-success" type="submit" value="Save" />
        </div>
    </form>
</div>

@section scripts{
    <script>$(function () {
            let dropzoneOptions = {
                autoProcessQueue: false,
                uploadMultiple: true,
                parallelUploads: 5,
                maxFiles: 5,
                addRemoveLinks: true,
                clickable: '#divImagesPreview',
                previewsContainer: '#divImagesPreview',
                hiddenInputContainer: '#divImages',
                dictDefaultMessage: '<em class="fa fa-upload text-muted"></em><br>Drop images here to upload',
                paramName: 'images',
                url: $('#frmSample').attr("action"),
                init: function () {
                    var myDropzone = this;

                    $('#divImagesPreview').append(
                        Dropzone.createElement(
                            `<div class="dz-default dz-message">${dropzoneOptions.dictDefaultMessage}</div>`
                        )
                    );

                    myDropzone.on("addedfile", function (file) {
                        $('.dz-message').hide();
                    });

                    myDropzone.on("removedfile", function (file) {
                        if (myDropzone.files.length === 0) {
                            $('.dz-message').show();
                        }
                    });

                    myDropzone.on("complete", function (file) {
                        window.location = '@Urls.Projects';
                    });

                    $('#frmSample input[type=submit]').click(function (e) {
                        e.preventDefault();
                        e.stopPropagation();
                        if (myDropzone.getQueuedFiles().length === 0) {
                            $('#frmSample').submit();
                        }
                        else {
                            myDropzone.processQueue();
                        }
                    });
                }
            };
            dropzoneArea = new Dropzone('#frmSample', dropzoneOptions);

    $('#ddlFloor').change(function () {
        $('#txtFloor').val($("#ddlFloor option:selected").text());
        $('#ddlFloor').hide();
        $('#txtFloor').show();
        $('#btnFloorReset').show();
    });

    $('#btnFloorReset').click(function () {
        $('#txtFloor').removeAttr('value');
        $('#ddlFloor').prop('selectedIndex', 0);
        $('#ddlFloor').show();
        $('#txtFloor').hide();
        $('#btnFloorReset').hide();
    });

    $('#ddlMaterial').change(function () {
        $('#txtMaterial').val($("#ddlMaterial option:selected").text());
        $('#ddlMaterial').hide();
        $('#txtMaterial').show();
        $('#btnMaterialReset').show();
    });

    $('#btnMaterialReset').click(function () {
        $('#txtMaterial').removeAttr('value');
        $('#ddlMaterial').prop('selectedIndex', 0);
        $('#ddlMaterial').show();
        $('#txtMaterial').hide();
        $('#btnMaterialReset').hide();
    });

    $('#ddlStatus').change(function () {
        if ($('#ddlStatus').val() === '@InspectionStatusCodes.NoAccess') {
            $('#ddlMaterial').prop("selectedIndex", 0);
            $('#txtMaterialColour').val("");
            $('#ddlQuantity').prop("selectedIndex", 0);
            $('#txtMaterial').val("");
            $('#txtQuantity').val("");
            $('#btnMaterialReset').click();
        }
    });

            function showAsbestosFields() {
                if ($('#chkAsbestosPresumed')[0].checked) {
                    $('#divAsbestosPresumed').show();
                } else {
                    $('#divAsbestosPresumed').hide();
                }
            }

            $('#chkAsbestosPresumed').change(showAsbestosFields);

            // initialise fields
            showAsbestosFields();
        });</script>
}
