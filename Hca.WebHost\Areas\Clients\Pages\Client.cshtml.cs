﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientModel(ClientService clientService, IMediator mediator, ViewManager viewManager) : base(mediator, viewManager)
    {
        _clientService = clientService; 
    }

    [Required]
    [BindProperty]
    public ClientDto Client { get; set; }

    [Required]
    [BindProperty]
    public AddressDto Address { get; set; } = new AddressDto();

    public async Task<IActionResult> OnGetAsync(string clientId, CancellationToken cancellationToken)
    {
        if (!Guid.TryParse(clientId, out var guid))
        {
            Client = await _mediator.Send(new GetClientByUrlQuery(clientId), cancellationToken);
        }
        else
        {
            Client = await _clientService.GetClientAsync(guid, cancellationToken);
        }

        if (Client == null) return NotFound();

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(Client.Id, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (Client.AddressId.HasValue)
        {
            Address = await _clientService.GetAddressAsync(Client.AddressId.Value, cancellationToken);
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(Client.Id, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return Page();
        }

        await _clientService.UpdateClientAsync(Client, Address, cancellationToken);
        return Redirect(Urls.Client(clientId));
    }

    public async Task<IActionResult> OnPostArchiveAsync(Guid clientId, CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();
        //{
        //    var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
        //    if (!valid.IsValid) return Forbid();
        //}

        var archiveReason = Request.Form[Constants.ArchiveReasonFormName];

        await _clientService.ArchiveClientAsync(clientId, archiveReason, cancellationToken);
        return Redirect(Urls.Clients);
    }
}
