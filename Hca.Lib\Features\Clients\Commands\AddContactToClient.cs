﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Domain.Commands;

public class AddContactToClient : ICommand
{
    public AddContactToClient(
        Guid clientId,
        ContactDto contact)
    {
        ClientId = clientId;
        Contact = contact;
    }

    public Guid ClientId { get; }
    public ContactDto Contact { get; }
}

public class AddContactToClientHandler : DapperRequestHandler<AddContactToClient, CommandResult>
{
    private readonly ClientCountsService _clientCountsService;

    public AddContactToClientHandler(IDbHelper dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
    {
        _clientCountsService = clientCountsService;
    }

    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddContactToClient request)
    {
        var existing = await db.GetListAsync<ContactDto>($"WHERE {nameof(ContactDto.Email)} = @{nameof(request.Contact.Email)}", request.Contact);
        if (existing.Any()) return CommandResult.Fail("That email address already exists in the system");

        request.Contact.ClientId = request.ClientId;

        await db.InsertAsync(request.Contact);

        _clientCountsService.ClearClientCountsAsync(request.ClientId);

        return CommandResult.Success();
    }
}
