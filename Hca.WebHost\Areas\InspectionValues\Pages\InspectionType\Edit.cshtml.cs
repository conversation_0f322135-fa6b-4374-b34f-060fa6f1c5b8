﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Templates;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.InspectionValues.Pages.InspectionType;

public class Edit : InspectionValueEditPage
{
    private readonly InspectionService _inspectionService;
    private readonly TemplateService _templateService;

    public Edit(
        ValueListsService inspectionValuesService,
        TemplateService templateService,
        InspectionService inspectionService,
        IMediator mediator,
        ViewManager viewManager) : base(inspectionValuesService, mediator, viewManager)
    {
        _inspectionService = inspectionService;
        _templateService = templateService;
    }

    public override ValueListType ValueListType => ValueListType.InspectionType;

    public override string IndexUrl => Urls.InspectionType;

    public IEnumerable<TemplateDto> Templates { get; private set; }

    public TemplateDto DefaultTemplate { get; private set; }

    [BindProperty]
    public Guid DefaultTemplateId { get; set; }

    public override async Task OnGetAsync(Guid id, CancellationToken cancellationToken)
    {
        await base.OnGetAsync(id, cancellationToken);

        var defaultTemplateId = await _inspectionService.GetDefaultInspectionReportTemplate(id, cancellationToken);
        Templates = (await _templateService.GetTemplatesAsync(TemplateType.Report, cancellationToken)).Items;

        if (defaultTemplateId.HasValue)
        {
            DefaultTemplateId = defaultTemplateId.Value;
            DefaultTemplate = Templates.SingleOrDefault(t => t.Id == defaultTemplateId);
        }
    }

    public override async Task<IActionResult> OnPostAsync(Guid id, CancellationToken cancellationToken)
    {
        var result = await base.OnPostAsync(id, cancellationToken);

        if (DefaultTemplateId != Guid.Empty)
        {
            await _inspectionService.SetDefaultInspectionReportTemplate(
                id,
                DefaultTemplateId,
                cancellationToken);
        }

        return result;
    }
}
