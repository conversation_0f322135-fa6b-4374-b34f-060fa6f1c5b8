﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{ Layout = "_LayoutRoot"; }

@section scripts {
    @RenderSection("scripts", required: false)
}

@section Styles {
    @RenderSection("Styles", required: false)
}

@section BodyArea {
    @RenderSection("BodyArea", required: false)
}

<div class="wrapper">
    <!-- top navbar-->
    <header class="topnavbar-wrapper">
        <partial name="_TopNavbar" />
    </header>

    <!--sidebar-->
    <aside class="aside-container">
        @RenderSection("Sidebar", required: false)
    </aside>

    @if (await ViewManager.IsHcaUser)
    {<!-- offsidebar-->
                        <aside class="offsidebar d-none">
                            <partial name="_Offsidebar" />
                        </aside>}

    <!-- Main section-->
    <section class="section-container">
        <!-- Page content-->
        <div class="content-wrapper">
            <div class="topRow">
                <div>

                </div>
                <div class="buttonCol">
                    <form style="display: inline-block;"
                          asp-area="Identity"
                          asp-page="/Account/Logout"
                          asp-route-returnUrl="@Url.Page("/", new { area = "" })"
                          method="post">
                        <button type="submit" class="btn btn-danger">Logout</button>
                    </form>
                </div>
            </div>
            <style>
                .topRow {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    margin-bottom: 10px;
                }

                .buttonCol {
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: flex-end;
                    gap: 10px;
                }

                    .buttonCol a {
                        min-width: 100px;
                    }
            </style>
            @RenderBody()
        </div>
    </section>
    <!-- Page footer-->
    <footer class="footer-container">
        <partial name="_Footer" />
    </footer>
</div>
