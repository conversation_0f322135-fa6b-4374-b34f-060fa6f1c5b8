﻿using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Config.Queries;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Documents.Templates.Queries;
using Hca.Lib.Features.Templates;
using Hca.Lib.Features.Templates.Data.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Queries
{
    public class GetQuoteTemplate : IQuery<TemplateObject>
    {
    }

    public class GetQuoteTemplateHandler : IRequestHandler<GetQuoteTemplate, TemplateObject>
    {
        private readonly IMediator _mediator;

        public GetQuoteTemplateHandler(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task<TemplateObject> Handle(GetQuoteTemplate request, CancellationToken cancellationToken)
        {
            // see if the default is configured
            var quoteTemplateConfig = await _mediator.Send(new GetConfig("QuoteTemplate"), cancellationToken);
            if (Guid.TryParse(quoteTemplateConfig?.StringValue, out var templateId))
            {
                return await _mediator.Send(new GetTemplateObject(templateId), cancellationToken);
            }

            // otherwise get the first quote template available
            var anyTemplate = (await _mediator.Send(new FindTemplates(TemplateType.Quote), cancellationToken)).Items.FirstOrDefault();
            if (anyTemplate != null)
            {
                return await _mediator.Send(new GetTemplateObject(anyTemplate.Id));
            }

            // otherwise just cook one up
            templateId = Guid.Empty;
            var sectionId = Guid.Empty;
            var fieldId = Guid.Empty;

            return new TemplateObject
            {
                Id = templateId,
                TemplateName = "Quote Template",
                TemplateCode = "QUOTE001",
                TemplateType = TemplateType.Quote,
                Sections = new[]
                {
                    new TemplateSection
                    {
                        TemplateSectionId = sectionId,
                        SectionCode = "SECTION1",
                        SectionOrder = 1,
                        Fields = new []
                        {
                            TemplateTextField.Create(
                                @"<span contenteditable=""true"" class=""hint"">[Insert brief description of letter subject, e.g., Asbestos Reinspection quote, Address]</span><span></span>",
                                false)
                        }
                    }
                },
                Files = new[]
                {
                    new TemplateFileDto{
                        Id = fieldId,
                        TemplateId = templateId,
                        DisplayOrder = 1,
                        FileName = "Quote001.docx",
                    }
                },
            };
        }
    }
}
