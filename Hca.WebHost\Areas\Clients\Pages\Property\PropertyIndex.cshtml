﻿@page "/properties/{propertyId:guid}/{handler?}"
@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel
@using Hca.WebHost.Areas.Clients.Pages.Property
@using Hca.WebHost.TagHelpers.NavMenu
@using Hca.Lib.Features.Clients
@inject Hca.Lib.Services.PropertyCountsService PropertyCountsService
@inject Hca.WebHost.Pipeline.ViewManager ViewManager
@{
    var counts = await PropertyCountsService.GetPropertyChildCountsAsync(Model.Client.Id, Model.Property.Id);
}
@{
    ViewData["Title"] = Model.Client.ClientName + " " + Model.Property.PropertyCode;
}

@functions {
    string ActiveSection(PropertyPageSection section)
    {
        return section == Model.Section ? "active" : @"";
    }
}

<div class="row">
    <div class="col-xl-3 col-lg-4">

        <!-- START menu-->
        <div class="mb-boxes collapse show">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            @if (Model.Property.Deleted.HasValue)
                            {
                                <img src="~/images/Deleted.png" style="float: right;" />
                            }
                            else if (Model.Property.Archived.HasValue)
                            {
                                <img src="~/images/Archived.png" style="float: right;" />
                            }

                            <small class="text-muted">DETAILS</small>
                        </li>
                        <li class="nav-item @ActiveSection(PropertyPageSection.AddressHome)">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.ClientProperty(Model.Client.Id, Model.Property.Id)">
                                <em class="fa-fw fa-lg fa fa-building mr-2"></em>
                                <span>Property</span>
                            </a>
                        </li>
                        <li class="nav-item nav-count @ActiveSection(PropertyPageSection.Documents)">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.ClientPropertyDocuments(Model.Client.Id, Model.Property.Id)">
                                <em class="fa-fw fa-lg fa fa-file mr-2"></em>
                                <span>Documents</span>
                            </a>
                            <span class="menu-item-count">@counts.DocumentCount</span>
                        </li>
                        <li class="nav-item nav-count @ActiveSection(PropertyPageSection.FloorPlans)">
                            <a class="nav-link d-flex align-items-center"
                               href="@Urls.FloorPlans(Model.Client.Id, Model.Property.Id)">
                                <em class="fa-fw fa-lg fa fa-map mr-2"></em>
                                <span>Floor Plans</span>
                            </a>
                            <span class="menu-item-count">@counts.FloorPlanCount</span>
                        </li>
                    </ul>
                    @if (Model.Property.ArchiveReason.IsPopulated())
                    {
                        <h5 style="font-weight: normal; margin-top: 10px;">
                            <strong>Archive Reason: </strong>
                            @Model.Property.ArchiveReason
                        </h5>
                    }
                </div>
            </div>
        </div>

        <div class="collapse show">
            <div class="card card-default">
                <div class="card-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item p-2">
                            <small class="text-muted">ACTIONS</small>
                        </li>

                        @if (Model.Section == PropertyPageSection.DocumentEdit && Model.Document.QrDocumentUrl.IsNullOrEmpty())
                        {
                            <nav-link-confirm action="@Url.PageLink(pageHandler: "CreateQrCode", values: new { documentId = Model.Document.Id })"
                                              text="Create QR Code"
                                              buttonType="Edit"
                                              message="This will create a QR Code for this document, do you wish to proceed"></nav-link-confirm>
                        }

                        @if (Model.Property.Deleted.HasValue)
                        {
                            <nav-link text="Undelete Property" action="@Urls.ClientPropertyUndelete(Model.Client.Id, Model.Property.Id)"></nav-link>
                        }
                        else if (Model.Property.Archived.HasValue)
                        {
                            <nav-link text="Reinstate Property" action="@Urls.ClientPropertyReinstate(Model.Client.Id, Model.Property.Id)"></nav-link>

                            <nav-delete action="@Urls.ClientPropertyDelete(Model.Client.Id, Model.Property.Id)"
                                        text="Delete Property"
                                        item-name="property"></nav-delete>
                        }
                        else
                        {

                            <nav-link text="Upload a Document"
                                      action="@Urls.ClientPropertyDocumentUpload(Model.Client.Id, Model.Property.Id)"
                                      type="@NavButtonType.Action"></nav-link>

                            <nav-link text="Upload a Floor Plan"
                                      action="@Urls.FloorPlanUpload(Model.Client.Id, Model.Property.Id)"
                                      type="@NavButtonType.Action"></nav-link>

                            @if (Model.Section == PropertyPageSection.DocumentEdit)
                            {
                                <nav-delete action="@Urls.ClientPropertyDocumentDelete(Model.Client.Id, Model.Property.Id, Model.Document.Id)"
                                            text="Delete Document"
                                            item-name="document"></nav-delete>
                            }
                            else
                            {
                                <nav-link text="Edit Property Details"
                                          action="@Urls.ClientProperty(Model.Client.Id, Model.Property.Id).AddEditMode()"></nav-link>

                                <nav-link text="Edit Property Photo"
                                          action="@Urls.ClientPropertyAddPhoto(Model.Client.Id, Model.Property.Id).AddEditMode()"></nav-link>

                                <nav-archive action="@Urls.ClientPropertyArchive(Model.Client.Id, Model.Property.Id)"
                                             text="Archive Property"
                                             item-name="property"></nav-archive>
                            }
                        }

                    </ul>
                </div>
            </div>
        </div>
        <!-- END menu-->

        <partial name="_PropertyQrCodePartial" model="@(new PropertyQrCodeModel(null, null) { Property = Model.Property })" />
    </div>

    <div class="col-xl-9 col-lg-8">

        @switch (Model.Section)
        {
            case PropertyPageSection.AddressHome:
                <partial name="_PropertyAddressPartial" model="Model" />
                break;
            case PropertyPageSection.AddPhoto:
                <partial name="_PropertyAddPhotoPartial" model="Model" />
                break;
            case PropertyPageSection.UploadDocument:
                <partial name="_PropertyUploadDocumentPartial" model="Model" />
                break;
            case PropertyPageSection.Documents:
                <partial name="_PropertyDocumentsPartial" model="Model" />
                break;
            case PropertyPageSection.DocumentEdit:
                <partial name="_PropertyEditDocumentPartial" model="Model" />
                break;
            case PropertyPageSection.UploadFloorPlan:
                <partial name="_FloorPlanUploadPartial" model="Model" />
                break;
            case PropertyPageSection.FloorPlans:
                <partial name="_FloorPlansPartial" model="Model" />
                break;
            case PropertyPageSection.FloorPlanEdit:
                <partial name="_FloorPlanEditPartial" model="Model" />
                break;
        }

    </div>
</div>

@section Styles {
    <link href="~/vendor/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet" />

    <style>
        /* Custom layout for content that needs to fill height with proper padding */
        .layout-content-fill .wrapper .section-container {
            display: flex;
            flex-direction: column;
            min-height: calc(100vh - 55px - 60px); /* viewport - navbar - footer */
            margin-bottom: 0 !important; /* Override the default footer margin */
        }

        .layout-content-fill .wrapper .section-container .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            /* Keep the normal padding for readability */
        }

        /* Make the container-fluid fill available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Ensure the main content area fills available space */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row {
            flex: 1;
            display: flex;
        }

        /* Make the columns fill the available height */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col-xl-3,
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col-lg-4 {
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col-xl-9,
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row > .col-lg-8 {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Make the cards fill the available height */
        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row .card {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .layout-content-fill .wrapper .section-container .content-wrapper .container-fluid > .row .card > .card-body {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
    </style>
}

@section scripts {
    <script src="~/vendor/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
    <script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>
    <partial name="_MapScripts" model="new _MapScriptsModel(Model.Property, Model.Address)" />
    <script>

        $(() => {
            $('#documentDatePicker').datepicker({
                format: 'dd/mm/yyyy',
                orientation: 'bottom',
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-crosshairs',
                    clear: 'fa fa-trash'
                }
            });
            $('#documentDatePicker').datepicker('setDate', @Html.Raw(Model.Document == null ? "null" : $"'{Model.Document?.DocumentDate.ToString("dd/MM/yyyy")}'"));

            $('#nextInspectionDatePicker').datepicker({
                format: 'dd/mm/yyyy',
                orientation: 'bottom',
                icons: {
                    time: 'fa fa-clock-o',
                    date: 'fa fa-calendar',
                    up: 'fa fa-chevron-up',
                    down: 'fa fa-chevron-down',
                    previous: 'fa fa-chevron-left',
                    next: 'fa fa-chevron-right',
                    today: 'fa fa-crosshairs',
                    clear: 'fa fa-trash'
                }
            });
            $('#nextInspectionDatePicker').datepicker('setDate', @Html.Raw((Model.Document != null && Model.Document.NextInspectionDate.HasValue) ? $"'{Model.Document?.NextInspectionDate.Value.ToString("dd/MM/yyyy")}'" : "null"));

            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
        @if (Model.Site != null)
        {
            <text>{ url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                    { url: '@Urls.ClientSite(Model.Client.Id, Model.Site.Id)', text: '@Model.Site.GetDisplayText()' }, </text>
        }
        else
        {
            <text>{ url: '@Urls.ClientProperties(Model.Client.Id)', text: 'Properties' }, </text>
        }
                { text: '@Model.Property.GetDisplayText(Model.Address)' }
            ]);
        });

    </script>
}