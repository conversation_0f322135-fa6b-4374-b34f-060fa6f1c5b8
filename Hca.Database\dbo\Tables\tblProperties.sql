﻿CREATE TABLE [dbo].[tblProperties] (
    [Id]                 UNIQUEIDENTIFIER CONSTRAINT [DF_Properties_Id] DEFAULT (newid()) NOT NULL,
    [Created]            DATETIME         CONSTRAINT [DF_Properties_Created] DEFAULT (getdate()) NOT NULL,
    [ClientId]           UNIQUEIDENTIFIER NOT NULL,
    [AddressId]          UNIQUEIDENTIFIER NULL,
    [SiteId]             UNIQUEIDENTIFIER NULL,
    [PropertyCode]       NVARCHAR (MAX)   NULL,
    [Notes]              NVARCHAR (MAX)   NULL,
    [ImageContainerName] NVARCHAR (MAX)   NULL,
    [ImageBlobName]      NVARCHAR (MAX)   NULL,
    [GroupName]          NVARCHAR (MAX)   NULL,
    [BuildingName]       NVARCHAR (MAX)   NULL,
    [BuildingNumber]     NVARCHAR (MAX)   NULL,
    [Unit]               NVARCHAR (MAX)   NULL,
    [Floor]              NVARCHAR (MAX)   NULL,
    [Custom]             NVARCHAR (MAX)   NULL,
    [IsManaged] BIT NOT NULL DEFAULT ((1)),
    [Archived] DATETIME NULL,
    [Deleted] DATETIME NULL,
    [ArchiveReason] NVARCHAR(MAX) NULL, 
    [QrCodeId] NVARCHAR(50) NULL, 
    [QrCodeDocumentId] NVARCHAR(50) NULL, 
    CONSTRAINT [PK_Properties] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Properties_Addresses] FOREIGN KEY ([AddressId]) REFERENCES [dbo].[tblAddresses] ([Id]),
    CONSTRAINT [FK_Properties_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Properties_Sites] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[tblSites] ([Id])
);

