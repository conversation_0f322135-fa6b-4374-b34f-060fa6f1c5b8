﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UndeleteProperty : ICommand
    {
        public UndeleteProperty(Guid clientId, Guid propertyId)
        {
            ClientId = clientId;
            PropertyId = propertyId;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
    }

    public class UndeletePropertyHandler : DapperRequestHandler<UndeleteProperty, CommandResult>
    {
        private readonly SiteCountsService _siteCountsService;
        private readonly ClientCountsService _clientCountsService;

        public UndeletePropertyHandler(IDb<PERSON>elper dbHelper, SiteCountsService siteCountsService, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _siteCountsService = siteCountsService;
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UndeleteProperty request)
        {
            var property = await db.GetAsync<PropertyDto>(request.PropertyId);
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)} = NULL " +
                $"WHERE {nameof(PropertyDto.Id)}=@{nameof(request.PropertyId)}", request);

            _clientCountsService.ClearClientCountsAsync(property.ClientId);
            if (property.SiteId.HasValue)
            {
                _siteCountsService.ClearSiteCountsAsync(property.SiteId.Value);
            }

            return CommandResult.Success();
        }
    }
}
