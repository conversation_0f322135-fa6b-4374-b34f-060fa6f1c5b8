﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Services;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

[IgnoreAntiforgeryToken(Order = 1001)]
public class SampleImagesModel : HcaPageModel
{
    public SampleImagesModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {

    }

    public async Task OnGetAsync(Guid inspectionId, Guid sampleId, CancellationToken cancellationToken = default)
    {
        SampleImages = (await _mediator.Send(new GetSampleImagesBySampleId(inspectionId, sampleId), cancellationToken)).Items;
    }

    public IEnumerable<InspectionSampleImageDto> SampleImages { get; private set; }
    public Guid? Selected { get; private set; }


    [BindProperty]
    public IFormFile Upload { get; set; }
    [BindProperty]
    public Guid InspectionId { get; set; }
    [BindProperty]
    public Guid SampleId { get; set; }

    private byte[] UploadContent
    {
        get
        {
            if (Upload.Length > 0)
            {
                using var ms = new MemoryStream();
                Upload.CopyTo(ms);
                return ms.ToArray();
            }
            return null;
        }
    }

    public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        var fileContent = UploadContent;
        if (fileContent == null) return BadRequest();
        await _mediator.Send(
            new UploadSampleImage(
                InspectionId,
                SampleId,
                Upload.FileName,
                fileContent),
            cancellationToken);
        return new OkResult();
    }
}
