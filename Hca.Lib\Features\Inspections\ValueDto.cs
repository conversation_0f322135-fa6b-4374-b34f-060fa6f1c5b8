﻿using System;
using Dapper;

namespace Hca.Lib.Features.Inspections
{
    [Table("tblValueLists")]
    public class ValueDto
    {
        public Guid Id { get; set; }

        public ValueListType ValueListType { get; set; }

        public DateTime Created { get; set; } = DateTime.UtcNow;

        public string DisplayText { get; set; }

        public string HintText { get; set; }

        public string Value { get; set; }

        public int? Priority { get; set; }
    }

    public enum ValueListType
    {
        Floors = 1,
        Material = 2,
        Quantity = 3,
        Recommendation = 4,
        Priority = 5,
        FloorPlanColours = 6,
        InspectionType = 10,
    }
}
