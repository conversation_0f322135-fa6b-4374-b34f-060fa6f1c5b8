﻿@page "/clients/{clientId}"
@model Hca.WebHost.Areas.Clients.Pages.ClientModel
@{
    Layout = "_LayoutClient";
    ViewData["Title"] = Model.Client.ClientName;
    ViewData["Client"] = Model.Client;

    var editMode = Request.Query[Constants.QuerystringModeKey] == Constants.EditMode;
}

<form method="post" enctype="multipart/form-data">
    <div class="row">
        <div class="col">
            <div class="card card-default">
                <div class="card-body text-center" id="divClientBanner">
                    @if (Model.Client.ClientType == Lib.Features.Clients.ClientType.Commercial)
                    {
                        <div class="py-4">
                            @if (string.IsNullOrWhiteSpace(Model.Client.LogoUrl))
                            {
                                <p>Logo not set</p>
                            }
                            else
                            {
                                <modalImage imageUrl="@Model.Client.LogoUrl" altText="@Model.Client.ClientName Logo"></modalImage>
                            }
                        </div>
                    }
                    @if (editMode)
                    {
                        <input asp-for="Client.ClientName" row-label="Client Name" />
                        <input asp-for="Client.UrlSafeClientName" row-label="URL Name" />
                        <input asp-for="Client.Id" type="hidden" />
                    }
                    else
                    {
                        <h3 class="m-0 text-bold my-3">
                            @Model.Client.ClientName
                        </h3>
                    }
                    @if (Model.Client.ClientType == Lib.Features.Clients.ClientType.Domestic)
                    {
                        <small>(Domestic)</small>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row" id="divClientDetails">
        <div class="col col-xl-6 col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card card-default">
                <div class="card-header d-flex align-items-center">
                    <div class="d-flex justify-content-center col">
                        <div class="h4 m-0 text-center">
                            @(Model.Client.ClientType == Lib.Features.Clients.ClientType.Commercial ? "Head Office" : "Address")
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row py-4 justify-content-center">
                        <div class="col-12 col-sm-10">
                            <input type="text" asp-for="Client.BuildingNumber" row-label="Building Number" />
                            <input type="text" asp-for="Client.BuildingName" row-label="Building Name" />
                            <input type="text" asp-for="Client.Unit" row-label="Unit (type a number/letter only)" />
                            <input type="text" asp-for="Client.Floor" row-label="Floor" />
                            <partial name="_AddressFieldsPartial"
                                     for="Address"
                                     view-data='new ViewDataDictionary(ViewData) { { "showMap", false } }' />
                        </div>
                    </div>
                </div>
                <save-cancel-footer></save-cancel-footer>
            </div>
        </div>

        <div class="col col-xl-6 col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="card card-default">
                <div class="card-header">
                    Client Requirements
                </div>
                <div class="card-body">
                    <div class="row py-4 justify-content-center">
                        <div class="col-12 col-sm-10">
                            <textarea asp-for="Client.EmergencyContactDetails" row-label="Emergency Contact Details"></textarea>
                            <textarea asp-for="Client.EscalationProcedure" row-label="Escalation Procedure"></textarea>
                            <textarea asp-for="Client.KpisAndSlas" row-label="KPIs/SLAs"></textarea>
                            <input asp-for="Client.InvoiceEmailAddress" row-label="Invoice Email Address" />
                            <input asp-for="Client.AccountQueriesEmailAddress" row-label="Account Queries Email Address" />
                            <textarea asp-for="Client.GeneralRequirements" row-label="General Requirements"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</form>



@section scripts{
    <script>

        $(() => {
            drawBreadcrumb([ { text: '@Model.Client.ClientName' }]);
        });

    </script>
}
