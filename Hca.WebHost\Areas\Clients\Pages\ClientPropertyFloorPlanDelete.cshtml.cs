﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientPropertyFloorPlanDeleteModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientPropertyFloorPlanDeleteModel(
        ClientService clientService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    public string ErrorMessage { get; private set; }

    public async Task<IActionResult> OnPostAsync(
        Guid propertyId,
        string id,
        CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewProperty(Guid.Parse(id), ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        var deleteResult = await _clientService.DeleteFloorPlanAsync(Guid.Parse(id), cancellationToken);
        if (deleteResult.IsSuccess)
        {
            return Redirect(Urls.FloorPlans(Guid.Empty, propertyId));
        }

        ErrorMessage = deleteResult.Reason;

        return Page();
    }
}
