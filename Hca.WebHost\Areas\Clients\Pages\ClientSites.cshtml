﻿@page "/clients/{clientId:guid}/sites"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitesModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="content-area">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/sites"
                  hx-target="#divSiteSearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, click from:#btnSearchSites, change from:#ShowArchivedSites">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search sites" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary"
                                type="button"
                                id="btnSearchSites">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            <input asp-for="ShowArchivedSites" class="ml-4 mx-2" />Show Archived Sites
                        </div>
                    </div>

                </div>
                @Html.AntiForgeryToken()
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card card-default">
                <div class="card-header">
                    <small class="text-muted">SITES</small>
                </div>
                <div class="card-body" id="divSiteSearchResults">
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        /* Dynamic viewport height layout that respects footer */
        .content-area {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 195px); /* Navbar + content padding + footer space */
            min-height: 400px; /* Minimum sensible height */
        }

        .content-area > .row:first-child {
            flex-shrink: 0; /* Search form doesn't shrink */
        }

        .content-area > .row:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .content-area > .row:last-child > .col {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-area > .row:last-child > .col > .card {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body {
            flex: 1;
            overflow: hidden;
            min-height: 0;
            padding: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body > .row {
            height: 100%;
            margin: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6 {
            padding: 15px;
        }

        .scrollable-list-container {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 15px; /* Account for scrollbar */
        }

        /* Ensure the map column has fixed height */
        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6:last-child {
            height: 100%;
            overflow: hidden;
        }

        /* Ensure the sites list column can scroll */
        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6:first-child {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Responsive adjustments */
        @@media (max-height: 600px) {
            .content-area {
                height: calc(100vh - 120px);
                min-height: 300px;
            }
        }

        @@media (max-width: 768px) {
            .content-area {
                height: calc(100vh - 120px);
                min-height: 350px;
            }
        }
    </style>
}


@section scripts{
    <script>

        $(() => {
            drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Sites' }]);
        });

    </script>
}
