﻿@page
@model Hca.WebHost.Areas.Identity.Pages.Account.ResetPasswordModel
@{
}
@{
    ViewData["Title"] = "Reset Password";
    Layout = "_LayoutPage";
}

<div class="block-center mt-4 wd-xl">
    <div class="card card-flat">
        <div class="card-header text-center">
            <a href="#">
                <img class="block-center rounded" src="~/images/hca_logo.png" alt="Image" width="290" />
            </a>
        </div>
        <div class="card-body">
            <p class="text-center py-2">PASSWORD RESET</p>
            <form class="mb-3" id="account" method="post" novalidate="novalidate">
                <p>Enter your email address below and if it is registered on our system you will receive instructions on how to reset your password in your inbox.</p>
                <div class="form-group">
                    <div class="input-group with-focus">
                        <input class="form-control" asp-for="Password" type="password" placeholder="Enter new password" autocomplete="off" required="required" />
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group with-focus">
                        <input class="form-control" asp-for="ConfirmPassword" type="password" placeholder="Enter new password again" autocomplete="off" required="required" />
                    </div>
                </div>
                <button class="btn btn-block btn-danger mt-3" type="submit">Reset</button>
                <div asp-validation-summary="All" class="text-danger"></div>
            </form>
        </div>
    </div>
</div>
