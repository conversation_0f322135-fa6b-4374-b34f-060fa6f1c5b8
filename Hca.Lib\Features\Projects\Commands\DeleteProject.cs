﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Projects.Documents;

namespace Hca.Lib.Features.Projects.Commands
{
    public class DeleteProject : ICommand
    {
        public DeleteProject(Guid projectId)
        {
            ProjectId = projectId;
        }

        public Guid ProjectId { get; }
    }

    public class DeleteProjectHandler : <PERSON>pperRequestHandler<DeleteProject, CommandResult>
    {
        public DeleteProjectHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteProject request)
        {
            var project = await db.GetAsync<ProjectDto>(request.ProjectId);

            if (project == null) return CommandResult.Fail("Project does not exist");

            await db.DeleteAsync<InspectionSampleImageDto>(
                $"WHERE InspectionSampleId IN (" +
                $"SELECT tblInspectionSamples.Id FROM tblInspectionSamples " +
                $"JOIN tblInspections ON InspectionId = tblInspections.Id " +
                $"WHERE {nameof(InspectionDto.ProjectId)}=@{nameof(request.ProjectId)})", request);

            await db.DeleteAsync<InspectionSampleDto>(
                $"WHERE InspectionId IN (" +
                $"SELECT tblInspections.Id FROM tblInspections " +
                $"WHERE {nameof(InspectionDto.ProjectId)}=@{nameof(request.ProjectId)})", request);

            await db.DeleteAsync<InspectionReportDto>(
                $"WHERE InspectionId IN (" +
                $"SELECT tblInspections.Id FROM tblInspections " +
                $"WHERE {nameof(InspectionDto.ProjectId)}=@{nameof(request.ProjectId)})", request);

            await db.DeleteAsync<InspectionDto>(
                $"WHERE {nameof(InspectionDto.ProjectId)}=@{nameof(request.ProjectId)}", request);

            if (project.QuoteId.HasValue)
            {
                await db.DeleteAsync<QuoteDocumentDto>("WHERE QuoteId=@QuoteId", project); // todo: change to a job or even make conditional/break relationship?
                await db.DeleteAsync<QuotePropertyDto>("WHERE QuoteId=@QuoteId", project);
                await db.DeleteAsync<QuoteDto>(project.QuoteId.Value);
            }

            await db.DeleteAsync<ProjectDto>(project.Id);

            return CommandResult.Success();
        }
    }
}
