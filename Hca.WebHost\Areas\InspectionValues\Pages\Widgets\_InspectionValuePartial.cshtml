﻿@model Hca.WebHost.Areas.InspectionValues.Models.ValueModel

@using Hca.Lib.Features.Inspections

<div class="content-heading">
    <div>
        @Model.Header
        <small>@Model.SubHeader</small>
    </div>
</div>

<div class="card card-default">
    <form method="post">
        <div class="card-body">
            <input asp-for="Value.DisplayText" row-label="@Model.ColumnNames[nameof(ValueDto.DisplayText)]" />

            @if (Model.ColumnNames.ContainsKey(nameof(ValueDto.HintText)))
            {
                <input type="text" asp-for="Value.HintText" row-label="@Model.ColumnNames[nameof(ValueDto.HintText)]" />
            }

            @if (Model.ColumnNames.ContainsKey(nameof(ValueDto.Value)))
            {
                switch (Model.InspectionValueType)
                {
                    case Hca.WebHost.Areas.InspectionValues.Pages.Widgets.InspectionValueType.Colour:
                        <div class="form-group row">
                            <label class="col-xl-2 col-form-label" for="colourPickerValue">@Model.ColumnNames[nameof(ValueDto.Value)]</label>
                            <div id="colourPicker" class="col-xl-10 input-group">
                                <input type="text" id="colourPickerValue"
                                       class="form-control"
                                       name="Value.@nameof(ValueDto.Value)"
                                       value="@Model.Value.Value" />
                                <span class="input-group-append">
                                    <span class="input-group-text colorpicker-input-addon"><i></i></span>
                                </span>
                            </div>
                        </div>
                        break;

                    case InspectionValues.Pages.Widgets.InspectionValueType.Boolean:
                        <div class="form-group row">
                            <div class="checkbox c-checkbox">
                                <label>
                                    <input type="checkbox"
                                           id="chkValue"
                                           name="Value.Value"
                                           asp-for="Value.Value" />
                                    <span class="fa fa-check"></span> @Model.ColumnNames[nameof(ValueDto.Value)]
                                </label>
                            </div>
                        </div>
                        break;

                    default: throw new Exception("Must provide a type for inspection values");
                }
            }
        </div>
        <div class="card-footer">
            <input class="btn btn-success" type="submit" value="Save" />
        </div>
    </form>
</div>
