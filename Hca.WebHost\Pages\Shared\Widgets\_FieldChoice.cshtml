﻿@model Hca.Lib.Features.Documents.Fields.ChoiceField

@if (Model.MaxChoices > 0)
{
    <p>Choose a maximum of @Model.MaxChoices from this list</p>
}
else
{
    <p>Choose any from this list</p>
}

@for (var i = 0; i < Model.Choices.Count(); i++)
{
    var choice = Model.Choices[i];
    <p class="form-check">
        <input type="hidden" asp-for="Choices[i].Id" />
        <input class="form-check-input" type="checkbox" asp-for="Choices[i].Chosen" />
        <span class="form-check-label">
            @if (!string.IsNullOrWhiteSpace(choice.Hint))
            {
                <span><strong>HINT</strong>&nbsp;&nbsp;@choice.Hint</span><br />
            }
            <span>@choice.Text</span>
        </span>
    </p>
}
