﻿CREATE TABLE [dbo].[tbl<PERSON><PERSON>orPlans] (
    [Id]            UNIQUEIDENTIFIER CONSTRAINT [DF_FloorPlans_Id] DEFAULT (newid()) NOT NULL,
    [Created]       DATETIME         CONSTRAINT [DF_FloorPlans_Created] DEFAULT (getdate()) NOT NULL,
    [PropertyId]    UNIQUEIDENTIFIER NOT NULL,
    [ContainerName] NVARCHAR (MAX)   NOT NULL,
    [BlobName]      NVARCHAR (MAX)   NOT NULL,
    [ThumbnailName] NVARCHAR (MAX)   NULL,
    [FloorId]       UNIQUEIDENTIFIER NOT NULL,
    [Notes]         NVARCHAR (MAX)   NULL,
    CONSTRAINT [PK_FloorPlans] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_FloorPlans_FloorId] FOREIGN KEY ([FloorId]) REFERENCES [dbo].[tblValueLists] ([Id]),
    CONSTRAINT [FK_FloorPlans_Properties] FOREIGN KEY ([PropertyId]) REFERENCES [dbo].[tblProperties] ([Id])
);

