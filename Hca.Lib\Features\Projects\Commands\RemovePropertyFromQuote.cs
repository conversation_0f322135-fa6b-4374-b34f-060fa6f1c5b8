﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Projects.Commands
{
    public class RemovePropertyFromQuote : ICommand
    {
        public RemovePropertyFromQuote(Guid quotePropertyId)
        {
            QuotePropertyId = quotePropertyId;
        }

        public Guid QuotePropertyId { get; }
    }

    public class RemovePropertyFromQuoteHandler : DapperRequestHandler<RemovePropertyFromQuote, CommandResult>
    {
        public RemovePropertyFromQuoteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDb<PERSON>elper db, RemovePropertyFromQuote request)
        {
            await db.DeleteAsync<QuotePropertyDto>(request.QuotePropertyId);

            return CommandResult.Success();
        }
    }
}
