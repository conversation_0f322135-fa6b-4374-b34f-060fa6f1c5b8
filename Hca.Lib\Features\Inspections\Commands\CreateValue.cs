﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class CreateValue : ICommand
    {
        public CreateValue(
            ValueListType type,
            string displayText,
            string value = null,
            string hintText = null) : this(new ValueDto
            {
                Id = Guid.NewGuid(),
                ValueListType = type,
                DisplayText = displayText,
                Value = value,
                HintText = hintText,
            })
        { }

        public CreateValue(ValueDto dto)
        {
            Dto = dto;
        }

        public ValueDto Dto { get; }
    }

    public class CreateValueHandler : DapperRequestHandler<CreateValue, CommandResult>
    {
        public CreateValueHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateValue request)
        {
            await db.InsertAsync(request.Dto);
            return CommandResult.Success();
        }
    }
}
