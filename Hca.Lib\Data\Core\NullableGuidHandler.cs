﻿using Dapper;
using System;
using System.Data;

namespace Hca.Lib.Data.Core;

public class NullableGuidHandler : SqlMapper.TypeHandler<Guid?>
{
    public override void SetValue(IDbDataParameter parameter, Guid? value)
    {
        parameter.Value = value.HasValue ? (object)value.Value : DBNull.Value;
    }

    public override Guid? Parse(object value)
    {
        return value == null || value == DBNull.Value ? (Guid?)null : (Guid)value;
    }
}