﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using Dapper;
    using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

    [Table(TableNames.Addresses)]
    public class AddressDto
    {
        public Guid Id { get; set; }
        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string StreetName { get; set; }
        [TownOrCityRequired]
        public string Town { get; set; }
        [TownOrCityRequired]
        public string City { get; set; }
        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string County { get; set; }
        public string Country { get; set; }
        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string Postcode { get; set; }

        public double? Lat { get; set; }
        public double? Lon { get; set; }
    }

    [AttributeUsage(AttributeTargets.Property, AllowMultiple = true)]
    public class TownOrCityRequiredAttribute : ValidationAttribute, IClientModelValidator
    {
        public void AddValidation(ClientModelValidationContext context)
        {
            context.Attributes.TryAdd("data-val", "true");
            context.Attributes.TryAdd("data-val-town-or-city", "An address requires at least one of town and city to be populated");
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (validationContext.ObjectInstance is not AddressDto dto)
            {
                return new ValidationResult("This can only validate AddressDto objects");
            }

            if (string.IsNullOrWhiteSpace($"{dto.City}{dto.Town}"))
            {
                return new ValidationResult("An address requires at least one of town and city to be populated");
            }

            return ValidationResult.Success;
        }
    }
}
