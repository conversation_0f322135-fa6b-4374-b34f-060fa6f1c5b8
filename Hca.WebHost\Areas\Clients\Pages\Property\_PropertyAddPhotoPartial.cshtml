﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">ADD PROPERTY PHOTO</small>
    </div>
    <form method="post" enctype="multipart/form-data">
        <div class="card-body">
            <div class="form-group row">
                <label class="col-xl-2 col-form-label" for="UploadImage">Choose image file</label>
                <div class="col-xl-10">
                    <input class="form-control filestyle"
                           type="file"
                           data-classbutton="btn btn-secondary"
                           data-classinput="form-control inline"
                           data-icon="&lt;span class='fa fa-upload mr'&gt;&lt;/span&gt;"
                           asp-for="UploadFile" />
                </div>
            </div>
        </div>
        <save-cancel-footer CancelUrl="@Urls.ClientProperty(Model.Client.Id, Model.Property.Id)"></save-cancel-footer>
    </form>
</div>
