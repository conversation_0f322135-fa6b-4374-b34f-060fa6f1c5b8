﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager
<li class="@Html.IsActive(area: "Clients")">
    <a href="@Urls.Clients" title="Clients">
        <em class="icon-diamond"></em>
        <span data-localize="sidebar.nav.SINGLEVIEW">Clients</span>
    </a>
</li>
<li class="@Html.IsActive(area: "Projects")">
    <a href="@Urls.Projects" title="Projects">
        <em class="far fa-thumbs-up"></em>
        <span data-localize="sidebar.nav.SINGLEVIEW">Projects</span>
    </a>
</li>
@if (await ViewManager.IsAdminUser)
{
    <li class="@Html.IsActive(area: "Templates")">
        <a href="@Urls.Templates" title="Templates">
            <em class="far fa-object-group"></em>
            <span data-localize="sidebar.nav.SINGLEVIEW">Templates</span>
        </a>
    </li>
    <li class="@Html.IsActive(area: "Users")">
        <a href="@Urls.Users" title="Users">
            <em class="far fa-user"></em>
            <span data-localize="sidebar.nav.SINGLEVIEW">Users</span>
        </a>
    </li>
    <li class="@Html.IsActive(area: "InspectionValues")">
        <a href="#inspectionValueLists" title="Value Lists" data-toggle="collapse">
            <em class="icon-map"></em>
            <span data-localize="sidebar.nav.inspectionValueLists.INSPECTIONVALUES">Value Lists</span>
        </a>
        <ul id="inspectionValueLists" class="sidebar-nav sidebar-subnav collapse">
            <li class="sidebar-subnav-header">Value Lists</li>
            <li class="@Html.IsActive("Floors", "InspectionValues")">
                <a href="@Urls.Floors" title="Floors">
                    <span data-localize="sidebar.nav.inspectionValueLists.FLOORS">Floors</span>
                </a>
            </li>
            <li class="@Html.IsActive("FloorPlanColours", "InspectionValues")">
                <a href="@Urls.FloorPlanColours" title="Floor Plan Colours">
                    <span data-localize="sidebar.nav.inspectionValueLists.FLOORPLANCOLOURS">Floor Plan Colours</span>
                </a>
            </li>
            <li class="@Html.IsActive("Material", "InspectionValues")">
                <a href="@Urls.Material" title="Material">
                    <span data-localize="sidebar.nav.inspectionValueLists.MATERIAL">Material</span>
                </a>
            </li>
            <li class="@Html.IsActive("Quantity", "InspectionValues")">
                <a href="@Urls.Quantity" title="Quantity">
                    <span data-localize="sidebar.nav.inspectionValueLists.QUANTITY">Quantity</span>
                </a>
            </li>
            <li class="@Html.IsActive("Recommendation", "InspectionValues")">
                <a href="@Urls.Recommendation" title="Recommendation">
                    <span data-localize="sidebar.nav.inspectionValueLists.RECOMMENDATION">Recommendation</span>
                </a>
            </li>
            <li class="@Html.IsActive("Priority", "InspectionValues")">
                <a href="@Urls.Priority" title="Priority">
                    <span data-localize="sidebar.nav.inspectionValueLists.PRIORITY">Priority</span>
                </a>
            </li>
        </ul>
    </li>
 }