﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Clients.Pages;

[ValidateAntiForgeryToken]
public class ClientsIndexModel : HcaPageModel
{
    public ClientsIndexModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    [BindProperty] public string SearchTerm { get; set; }
    [BindProperty] public bool ShowArchivedClients { get; set; }
    [BindProperty] public string SearchText { get; set; }
    [BindProperty] public int PageNum { get; set; } = 1;
    [BindProperty] public int PageSize { get; set; } = 50;
    public PagedDtoSet<ClientDto> SearchResults { get; private set; }
    public PagedDtoSet<ClientDto> ArchivedResults { get; private set; }

    public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
    {
        // only staff users can view clients
        if (!await IsHcaUser) return Forbid();

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        // only admin users can create clients
        if (!await IsAdminUser) return Forbid();

        if (string.IsNullOrWhiteSpace(SearchText))
        {
            SearchResults = await _mediator.Send(new GetAllClientsPagedQuery(PageNum, PageSize), cancellationToken);

            if (ShowArchivedClients)
            {
                ArchivedResults = await _mediator.Send(new GetAllClientsPagedQuery(PageNum, PageSize, true), cancellationToken);
            }
        }
        else
        {
            SearchResults = await _mediator.Send(new ClientSearchQuery(SearchText, PageNum, PageSize), cancellationToken);

            if (ShowArchivedClients)
            {
                ArchivedResults = await _mediator.Send(new ClientSearchQuery(SearchText, PageNum, PageSize, true), cancellationToken);
            }
        }

        return Partial("~/Areas/Clients/Pages/_ClientSearchResults.cshtml", this);
    }
}
