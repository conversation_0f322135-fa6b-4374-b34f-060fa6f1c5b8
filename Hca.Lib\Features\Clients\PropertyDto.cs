﻿namespace Hca.Lib.Features.Clients
{
    using System;
    using Dapper;

    [Table(TableNames.Properties)]
    public class PropertyDto
    {
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        public Guid ClientId { get; set; }

        public Guid? AddressId { get; set; }

        public Guid? SiteId { get; set; }

        public string PropertyCode { get; set; }

        public string ImageContainerName { get; set; }

        public string ImageBlobName { get; set; }

        public string Unit { get; set; }
        public string Custom { get; set; }

        public DateTime? Archived { get; set; }

        public DateTime? Deleted { get; set; }

        public string ArchiveReason { get; set; }

        public string QrCodeId { get; set; }
        public string QrCodeDocumentId { get; set; }
    }
}
