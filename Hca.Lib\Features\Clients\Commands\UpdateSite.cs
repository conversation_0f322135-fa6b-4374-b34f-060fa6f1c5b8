﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class UpdateSite : ICommand
    {
        public UpdateSite(
            Guid clientId,
            Guid siteId,
            Guid? addressId,
            string siteName)
        {
            ClientId = clientId;
            SiteId = siteId;
            AddressId = addressId;
            SiteName = siteName;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
        public Guid? AddressId { get; }
        public string SiteName { get; }
    }

    public class UpdateSiteHandler : DapperRequestHandler<UpdateSite, CommandResult>
    {
        public UpdateSiteHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateSite request)
        {
            await db.UpdateAsync(new SiteDtoExtended
            {
                ClientId = request.ClientId,
                AddressId = request.AddressId,
                Id = request.SiteId,
                SiteName = request.SiteName
            });

            return CommandResult.Success();
        }
    }
}
