﻿using System;
using System.Collections.Generic;
using System.Linq;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Queries
{
    public static class TemplateUtils
    {

        public static IEnumerable<TemplateSection> BuildTemplateSections(
            IEnumerable<TemplateSectionDto> templateSectionDtos,
            IEnumerable<TemplateFieldDto> templateFieldDtos) =>
            templateSectionDtos.Select(s => BuildTemplateSection(s, templateFieldDtos));

        public static TemplateSection BuildTemplateSection(TemplateSectionDto s, IEnumerable<TemplateFieldDto> templateFieldDtos)
        {
            return new TemplateSection
            {
                TemplateSectionId = s.Id,
                SectionCode = s.SectionCode,
                SectionOrder = s.SectionOrder,
                SectionTitle = s.SectionTitle,
                Fields = templateFieldDtos
                    .Where(f => f.TemplateSectionId == s.Id)
                    .Select(f => BuildTemplateField(f)),
            };
        }

        public static ITemplateField<Field> BuildTemplateField(TemplateFieldDto field)
        {
            return field.FieldType switch
            {
                FieldType.Text => TemplateTextField.Rehydrate(field.Id, field.FieldOrder, field.Optional, field.FieldContent),
                FieldType.Choice => TemplateChoiceField.Rehydrate(field.Id, field.FieldOrder, field.Optional, field.FieldContent),
                _ => throw new NotImplementedException(),
            };
        }
    }
}
