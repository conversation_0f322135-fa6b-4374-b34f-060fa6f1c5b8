﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Config.Queries
{
    public class GetConfig : IQuery<ConfigDto>
    {
        public GetConfig(string configName)
        {
            ConfigName = configName;
        }

        public string ConfigName { get; }
    }

    public class GetConfigHandler : DapperRequestHandler<GetConfig, ConfigDto>
    {
        public GetConfigHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<ConfigDto> OnHandleAsync(IDbHelper db, GetConfig request) =>
            db.GetAsync<ConfigDto>(request.ConfigName);
    }
}
