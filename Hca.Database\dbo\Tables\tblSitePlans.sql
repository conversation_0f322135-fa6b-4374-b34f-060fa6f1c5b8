﻿CREATE TABLE [dbo].[tblSitePlans] (
    [Id]            UNIQUEIDENTIFIER CONSTRAINT [DF_SitePlans_Id] DEFAULT (newid()) NOT NULL,
    [Created]       DATETIME         CONSTRAINT [DF_SitePlans_Created] DEFAULT (getdate()) NOT NULL,
    [SiteId]        UNIQUEIDENTIFIER NOT NULL,
    [ContainerName] NVARCHAR (MAX)   NOT NULL,
    [BlobName]      NVARCHAR (MAX)   NOT NULL,
    [ThumbnailName] NVARCHAR (MAX)   NULL,
    [Notes]         NVARCHAR (MAX)   NULL,
    CONSTRAINT [PK_SitePlans] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_SitePlans_Sites] FOREIGN KEY ([SiteId]) REFERENCES [dbo].[tblSites] ([Id])
);

