﻿using System;
using Dapper;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Templates;
using Newtonsoft.Json.Linq;

namespace Hca.Lib.Features.Documents
{
    [Table("tblDocumentFields")]
    public class DocumentFieldDto
    {
        public Guid Id { get; set; }

        public Guid DocumentSectionId { get; set; }

        public FieldType FieldType { get; set; }

        public string FieldContent { get; set; }
        //protected JObject _fieldContent = new();
        //public string FieldContent { get => _fieldContent.ToString(); set => _fieldContent = JObject.Parse(value); }

        public int FieldOrder { get; set; }

        public bool IsOptional { get; set; }

        public bool IsComplete { get; set; }

        [IgnoreSelect]
        [IgnoreInsert]
        [IgnoreUpdate]
        public Field Specialised
        {
            get => FieldType switch
            {
                FieldType.Choice => this.Convert<ChoiceField>(),
                FieldType.Text => this.Convert<TextField>(),
                _ => throw new NotImplementedException(),
            };
        }

        //[IgnoreSelect]
        //[IgnoreInsert]
        //[IgnoreUpdate]
        //public string SpecialisedType
        //{
        //    get => this.GetType().FullName;
        //}
    }
}
