﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class EditSitePlan : ICommand
    {
        public EditSitePlan(
            Guid planId,
            string notes)
        {
            PlanId = planId;
            Notes = notes;
        }

        public Guid PlanId { get; }
        public string Notes { get; }
    }

    public class EditSitePlanHandler : DapperRequestHandler<EditSitePlan, CommandResult>
    {
        public EditSitePlanHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EditSitePlan request)
        {
            var dto = await db.GetAsync<SitePlanDto>(request.PlanId);
            dto.Notes = request.Notes;

            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}

