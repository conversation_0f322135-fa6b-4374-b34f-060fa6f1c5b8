﻿using System.Collections.Generic;
using System.Linq;
using Hca.Lib.Features.Inspections.Queries.Models;

namespace Hca.Lib.Features.Inspections
{
    public static class InspectionQueryExtensions
    {
        public static string GetDisplayText(this InspectionQueryModel inspection) 
        {
            var resultParts = new List<string>();

            if (!string.IsNullOrWhiteSpace(inspection.PropertyCode)) resultParts.Add(inspection.PropertyCode);
            AddAddress(inspection, resultParts);

            return string.Join(", ", resultParts.Where(p => !string.IsNullOrWhiteSpace(p)).Select(p => p.Trim()));
        }

        private static void AddAddress(InspectionQueryModel inspection, List<string> resultParts)
        {
            if (!string.IsNullOrWhiteSpace(inspection.Floor)) resultParts.Add($"Floor {inspection.Floor}");
            if (!string.IsNullOrWhiteSpace(inspection.Unit)) resultParts.Add($"Unit {inspection.Unit}");
            if (!string.IsNullOrWhiteSpace(inspection.BuildingName)) resultParts.Add(inspection.BuildingName);
            resultParts.Add($"{inspection.BuildingNumber} {inspection.StreetName}");
            resultParts.Add($"{inspection.Town}");
            resultParts.Add($"{inspection.County}");
            resultParts.Add($"{inspection.Postcode}");
            if (!string.IsNullOrWhiteSpace(inspection.Country)) resultParts.Add($"{inspection.Country}");
        }
    }
}
