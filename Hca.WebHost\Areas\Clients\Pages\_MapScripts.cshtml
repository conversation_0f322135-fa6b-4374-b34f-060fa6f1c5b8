﻿@model Hca.WebHost.Areas.Clients.Pages._MapScriptsModel
@{
    var fillColour = !string.IsNullOrWhiteSpace(Model.PointerColour)
        ? Model.PointerColour
        : Model.NextInspection switch
        {
            null => "#768294",
            DateTime dt when dt < DateTime.Now => "#f05050",
            DateTime dt when dt < DateTime.Now.AddMonths(1) => "#ff902b",
            _ => "#27c24c",
        };

    var label = Model.ShowMapNumbers ? Model.PropertyCount.ToString() : " ";

    var shape = Model.ShowMapNumbers ? "calloutSVGFilled" : "pinSVGFilled";
}
<script>
    var map;
    var marker;
    var calloutSVGFilled = "M25 1 h-25 v20 h7.547 l5.439 8.05 5.44 -8.05 h7 z";
    var pinSVGFilled = "M 12,2 C 8.1340068,2 5,5.1340068 5,9 c 0,5.25 7,13 7,13 0,0 7,-7.75 7,-13 0,-3.8659932 -3.134007,-7 -7,-7 z";

    function placeMarker(location) {
        if (!map) return;

        if (marker) {
            marker.setPosition(location);
        } else {
            marker = new google.maps.Marker({
                position: location,
                map: map,
                icon: {
                    path: @shape,
                    fillOpacity: 1,
                    fillColor: '@fillColour',
                    labelOrigin: new google.maps.Point(12, 11),
                },
                label: {
                    text: '@label',
                    fontWeight: 'bold',
                },
            });
        }

        map.setCenter(location);
        $('#Address_Lat').val(location.lat);
        $('#Address_Lon').val(location.lng);
    }

    findCoordinates = ({ success, fail }) => {

        var geocoder = new google.maps.Geocoder();

        var addressParts = [
            $("#Property_BuildingNumber").val(),
            $("#Property_BuildingName").val(),
            $("#Address_StreetName").val(),
            $("#Address_Town").val(),
            $("#Address_City").val(),
            $("#Address_County").val(),
            $("#Address_Country").val(),
            $("#Address_Postcode").val(),
        ].filter(Boolean).join(", ");

        geocoder.geocode({
            'address': addressParts,
        },
            function (results, status) {
                if (status == google.maps.GeocoderStatus.OK) {
                    placeMarker(results[0].geometry.location);
                    typeof success === 'function' && success(results[0].geometry.location);
                } else {
                    console.log("Geocode was not successful for the following reason: " + status);
                    typeof fail === 'function' && fail();
                }
            });
    }

    function initMap() {
        var geocoder = new google.maps.Geocoder();
        var mapElement = document.getElementById("map");

        if (!mapElement) return;

        map = new google.maps.Map(mapElement,
            {
                gestureHandling: 'greedy',
    @if (Model != null && Model.Lat.HasValue && Model.Lon.HasValue)
            {
                <text>
                    zoom: 8,
                    center: new google.maps.LatLng(@(Model.Lat.Value), @(Model.Lon.Value)),
                </text>
    }
            else
            {
                <text>
                    zoom: 6,
                    center: new google.maps.LatLng(52.736129, -1.988229),
                </text>
    }
            mapTypeId: google.maps.MapTypeId.ROADMAP
                        });

    @if (Model.Lat.HasValue && Model.Lon.HasValue)
        {
            <text>
                marker = new google.maps.Marker({
                    position: new google.maps.LatLng(@(Model.Lat), @(Model.Lon)),
                    map: map,
                    icon: {
                        path: @shape,
                        fillOpacity: 1,
                        fillColor: '@fillColour',
                        labelOrigin: new google.maps.Point(12, 11),
                    },
                    label: {
                        text: '@label',
                        fontWeight: 'bold',
                    },
                });
            </text>
    }

    @*@if (Context.IsEditMode() || Context.IsNewMode())
        {
        <text>
        google.maps.event.addListener(map, 'click', function(event) {
        placeMarker(event.latLng);
        });
        </text>
        }

        $('#btnSetMapFromAddress').click((e) => {
        e.preventDefault();
        findCoordinates();
        });*@
                }
</script>

<script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCurPCz8HEXfT3pkLqpBprIKfImFtkE3DQ&callback=initMap&v=weekly"
    defer></script>