﻿using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Clients.Queries.Models;
using LazyCache;
using MediatR;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Services;

public class ClientCountsService
{
    private readonly IMediator _mediator;
    private readonly IAppCache _appCache;

    public ClientCountsService(IAppCache appCache, IMediator mediator)
    {
        _appCache = appCache ?? throw new ArgumentNullException(nameof(appCache));
        _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    }

    public async Task<ClientCountsModel> GetClientCountsAsync(Guid clientId)
    {
        return await _appCache.GetOrAddAsync($"ClientCounts_{clientId}", async () =>
        {
            var clientCounts = await _mediator.Send(new GetClientCounts(clientId));
            return clientCounts;
        }, DateTimeOffset.UtcNow.AddMinutes(10));
    }

    public void ClearClientCountsAsync(Guid clientId)
    {
        _appCache.Remove($"ClientCounts_{clientId}");
    }
}
