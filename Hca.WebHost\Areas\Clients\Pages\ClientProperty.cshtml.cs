﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.Lib.Features.Inspections;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientPropertyModel : HcaPageModel
{
    private readonly ClientService _clientService;
    private readonly ValueListsService _valueListsService;

    public ClientPropertyModel(
        ClientService clientService, 
        ValueListsService valueListsService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
        _valueListsService = valueListsService;
    }

    [BindProperty]
    [Required]
    public PropertyDtoExtended Property { get; set; }
    [BindProperty]
    public AddressDto Address { get; set; }
    [BindProperty]
    public bool? IsSiteProperty { get; set; }

    public IEnumerable<ValueDto> Floors { get; private set; }
    public IEnumerable<SiteDtoExtended> Sites { get; set; }
    public ClientDto Client { get; private set; }
    public string Id { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Sites = (await _clientService.GetSitesAsync(clientId, null, cancellationToken)).Items;

        Floors = await _valueListsService.GetAllAsync(ValueListType.Floors, cancellationToken);

        if (Guid.TryParse(id, out var propertyId))
        {
            if (!await IsHcaUser)
            {
                var valid = await _mediator.Send(new CanContactViewProperty(propertyId, ContactId), cancellationToken);
                if (!valid.IsValid) return Forbid();
            }

            Property = await _clientService.GetPropertyAsync(propertyId, cancellationToken);
            Address = await _clientService.GetAddressAsync(Property.AddressId.Value, cancellationToken);
        }
        else
        {
            Property = new PropertyDtoExtended();
        }

        var querySiteId = Request.Query[Urls.Query_SiteId];
        if (!string.IsNullOrWhiteSpace(querySiteId)
            && Guid.TryParse(querySiteId, out var siteId))
        {
            IsSiteProperty = true;
            Property.SiteId = siteId;
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewProperty(Guid.Parse(id), ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Sites = (await _clientService.GetSitesAsync(clientId, null, cancellationToken)).Items;

        Floors = await _valueListsService.GetAllAsync(ValueListType.Floors, cancellationToken);

        if (IsSiteProperty ?? false)
        {
            // remove address fields from validation as they won't be set for a site property
            ModelState.Remove("Address");
            foreach(var field in typeof(AddressDto).GetProperties())
            {
                ModelState.Remove(field.Name);
            }

            if (string.IsNullOrWhiteSpace(Property.Unit)
                && string.IsNullOrWhiteSpace(Property.PropertyCode)
                && string.IsNullOrWhiteSpace(Property.Custom))
            {
                ModelState.AddModelError("Property.Custom", "Must provide at least one building identifier for site properties");
                ModelState.AddModelError("Property.Unit", "Must provide at least one building identifier site properties");
                ModelState.AddModelError("Property.PropertyCode", "Must provide at least one building identifier for site properties");
            }
        }

        if (!IsSiteProperty.HasValue || !ModelState.IsValid)
        {
            return Page();
        }

        if (id.ToLower() == "new")
        {
            if (IsSiteProperty.Value)
            {
                await _clientService.AddPropertyAsync(clientId, Property, cancellationToken);
            }
            else
            {
                await _clientService.AddPropertyAsync(clientId, Property, Address, cancellationToken);
            }
        }
        else
        {
            await _clientService.UpdatePropertyAsync(clientId, Property, Address, cancellationToken);
        }

        return Redirect(IsSiteProperty.Value ? Urls.ClientSiteProperties(clientId, Property.SiteId.Value) : Urls.ClientProperties(clientId));
    }
}
