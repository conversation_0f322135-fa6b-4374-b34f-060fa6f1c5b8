﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections.Queries.Models;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetInspection : IQuery<InspectionQueryModel>
    {
        public GetInspection(Guid inspectionId)
        {
            InspectionId = inspectionId;
        }

        public Guid InspectionId { get; }
    }

    public class GetInspectionHandler : DapperRequestHandler<GetInspection, InspectionQueryModel>
    {
        public GetInspectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<InspectionQueryModel> OnHandleAsync(IDbHelper db, GetInspection request)
        {
            var sql = @"
SELECT tblInspections.*, ClientId, ClientName, PropertyCode
FROM tblInspections
JOIN tblProperties ON tblProperties.Id = PropertyId
JOIN tblClients ON tblClients.Id = ClientId
WHERE tblInspections.Id = @InspectionId";
            return db.QuerySingleOrDefaultAsync<InspectionQueryModel>(sql, new { request.InspectionId });
        }
    }
}
