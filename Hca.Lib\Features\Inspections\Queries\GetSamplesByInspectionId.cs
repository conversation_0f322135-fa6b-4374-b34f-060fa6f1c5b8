﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections.Queries;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetSamplesByInspectionId : IQueryMany<InspectionSampleDto>
    {
        public GetSamplesByInspectionId(Guid inspectionId)
        {
            InspectionId = inspectionId;
        }

        public Guid InspectionId { get; }
    }

    public class GetSamplesByInspectionIdHandler : DapperRequestHandler<GetSamplesByInspectionId, DtoSet<InspectionSampleDto>>
    {
        public GetSamplesByInspectionIdHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<InspectionSampleDto>> OnHandleAsync(IDbHelper db, GetSamplesByInspectionId request)
        {
            return DtoSet.From(await db.GetListAsync<InspectionSampleDto>("WHERE InspectionId=@InspectionId", new { request.InspectionId }));
        }
    }
}
