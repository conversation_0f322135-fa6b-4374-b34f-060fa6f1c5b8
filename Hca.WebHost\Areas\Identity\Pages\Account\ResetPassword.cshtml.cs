﻿using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Hca.Lib.Identity;
using Hca.Lib.Services;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace Hca.WebHost.Areas.Identity.Pages.Account
{
    public class ResetPasswordModel : PageModel
    {
        private readonly UserManager<HcaUser> _userManager;
        private readonly IMediator _mediator;

        public ResetPasswordModel(UserManager<HcaUser> userManager, IMediator mediator)
        {
            _userManager = userManager;
            _mediator = mediator;
        }

        [BindProperty]
        [Required]
        public string Password { get; set; }

        [BindProperty]
        [Required]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; }

        public async Task<IActionResult> OnPostAsync(string email, string token)
        {
            if (string.IsNullOrWhiteSpace(token)
                || string.IsNullOrWhiteSpace(email)
                || !ModelState.IsValid) return Page();

            var user = await _userManager.FindByEmailAsync(email);
            if (user == null) Redirect(Urls.ResetPasswordConfirmation);

            var resetPassResult = await _userManager.ResetPasswordAsync(user, token, Password);
            if (!resetPassResult.Succeeded)
            {
                foreach (var error in resetPassResult.Errors)
                {
                    ModelState.TryAddModelError(error.Code, error.Description);
                }

                return Page();
            }

            await _mediator.Send(new SendEmail(
                user.Email,
                "Spotlite Compliance password reset complete",
                $"Hi {user.UserName}<br /><br />Your password has been reset.  " +
                $"If you did not request this then please report <NAME_EMAIL>."));

            return Redirect(Urls.ResetPasswordConfirmation);
        }
    }
}
