﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewSite : IQuery<ValidationResult>
{
    public CanContactViewSite(Guid siteId, Guid contactId)
    {
        SiteId = siteId;
        ContactId = contactId;
    }

    public Guid SiteId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewSiteHandler : DapperRequestHandler<CanContactViewSite, ValidationResult>
{
    public CanContactViewSiteHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewSite request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.Sites} " +
            $"JOIN {TableNames.Contacts} ON {TableNames.Contacts}.{nameof(ContactDto.ClientId)} = {TableNames.Sites}.{nameof(SiteDto.ClientId)} " +
            $"WHERE {TableNames.Sites}.{nameof(SiteDto.Id)} = @{nameof(request.SiteId)} " +
            $"AND {TableNames.Contacts}.{nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}