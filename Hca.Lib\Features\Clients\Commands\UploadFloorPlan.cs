﻿using System;
using System.IO;
using System.Threading.Tasks;
using Hca.Lib.Azure;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using MediatR;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UploadFloorPlan : ICommand
    {
        public UploadFloorPlan(
            Guid clientId,
            Guid propertyId,
            Stream imageFile,
            string imageContentType,
            string filename,
            Guid floorId,
            string notes)
        {
            ClientId = clientId;
            PropertyId = propertyId;
            ImageFile = imageFile;
            ImageContentType = imageContentType;
            Filename = filename;
            FloorId = floorId;
            Notes = notes;
        }

        public Guid ClientId { get; }
        public Guid PropertyId { get; }
        public Stream ImageFile { get; }
        public string ImageContentType { get; }
        public string Filename { get; }
        public Guid FloorId { get; }
        public string Notes { get; }
    }

    public class UploadFloorPlanHandler : DapperRequestHandler<UploadFloorPlan, CommandResult>
    {
        private readonly PropertyCountsService _propertyCountsService;
        private readonly IMediator _mediator;

        public UploadFloorPlanHandler(IDbHelper dbHelper, IMediator mediator, PropertyCountsService propertyCountsService) : base(dbHelper)
        {
            _mediator = mediator;
            _propertyCountsService = propertyCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UploadFloorPlan request)
        {
            var blobName = $"{request.ClientId}/properties/{request.PropertyId}/floorplans/{request.Filename}";
            var containerName = StorageConstants.ClientsContainerName;
            var uploadResponse = await _mediator.Send(new UploadBlob(containerName, blobName, request.ImageFile, request.ImageContentType));

            if (!uploadResponse.IsSuccess)
            {
                return CommandResult.Fail("Failed to upload floor plan");
            }

            // reset the stream
            request.ImageFile.Seek(0, SeekOrigin.Begin);
            request.ImageFile.Position = 0;

            var thumbnail = await _mediator.Send(new GetImageJpegThumbnail(request.ImageFile, 0, 100));
            string thumbnailName = null;

            if (thumbnail != null)
            {
                thumbnailName = $"{request.ClientId}/properties/{request.PropertyId}/floorplans/100_{request.Filename}";
                var uploadThumbnailResponse = await _mediator.Send(new UploadBlob(containerName, thumbnailName, thumbnail, "image/jpeg"));

                if (!uploadThumbnailResponse.IsSuccess)
                {
                    return CommandResult.Fail("Failed to upload floor plan thumbnail");
                }
            }

            var dto = new FloorPlanDto
            {
                Id = Guid.NewGuid(),
                ContainerName = containerName,
                BlobName = blobName,
                ThumbnailName = thumbnailName,
                PropertyId = request.PropertyId,
                FloorId = request.FloorId,
                Notes = request.Notes,
                Created = DateTime.UtcNow,
            };

            await db.InsertAsync(dto);

            _propertyCountsService.ClearPropertyCountsAsync(request.PropertyId);

            return CommandResult.Success();
        }
    }
}

