﻿using System;
using System.Collections.Generic;
using System.Linq;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Users;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class TagFactory
    {
        public static IEnumerable<(string Tag, string Text)> CreateStandardTags(PropertyDtoExtended location, ClientDto client, AddressDto address)
        {
            return location.CreateStandardTags()
                .Concat(client.CreateStandardTags())
                .Concat(client.CreateStandardTags(address));
        }

        public static IEnumerable<(string Tag, string Text)> CreateStandardTags(this PropertyDtoExtended location, string propertyPrefix = null)
        {
            return new List<(string, string)>
            {
                ($"{propertyPrefix}PropertyCode", location.PropertyCode)
            };
        }

        public static IEnumerable<(string Tag, string Text)> CreateStandardTags(this UserDto user)
        {
            return new List<(string, string)>
            {
                ("UserName", $"{user.FirstName} {user.LastName}"),
                ("UserPosition", user.Position),
            };
        }

        public static IEnumerable<(string Tag, string Text)> CreateStandardTags()
        {
            return new List<(string, string)>
            {
                ("CurrentDate", DateTime.Now.ToLongDateString()) 
            };
        }

        public static IEnumerable<(string Tag, string Text)> CreateStandardTags(this ClientDto client, AddressDto address, string addressPrefix = null)
        {
            var line1Parts = new string[]
            {
                client.Unit,
                client.BuildingName,
                client.Floor,
            };
            var line1 = string.Join(", ", line1Parts.Where(p => !string.IsNullOrWhiteSpace(p)));
            if (string.IsNullOrWhiteSpace(line1)) line1 = " ";

            var line2Parts = new string[]
            {
                client.BuildingNumber,
                address.StreetName
            };
            var line2 = string.Join(" ", line2Parts.Where(p => !string.IsNullOrWhiteSpace(p)));
            if (string.IsNullOrWhiteSpace(line2)) line2 = " ";

            var list = new List<(string, string)>
            {
                ($"{addressPrefix}BuildingName", client.BuildingName ?? " "),
                ($"{addressPrefix}BuildingNumber", client.BuildingNumber ?? " "),
                ($"{addressPrefix}Unit", client.Unit ?? " "),
                ($"{addressPrefix}Floor", client.Floor ?? " "),
                ($"{addressPrefix}StreetName", address.StreetName ?? " "),
                ($"{addressPrefix}Town", address.Town ?? " "),
                ($"{addressPrefix}City", address.City ?? " "),
                ($"{addressPrefix}County", address.County ?? " "),
                ($"{addressPrefix}Country", address.Country ?? " "),
                ($"{addressPrefix}Postcode", address.Postcode ?? " "),
                ($"{addressPrefix}AddressLine1", line1),
                ($"{addressPrefix}AddressLine2", line2),
            };

            return list;
        }

        public static IEnumerable<(string Tag, string Text)> CreateStandardTags(this ClientDto client)
        {
            return new List<(string, string)>
            {
                ("ClientName", client.ClientName)
            };
        }
    }
}
