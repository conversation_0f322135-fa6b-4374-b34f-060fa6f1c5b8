﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries;

public class GetContactByEmail : IQuery<ContactDto>
{
    public GetContactByEmail(string email)
    {
        Email = email;
    }

    public string Email { get; }
}

public class GetContactByEmailHandler : Dapper<PERSON>equestHandler<GetContactByEmail, ContactDto>
{
    public GetContactByEmailHandler(IDbHelper dbHelper) : base(dbHelper) { }

    public override async Task<ContactDto> OnHandleAsync(IDbHelper db, GetContactByEmail request)
    {
        var dto = await db.GetAsync<ContactDto>($"WHERE {nameof(ContactDto.Email)}=@{nameof(GetContactByEmail.Email)}", request);

        return dto;
    }
}
