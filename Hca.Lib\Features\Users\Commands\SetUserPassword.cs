﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Commands
{
    public class SetUserPassword : ICommand
    {
        public SetUserPassword(Guid userId, string password)
        {
            UserId = userId;
            Password = password;
        }

        public Guid UserId { get; }
        public string Password { get; }
    }

    public class SetUserPasswordHandler : DapperRequestHandler<SetUserPassword, CommandResult>
    {
        public SetUserPasswordHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetUserPassword request)
        {
            var user = await db.GetAsync<UserDto>(request.UserId);
            if (user == null) return CommandResult.Fail("User not found");

            user.Password = request.Password;
            await db.UpdateAsync(user);

            return CommandResult.Success();
        }
    }
}
