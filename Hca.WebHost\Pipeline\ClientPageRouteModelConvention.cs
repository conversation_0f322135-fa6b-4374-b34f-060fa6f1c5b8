﻿using Microsoft.AspNetCore.Mvc.ApplicationModels;
using System.Linq;

namespace Hca.WebHost.Pipeline
{
    public class ClientPageRouteModelConvention : IPageRouteModelConvention
    {
        public void Apply(PageRouteModel model)
        {
            //if (model.RelativePath.StartsWith("/client"))
            //{
            //    foreach (var selector in model.Selectors.ToList())
            //    {
            //        selector.AttributeRouteModel.Template = "client/{tenant}/" + selector.AttributeRouteModel.Template;
            //    }
            //}

            //var loginPath = "/Identity/Account/Login";
            if (model.RelativePath.Contains("Identity"))
            {
                var defaultSelector = model.Selectors.First();

                model.Selectors.Add(new()
                {
                    AttributeRouteModel = new()
                    {
                        Template = "client/{tenant}/" + defaultSelector.AttributeRouteModel.Template,
                    }
                });
            }
        }
    }
}
