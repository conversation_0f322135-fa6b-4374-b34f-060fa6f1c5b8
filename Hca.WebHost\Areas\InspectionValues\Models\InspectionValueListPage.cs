﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.InspectionValues.Models;

[IgnoreAntiforgeryToken]
public abstract class InspectionValueListPage : HcaPageModel
{
    private readonly ValueListsService _inspectionValuesService;

    public InspectionValueListPage(
        ValueListsService inspectionValuesService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _inspectionValuesService = inspectionValuesService;
    }

    public abstract ValueListType ValueListType { get; }

    [BindProperty]
    public IEnumerable<ValueDto> Values { get; set; }

    public virtual async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        Values = await _inspectionValuesService.GetAllAsync(ValueListType, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        if (!await IsAdminUser) return Forbid();

        await _inspectionValuesService.UpdatePriorities(Values, cancellationToken);

        return new OkObjectResult("Updated");
    }
}
