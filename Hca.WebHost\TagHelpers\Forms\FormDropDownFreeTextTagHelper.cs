﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.TagHelpers;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers.Forms
{
    [HtmlTargetElement("free-text-drop-down", Attributes = "asp-for, asp-for-actual, row-label")]
    public class FormDropDownFreeTextTagHelper : TagHelper
    {
        private readonly IHtmlGenerator _htmlGenerator;
        private readonly HttpContext _httpContext;

        public FormDropDownFreeTextTagHelper(
            IHtmlGenerator htmlGenerator,
            IHttpContextAccessor httpContextAccessor)
        {
            _htmlGenerator = htmlGenerator;
            _httpContext = httpContextAccessor.HttpContext;
        }

        [HtmlAttributeName("row-label")]
        public string HcaLabel { get; set; }

        [HtmlAttributeName("validation-content")]
        public string ValidationContent { get; set; }

        [HtmlAttributeName("asp-for")]
        public ModelExpression For { get; set; }

        [HtmlAttributeName("asp-for-actual")]
        public ModelExpression ForActual { get; set; }

        [HtmlAttributeName("asp-items")]
        public IEnumerable<SelectListItem> Items { get; set; }

        [HtmlAttributeName("asp-id")]
        public string Id { get; set; }

        [HtmlAttributeName("asp-style")]
        public string Style { get; set; }

        [HtmlAttributeNotBound]
        [ViewContext]
        public ViewContext ViewContext { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            var preContent = $"<div class=\"form-group row\" id=\"{Id}\" style=\"{Style}\">" +
                $"<label class=\"col-xl-2 col-form-label\">{HcaLabel.HtmlEncode()}</label>" +
                $"<div class=\"col-xl-10 input-group\">";
            var postContent = @$"</div></div>
<script>
    $('#{For.Name.Replace('.', '_')}').change(function () {{
        $('#{ForActual.Name.Replace('.', '_')}').val($(""#{For.Name.Replace('.', '_')} option:selected"").text()).change();
        $('#{For.Name.Replace('.', '_')}').hide();
        $('#{ForActual.Name.Replace('.', '_')}').show();
        $('#btn{ForActual.Name.Replace('.', '_')}').show();
    }});

    $('#btn{ForActual.Name.Replace('.', '_')}').click(function () {{
        $('#{ForActual.Name.Replace('.', '_')}').removeAttr('value');
        $('#{For.Name.Replace('.', '_')}').prop('selectedIndex', 0);
        $('#{For.Name.Replace('.', '_')}').show();
        $('#{ForActual.Name.Replace('.', '_')}').hide();
        $('#btn{ForActual.Name.Replace('.', '_')}').hide();
    }});
</script>";

            var inputContext = CreateTagHelperContext();
            var inputOutput = CreateTagHelperOutput("input");

            inputOutput.Attributes.Add("class", "form-control");
            if (!_httpContext.IsEditMode())
            {
                inputOutput.Attributes.Add("style", "display: none;");
            }

            var input = new InputTagHelper(_htmlGenerator)
            {
                For = ForActual,
                ViewContext = ViewContext
            };

            input.Process(inputContext, inputOutput);

            var clearButton = new TagBuilder("span");
            clearButton.AddCssClass("input-group=-append");
            clearButton.AddCssClass("input-group=-addon");
            clearButton.Attributes.Add("id", $"btn{ForActual.Name.Replace('.', '_')}");
            clearButton.InnerHtml.AppendHtml(@"<span class=""input-group-text far fa-times-circle""></span>"); 
            if (!_httpContext.IsEditMode())
            {
                clearButton.Attributes.Add("style", "display: none;");
            }

            //inputOutput.PostElement.SetHtmlContent(
            //    $@"<span class=""input-group-append input-group-addon"" id=""btn{ForActual.Name.Replace('.', '_')}"" style=""display: none;"">
            //           <span class=""input-group-text far fa-times-circle""></span>
            //       </span>");

            var selectContext = CreateTagHelperContext();
            var selectOutput = CreateTagHelperOutput("select");

            selectOutput.Attributes.Add("class", "chosen-select form-control");
            if (_httpContext.IsEditMode())
            {
                selectOutput.Attributes.Add("style", "display: none;");
            }

            var select = new SelectTagHelper(_htmlGenerator)
            {
                For = For,
                ViewContext = ViewContext,
                Items = Items,
            };

            select.Process(selectContext, selectOutput);

            var validationContext = CreateTagHelperContext();
            var validationOutput = CreateTagHelperOutput("span");

            validationOutput.Content.Append(ValidationContent);

            validationOutput.Attributes.Add("class", "text-danger");

            var validation = new ValidationMessageTagHelper(_htmlGenerator)
            {
                For = ForActual,
                ViewContext = ViewContext
            };

            validation.Process(validationContext, validationOutput);

            output.TagName = "";
            output.PreElement.SetHtmlContent(preContent);
            output.PostElement.SetHtmlContent(postContent);
            output.Content.AppendHtml(selectOutput);
            output.Content.AppendHtml(inputOutput);
            output.Content.AppendHtml(clearButton);
            output.Content.AppendHtml(validationOutput);
        }

        private static TagHelperContext CreateTagHelperContext()
        {
            return new TagHelperContext(
                new TagHelperAttributeList(),
                new Dictionary<object, object>(),
                Guid.NewGuid().ToString("N"));
        }

        private static TagHelperOutput CreateTagHelperOutput(string tagName)
        {
            return new TagHelperOutput(
                tagName,
                new TagHelperAttributeList(),
                (a, b) =>
                {
                    var tagHelperContent = new DefaultTagHelperContent();
                    tagHelperContent.SetContent(string.Empty);
                    return Task.FromResult<TagHelperContent>(tagHelperContent);
                });
        }
    }
}
