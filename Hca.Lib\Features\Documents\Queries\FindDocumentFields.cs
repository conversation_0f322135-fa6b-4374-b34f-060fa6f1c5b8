﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Queries
{
    public class FindDocumentFields : IQueryMany<DocumentFieldDto>
    {
        public FindDocumentFields(Guid documentSectionId)
        {
            DocumentSectionId = documentSectionId;
        }

        public Guid DocumentSectionId { get; }
    }

    public class FindDocumentFieldHandler : DapperRequestHandler<FindDocumentFields, DtoSet<DocumentFieldDto>>
    {
        public FindDocumentFieldHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<DocumentFieldDto>> OnHandleAsync(IDbHelper db, FindDocumentFields request)
        {
            return DtoSet.From((await db.GetListAsync<DocumentFieldDto>(
                    "WHERE DocumentSectionId=@DocumentSectionId",
                    request))
                .OrderBy(f => f.<PERSON>));
        }
    }
}
