﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;

namespace Hca.Lib.Features.Clients.Domain.Commands
{
    public class DeleteFloorPlan : ICommand
    {
        public DeleteFloorPlan(Guid planId)
        {
            PlanId = planId;
        }

        public Guid PlanId { get; }
    }

    public class DeleteFloorPlanHandler : DapperRequestHandler<DeleteFloorPlan, CommandResult>
    {
        private readonly PropertyCountsService _propertyCountsService;

        public DeleteFloorPlanHandler(IDbHelper dbHelper, PropertyCountsService propertyCountsService) : base(dbHelper)
        {
            _propertyCountsService = propertyCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteFloorPlan request)
        {
            var plan = await db.GetAsync<FloorPlanDto>(request.PlanId);
            await db.DeleteAsync<FloorPlanDto>(request.PlanId);
            _propertyCountsService.ClearPropertyCountsAsync(plan.PropertyId);
            return CommandResult.Success();
        }
    }
}
