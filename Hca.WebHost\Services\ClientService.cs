﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Core.Queries;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Commands;
using Hca.Lib.Features.Clients.Domain.Commands;
using Hca.Lib.Features.Clients.Queries;
using MediatR;

namespace Hca.WebHost.Services;

public class ClientService
{
    private readonly IMediator _mediator;
    public ClientService(IMediator mediator)
    {
        _mediator = mediator;
    }

    #region Clients
    public async Task<IEnumerable<ClientDto>> GetAllClientsAsync(CancellationToken cancellationToken = default)
    {
        return (await _mediator.Send(new GetAllClientsQuery(), cancellationToken)).Items.OrderBy(c => c.ClientName);
    }

    public async Task<ClientDto> GetClientAsync(Guid clientId, CancellationToken cancellationToken = default)
    {
        return await _mediator.Send(new GetClientByIdQuery(clientId), cancellationToken);
    }

    public async Task AddClientAsync(ClientDto clientDto, AddressDto addressDto, CancellationToken cancellationToken)
    {
        await _mediator.Send(new CreateClient(
                clientDto.Id,
                clientDto.ClientName,
                clientDto.ClientType), cancellationToken);

        await _mediator.Send(new SetClientAddress(
            clientDto.Id,
            clientDto.BuildingNumber,
            clientDto.BuildingName,
            clientDto.Unit,
            clientDto.Floor,
            addressDto.StreetName,
            addressDto.Town,
            addressDto.City,
            addressDto.County,
            addressDto.Country,
            addressDto.Postcode), cancellationToken);
    }

    public async Task UpdateClientAsync(ClientDto clientDto, AddressDto addressDto, CancellationToken cancellationToken)
    {
        await _mediator.Send(new EditClient(
            clientDto.Id,
            clientDto.ClientName,
            clientDto.UrlSafeClientName), cancellationToken);

        await _mediator.Send(new SetClientAdditionalInfo(
            clientDto.Id,
            clientDto.EmergencyContactDetails,
            clientDto.EscalationProcedure,
            clientDto.KpisAndSlas,
            clientDto.InvoiceEmailAddress,
            clientDto.AccountQueriesEmailAddress,
            clientDto.GeneralRequirements), cancellationToken);

        await _mediator.Send(new SetClientAddress(
            clientDto.Id,
            clientDto.BuildingNumber,
            clientDto.BuildingName,
            clientDto.Unit,
            clientDto.Floor,
            addressDto.StreetName,
            addressDto.Town,
            addressDto.City,
            addressDto.County,
            addressDto.Country,
            addressDto.Postcode), cancellationToken);
    }

    public Task<CommandResult> DeleteClientAsync(Guid clientId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteClient(clientId), cancellationToken);

    public Task<CommandResult> UndeleteClientAsync(Guid clientId, CancellationToken cancellationToken) =>
        _mediator.Send(new UndeleteClient(clientId), cancellationToken);

    public async Task<Uri> UpdateClientLogoAsync(Guid clientId, string filename, byte[] logo, CancellationToken cancellationToken)
    {
        var imageUri = await _mediator.Send(new UploadClientLogo(clientId, filename, logo), cancellationToken);
        await _mediator.Send(new SetClientLogo(clientId, imageUri.ToString()), cancellationToken);
        return imageUri;
    }

    public Task<CommandResult> ArchiveClientAsync(Guid clientId, string reason, CancellationToken cancellationToken) =>
        _mediator.Send(new ArchiveClient(clientId, reason), cancellationToken).ContinueWith(r => r.Result);

    public Task<CommandResult> ReinstateClientAsync(Guid clientId, CancellationToken cancellationToken) =>
        _mediator.Send(new ReinstateClient(clientId), cancellationToken).ContinueWith(r => r.Result);
    #endregion

    #region Contacts

    public Task<DtoSet<ContactDto>> GetContactsAsync(Guid clientId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetContactsByClientId(clientId), cancellationToken);

    public Task DeleteContactAsync(Guid contactId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteContact(contactId), cancellationToken);

    public Task<CommandResult> EnableClientContactLoginAsync(Guid contactId, string newPassword, CancellationToken cancellationToken) =>
        _mediator.Send(new EnableClientContactLogin(contactId, newPassword), cancellationToken);

    public Task<CommandResult> DisableClientContactLoginAsync(Guid contactId, CancellationToken cancellationToken) =>
        _mediator.Send(new DisableClientContactLogin(contactId), cancellationToken);
    #endregion

    #region Properties
    public Task<PropertyDtoExtended> GetPropertyAsync(Guid propertyId, CancellationToken cancellationToken = default) =>
        _mediator.Send(new GetProperty(propertyId), cancellationToken);

    public Task<DtoSet<PropertyDtoExtended>> GetPropertiesAsync(
        Guid clientId,
        ComplianceStatus complianceStatus = ComplianceStatus.ShowAll,
        CancellationToken cancellationToken = default) =>
        GetPropertiesAsync(clientId, null, false, complianceStatus, cancellationToken);

    public Task<DtoSet<PropertyDtoExtended>> GetPropertiesAsync(
        Guid clientId,
        string searchText = null,
        ComplianceStatus complianceStatus = ComplianceStatus.ShowAll,
        CancellationToken cancellationToken = default) =>
        GetPropertiesAsync(clientId, searchText, false, complianceStatus, cancellationToken);

    public Task<DtoSet<PropertyDtoExtended>> GetPropertiesAsync(
        Guid clientId,
        string searchText = null,
        bool isArchived = false,
        ComplianceStatus complianceStatus = ComplianceStatus.ShowAll,
        CancellationToken cancellationToken = default) =>
        _mediator.Send(new GetClientProperties(clientId, searchText, isArchived: isArchived, complianceStatus: complianceStatus), cancellationToken);

    public Task<DtoSet<PropertyDtoExtended>> GetPropertiesAsync(
        Guid clientId,
        Guid siteId,
        CancellationToken cancellationToken = default) =>
        GetPropertiesAsync(clientId, siteId, false, cancellationToken);

    public Task<DtoSet<PropertyDtoExtended>> GetPropertiesAsync(
        Guid clientId,
        Guid siteId,
        bool isArchived = false,
        CancellationToken cancellationToken = default) =>
        _mediator.Send(new GetClientProperties(clientId, siteId, isArchived), cancellationToken);

    public async Task AddPropertyAsync(Guid clientId, PropertyDtoExtended property, CancellationToken cancellationToken)
    {
        var propertyId = Guid.NewGuid();

        await _mediator.Send(new AddPropertyToClient(
            clientId,
            propertyId,
            property.PropertyCode,
            property.Unit,
            property.SiteId,
            property.Custom), cancellationToken);
    }

    public async Task AddPropertyAsync(Guid clientId, PropertyDtoExtended property, AddressDto address, CancellationToken cancellationToken)
    {
        var propertyId = Guid.NewGuid();

        await _mediator.Send(new AddPropertyToClient(
            clientId,
            propertyId,
            property.PropertyCode,
            property.Unit,
            null,
            property.Custom), cancellationToken);

        if (address != null)
        {
            await _mediator.Send(new SetPropertyAddress(
                propertyId,
                property.Unit,
                address.StreetName,
                address.Town,
                address.City,
                address.County,
                address.Country,
                address.Postcode,
                address.Lat,
                address.Lon),
                cancellationToken);
        }
    }

    public Task<CommandResult> DeletePropertyAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteProperty(clientId, propertyId), cancellationToken);

    public Task<CommandResult> UndeletePropertyAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken) =>
        _mediator.Send(new UndeleteProperty(clientId, propertyId), cancellationToken);

    public async Task UpdatePropertyAsync(Guid clientId, PropertyDtoExtended property, AddressDto address, CancellationToken cancellationToken)
    {
        await _mediator.Send(new UpdateProperty(clientId, property.Id, property.AddressId, property.SiteId, property.PropertyCode, property.Custom), cancellationToken);

        if (address == null)
        {
            await _mediator.Send(new RemovePropertyAddress(property.Id), cancellationToken);
        }
        else
        {
            await _mediator.Send(new SetPropertyAddress(
                property.Id,
                property.Unit,
                address.StreetName,
                address.Town,
                address.City,
                address.County,
                address.Country,
                address.Postcode,
                address.Lat,
                address.Lon),
                cancellationToken);
        }
    }

    public Task<CommandResult> ArchivePropertyAsync(Guid propertyId, string reason, CancellationToken cancellationToken) =>
        _mediator.Send(new ArchiveProperty(propertyId, reason), cancellationToken).ContinueWith(r => r.Result);

    public Task<CommandResult> ReinstatePropertyAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken) =>
        _mediator.Send(new ReinstateProperty(clientId, propertyId), cancellationToken).ContinueWith(r => r.Result);
    #endregion

    #region Property Images

    public Task UploadPropertyImageAsync(Guid clientId, Guid propertyId, byte[] imageFile, string contentType, string fileName, CancellationToken cancellationToken) =>
        _mediator.Send(new UploadPropertyPhoto(clientId, propertyId, imageFile, contentType, fileName), cancellationToken);

    #endregion

    #region Property Documents

    public Task UploadPropertyDocumentAsync(
        Guid clientId,
        Guid propertyId,
        Stream imageFile, 
        string contentType,
        string fileName,
        DateTime documentDate,
        PropertyDocumentType documentType,
        string companyName,
        string notes,
        DateTime? nextInspectionDate,
        CancellationToken cancellationToken) =>
        _mediator.Send(new UploadPropertyDocument(
            clientId,
            propertyId,
            imageFile,
            contentType,
            fileName,
            documentDate,
            documentType,
            companyName,
            notes,
            nextInspectionDate),
            cancellationToken);

    public Task<IEnumerable<PropertyDocumentDto>> GetPropertyDocumentsAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetPropertyDocuments(clientId, propertyId), cancellationToken).ContinueWith(r => r.Result.Items);
    public Task<PropertyDocumentDto> GetPropertyDocumentAsync(Guid clientId, Guid propertyId, Guid documentId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetPropertyDocument(clientId, propertyId, documentId), cancellationToken);

    public Task EditPropertyDocumentAsync(
        Guid id,
        PropertyDocumentType documentType,
        DateTime documentDate,
        string companyName,
        string notes,
        DateTime? nextInspectionDate,
        CancellationToken cancellationToken) =>
        _mediator.Send(new EditPropertyDocument(
            id,
            documentDate,
            documentType,
            companyName,
            notes,
            nextInspectionDate),
            cancellationToken);

    public Task<CommandResult> DeletePropertyDocumentAsync(Guid documentId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeletePropertyDocument(documentId), cancellationToken);
    #endregion

    #region Floor Plans

    public Task UploadFloorPlanAsync(
        Guid clientId,
        Guid propertyId,
        Stream imageFile, 
        string contentType,
        string fileName,
        Guid floor,
        string notes,
        CancellationToken cancellationToken) =>
        _mediator.Send(new UploadFloorPlan(
            clientId,
            propertyId,
            imageFile,
            contentType,
            fileName,
            floor,
            notes),
            cancellationToken);

    public Task<IEnumerable<FloorPlanDto>> GetFloorPlansAsync(Guid clientId, Guid propertyId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetFloorPlans(clientId, propertyId), cancellationToken).ContinueWith(r => r.Result.Items);

    public Task EditFloorPlanAsync(
        Guid id,
        Guid floor,
        string notes,
        CancellationToken cancellationToken) =>
        _mediator.Send(new EditFloorPlan(
            id,
            floor,
            notes),
            cancellationToken);

    public Task<CommandResult> DeleteFloorPlanAsync(Guid floorPlanId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteFloorPlan(floorPlanId), cancellationToken);

    #endregion

    #region Sites
    internal Task<SiteDtoExtended> GetSiteAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetClientSite(clientId, siteId), cancellationToken);

    public async Task AddSiteAsync(Guid clientId, SiteDtoExtended site, AddressDto address, CancellationToken cancellationToken)
    {
        var siteId = Guid.NewGuid();
        await _mediator.Send(new AddSiteToClient(clientId, siteId, site.SiteName), cancellationToken);
        await _mediator.Send(new SetSiteAddress(
            clientId,
            siteId,
            address.StreetName,
            address.Town,
            address.City,
            address.County,
            address.Country,
            address.Postcode,
            address.Lat,
            address.Lon), cancellationToken);
    }

    internal Task UpdateSiteAsync(Guid clientId, SiteDtoExtended site, AddressDto address, CancellationToken cancellationToken)
    {
        return Task.WhenAll(
         _mediator.Send(new UpdateSite(clientId, site.Id, site.AddressId, site.SiteName), cancellationToken),
         _mediator.Send(new SetSiteAddress(
            clientId,
            site.Id,
            address.StreetName,
            address.Town,
            address.City,
            address.County,
            address.Country,
            address.Postcode,
            address.Lat,
            address.Lon), cancellationToken));
    }

    public Task<CommandResult> DeleteSiteAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteSite(clientId, siteId), cancellationToken);

    public Task<CommandResult> UndeleteSiteAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken) =>
        _mediator.Send(new UndeleteSite(clientId, siteId), cancellationToken);

    public Task<DtoSet<SiteDtoExtended>> GetSitesAsync(Guid clientId, string searchText, CancellationToken cancellationToken) =>
        GetSitesAsync(clientId, searchText, false, cancellationToken);

    public Task<DtoSet<SiteDtoExtended>> GetSitesAsync(Guid clientId, string searchText, bool isArchived, CancellationToken cancellationToken) =>
        _mediator.Send(new GetClientSites(clientId, searchText, isArchived), cancellationToken);

    public Task<CommandResult> ArchiveSiteAsync(Guid clientId, Guid siteId, string reason, CancellationToken cancellationToken) =>
        _mediator.Send(new ArchiveSite(siteId, reason), cancellationToken).ContinueWith(r => r.Result);

    public Task<CommandResult> ReinstateSiteAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken) =>
        _mediator.Send(new ReinstateSite(clientId, siteId), cancellationToken).ContinueWith(r => r.Result);
    #endregion

    #region Site Plans
    public Task<DtoSet<SitePlanDto>> GetSitePlansAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetSitePlans(clientId, siteId), cancellationToken);

    public Task UploadSitePlanAsync(
        Guid clientId,
        Guid siteId,
        byte[] imageFile, 
        string contentType,
        string fileName,
        string notes,
        CancellationToken cancellationToken) =>
        _mediator.Send(new UploadSitePlan(
            clientId,
            siteId,
            imageFile,
            contentType,
            fileName,
            notes),
            cancellationToken);

    public Task EditSitePlanAsync(
        Guid planId,
        string notes,
        CancellationToken cancellationToken) =>
        _mediator.Send(new EditSitePlan(
            planId,
            notes),
            cancellationToken);

    public Task<CommandResult> DeleteSitePlanAsync(Guid planId, CancellationToken cancellationToken) =>
        _mediator.Send(new DeleteSitePlan(planId), cancellationToken);
    #endregion

    #region Addresses
    public Task<AddressDto> GetAddressAsync(Guid addressId, CancellationToken cancellationToken) =>
        _mediator.Send(new GetAddress(addressId), cancellationToken);
    #endregion

}
