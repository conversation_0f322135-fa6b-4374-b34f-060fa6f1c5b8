﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class EditClient : ICommand
    {
        public EditClient(
            Guid clientId,
            string clientName,
            string urlSafeClientName)
        {
            ClientId = clientId;
            ClientName = clientName;
            UrlSafeClientName = urlSafeClientName;
        }

        public string ClientName { get; }
        public string UrlSafeClientName { get; }
        public Guid ClientId { get; }
    }

    public class EditClientHandler : DapperRequestHandler<EditClient, CommandResult>
    {
        public EditClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EditClient request)
        {
            var dto = await db.GetAsync<ClientDto>(request.ClientId);
            dto.ClientName = request.ClientName;
            dto.UrlSafeClientName = request.UrlSafeClientName;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
