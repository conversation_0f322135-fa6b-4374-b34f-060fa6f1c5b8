﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents.Commands;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

[IgnoreAntiforgeryToken(Order = 1001)]
public class ReportTemplateModel : HcaPageModel
{
    public ReportTemplateModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task<IActionResult> OnPostAsync(
        Guid reportId,
        [FromBody] string body,
        CancellationToken cancellationToken)
    {
        if (!Guid.TryParse(body, out var templateId))
        {
            return new BadRequestObjectResult("Bad template ID");
        }

        await _mediator.Send(new SetDocumentTemplate(reportId, templateId), cancellationToken);

        return new OkObjectResult(new { });
    }
}
