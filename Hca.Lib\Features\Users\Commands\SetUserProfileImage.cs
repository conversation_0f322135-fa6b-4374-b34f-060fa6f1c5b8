﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Azure.Core;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Users.Commands
{
    public class SetUserProfileImage : ICommand
    {
        public SetUserProfileImage(
            Guid userId,
            byte[] imageFile,
            string contentType,
            string filename)
        {
            UserId = userId;
            ImageFile = imageFile;
            ContentType = contentType;
            Filename = filename;
        }

        public Guid UserId { get; }
        public byte[] ImageFile { get; }
        public string ContentType { get; }
        public string Filename { get; }
    }

    public class SetUserProfileImageHandler : DapperRequestHandler<SetUserProfileImage, CommandResult>
    {
        private readonly IMediator _mediator;

        public SetUserProfileImageHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetUserProfileImage request)
        {
            var userDto = await db.GetAsync<UserDto>(request.UserId);
            var fileType = request.Filename.Split('.').Last();
            var targetFile = $"{request.UserId}/ProfileImage.{fileType}";

            await _mediator.Send(new UploadBlob(
                "users",
                targetFile,
                request.ImageFile, 
                request.ContentType));

            userDto.ProfileImageFileName = targetFile;

            await db.UpdateAsync(userDto);

            return CommandResult.Success();
        }
    }
}
