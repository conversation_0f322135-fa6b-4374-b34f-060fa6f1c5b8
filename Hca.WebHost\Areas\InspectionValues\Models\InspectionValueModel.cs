﻿using System.Collections.Generic;
using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Pages.Widgets;

namespace Hca.WebHost.Areas.InspectionValues.Models
{
    public class ValueModel
    {
        public ValueDto Value { get; set; }

        public Dictionary<string, string> ColumnNames { get; set; }

        public string Header { get; set; }

        public string SubHeader { get; set; }

        public InspectionValueType InspectionValueType { get; set; }
    }
}
