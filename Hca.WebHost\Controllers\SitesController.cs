﻿using Hca.Lib.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Hca.WebHost.Controllers;

[ApiController]
public class SitesController : Controller
{
    private readonly SiteCountsService _siteCountsService;

    public SitesController(SiteCountsService siteCountsService)
    {
        _siteCountsService = siteCountsService;
    }

    [HttpGet("api/{clientId}/sites/{siteId}/child-counts")]
    public async Task<IActionResult> GetSiteChildCounts(Guid clientId, Guid siteId)
    {
        var counts = await _siteCountsService.GetSiteCountsAsync(clientId, siteId);
        return Ok(counts);
    }
}
