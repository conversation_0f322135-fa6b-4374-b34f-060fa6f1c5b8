﻿using Hca.Lib.Azure.Blob;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Services.QRFileSpot;
using MediatR;
using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands;

public class CreatePropertyDocumentQrCode : ICommand
{
    public CreatePropertyDocumentQrCode(
        Guid clientId, 
        Guid propertyId, 
        Guid documentId,
        string categoryName,
        string documentType)
    {
        ClientId = clientId;
        PropertyId = propertyId;
        DocumentId = documentId;
        CategoryName = categoryName;
        DocumentType = documentType;
    }

    public Guid ClientId { get; }
    public Guid PropertyId { get; }
    public Guid DocumentId { get; }
    public string CategoryName { get; }
    public string DocumentType { get; }
}

public class CreatePropertyDocumentQrCodeHandler : DapperRequestHandler<CreatePropertyDocumentQrCode, CommandResult>
{
    private readonly IMediator _mediator;
    private readonly IQrFileSpotService _qrFileSpotService;

    public CreatePropertyDocumentQrCodeHandler(
        IDbHelper dbHelper,
        IQrFileSpotService qrFileSpotService,
        IMediator mediator) : base(dbHelper)
    {
        _qrFileSpotService = qrFileSpotService;
        _mediator = mediator;
    }

    public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreatePropertyDocumentQrCode request)
    {
        var document = await _mediator.Send(new GetPropertyDocument(request.ClientId, request.PropertyId, request.DocumentId));
        var blobStream = await _mediator.Send(new OpenBlobStream(document.ContainerName, document.BlobName));
        var filename = Path.GetFileName(document.BlobName);

        var qrDocument = await _qrFileSpotService.PostDocumentAsync(
            new Refit.StreamPart(blobStream.Stream, filename, blobStream.ContentType),
            request.CategoryName,
            request.DocumentType,
            null,
            CancellationToken.None);

        document.QrCodeId = qrDocument.Id;
        await db.UpdateAsync(document);

        return CommandResult.Success();
    }
}
