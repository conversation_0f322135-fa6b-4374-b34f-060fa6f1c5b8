﻿using Microsoft.AspNetCore.Mvc.Filters;
using System;
using System.Threading.Tasks;

namespace Hca.WebHost.Pipeline
{
    public class SelectedClientActionFilter : IAsyncPageFilter
    {
        private readonly ViewManager _viewManager;

        public SelectedClientActionFilter(
            ViewManager viewManager)
        {
            _viewManager = viewManager;
        }

        public async Task OnPageHandlerExecutionAsync(PageHandlerExecutingContext context, PageHandlerExecutionDelegate next)
        {
            // token contains user identity, this will dictate client id for client users
            // client id might be set for hca users by choice, this would show that client until unset
            // either of these means the client dashboard will be shown
            // this decision is stored in the viewmanager storage

            var query = context.HttpContext.Request.Query;

            if (await _viewManager.IsHcaUser && query.ContainsKey(Constants.QueryStringViewAsClient))
            {
                if (Guid.TryParse(query[Constants.QueryStringViewAsClient], out var clientId))
                {
                    // user is an hca employee and there is a clientId in the querystring
                    _viewManager.ClientId = clientId;
                }
                else
                {
                    // either an invalid client id is provided or hca user has requested to close the client view
                    _viewManager.ClientId = null;
                }
            }

            if (await _viewManager.IsClientUser)
            {
                // user is a client user so set the client id in the view manager
                _viewManager.ClientId = _viewManager.UserClientId;
            }

            await next();
        }

        public Task OnPageHandlerSelectionAsync(PageHandlerSelectedContext context) => Task.CompletedTask;
    }
}
