﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Recommendation.New
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Recommendation Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Recommendation",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />