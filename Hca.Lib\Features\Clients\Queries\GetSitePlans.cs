﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetSitePlans : IQueryMany<SitePlanDto>
    {
        public GetSitePlans(Guid clientId, Guid siteId)
        {
            ClientId = clientId;
            SiteId = siteId;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
    }

    public class GetSitePlansHandler : DapperRequestHandler<GetSitePlans, DtoSet<SitePlanDto>>
    {
        public GetSitePlansHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<SitePlanDto>> OnHandleAsync(IDbHelper db, GetSitePlans request)
        {
            var dtos = await db.GetListAsync<SitePlanDto>(
                $"WHERE {nameof(SitePlanDto.SiteId)}=@{nameof(request.SiteId)}",
                request);

            return DtoSet.From(dtos.OrderByDescending(d => d.Created));
        }
    }
}
