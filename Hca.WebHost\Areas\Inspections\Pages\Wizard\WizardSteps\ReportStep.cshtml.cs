﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Documents.Queries;
using Hca.Lib.Features.Inspections.Commands;
using Hca.Lib.Features.Inspections.Queries;
using Hca.Lib.Features.Inspections.Queries.Models;
using Hca.Lib.Features.Templates;
using Hca.Lib.Features.Templates.Data.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

public class ReportModel : HcaPageModel
{
    public ReportModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {

    }

    public async Task OnGetAsync(Guid inspectionId, CancellationToken cancellationToken)
    {
        var existingReports = await _mediator.Send(new FindDocuments(
            inspectionId),
            // filter by report status later - new[] { ReportStatus.InProgress, ReportStatus.ReadyForPublishing }),
            cancellationToken);

        if (!existingReports.Items.Any())
        {
            await _mediator.Send(new CreateInspectionReport(Guid.NewGuid(), inspectionId), cancellationToken);
            existingReports = await _mediator.Send(new FindDocuments(inspectionId, DocumentStatus.InProgress), cancellationToken);
        }

        if (existingReports.Items.Count() > 1)
        {
            throw new ApplicationException("Can't deal with this right now");
        }

        if (existingReports.Items.Count() == 1)
        {
            Report = existingReports.Items.Single();
        }

        ReportTemplates = (await _mediator.Send(new GetAllTemplatesQuery(), cancellationToken)).Items;
        Inspection = await _mediator.Send(new GetInspection(inspectionId), cancellationToken);
    }

    public DocumentDto Report { get; private set; }
    public IEnumerable<TemplateDto> ReportTemplates { get; private set; }
    public InspectionQueryModel Inspection { get; private set; }
}
