﻿@page

@using Hca.WebHost.Areas.Inspections.Models

@model Hca.WebHost.Areas.Inspections.Pages.NewModel

<div class="content-heading">New Inspection</div>

<div class="card card-default">
    <div class="card-body">
        <partial name="Wizard/_InspectionWizard" model="new InspectionWizardModel { Stage = InspectionMenuStage.Details, Inspection = Model.Inspection, IsNew = true }" />
    </div>
</div>


@section scripts {
    @Html.Raw(Model.WriteScriptBlocks())
}