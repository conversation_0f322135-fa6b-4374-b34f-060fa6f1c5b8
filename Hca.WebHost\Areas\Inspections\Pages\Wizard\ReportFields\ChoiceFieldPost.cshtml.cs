﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents.Commands;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard.ReportFields;

[IgnoreAntiforgeryToken(Order = 1001)]
public class ChoiceFieldPostModel : HcaPageModel
{
    public ChoiceFieldPostModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task<IActionResult> OnPostAsync(
        Guid reportId,
        Guid sectionId,
        Guid fieldId,
        CancellationToken cancellationToken)
    {
        await _mediator.Send(new SetDocumentChoicesField(
                reportId,
                sectionId,
                fieldId,
                Input.Choices.Where(c => c.<PERSON>sen).Select(c => c.Id)),
            cancellationToken);

        return new OkResult();
    }

    [BindProperty]
    public ChoicesModel Input { get; set; }
}

public class ChoicesModel
{
    public IEnumerable<ChoiceModel> Choices { get; set; }
}

public class ChoiceModel
{
    public Guid Id { get; set; }

    public bool Chosen { get; set; }
}
