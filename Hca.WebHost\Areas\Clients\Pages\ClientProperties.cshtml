﻿@page "/clients/{clientId:guid}/properties"
@model Hca.WebHost.Areas.Clients.Pages.ClientPropertiesModel
@{
    Layout = "_LayoutClient";
    ViewData["Client"] = Model.Client;
}

<div class="content-area">
    <div class="row">
        <div class="col">
            <form hx-post="/clients/@Model.Client.Id/properties"
                  hx-target="#divPropertySearchResults"
                  hx-swap="innerHTML"
                  hx-trigger="load, change from:#ShowArchivedProperties, click from:#btnPropertiesSearch">
                <div class="form-group mb-4">
                    <input class="form-control mb-2" type="text" placeholder="Search properties" id="txtSearch" name="searchText">
                    <div class="d-flex">
                        <button class="btn btn-secondary" type="button" id="btnPropertiesSearch">
                            Search
                        </button>
                        <button class="btn btn-sm btn-secondary">Clear</button>
                        <div class="d-flex align-items-center">
                            &nbsp;&nbsp;&nbsp;
                            <select asp-for="ComplianceStatusFilter">
                                <option value="0">Show All</option>
                                <option value="1">Compliant</option>
                                <option value="2">Not compliant</option>
                                <option value="3">Next inspection is due within one month</option>
                                <option value="4">No documents uploaded and/or no ACMs identified or presumed</option>
                            </select>
                            &nbsp;&nbsp;&nbsp;
                            <input asp-for="ShowArchivedProperties" class="ml-4 mx-2" />Show Archived Properties
                        </div>
                    </div>
                    @Html.AntiForgeryToken()
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card card-default results-card">
                <div class="card-header">
                    <small class="text-muted">PROPERTIES</small>
                </div>
                <div class="card-body" id="divPropertySearchResults">
                </div>
            </div>
        </div>
    </div>
</div>





@section Styles {
    <style>
        /* Dynamic viewport height layout that respects footer */
        .content-area {
            display: flex;
            flex-direction: column;
            height: calc(100vh - 195px); /* Navbar + content padding + footer space */
            min-height: 400px; /* Minimum sensible height */
        }

        .content-area > .row:first-child {
            flex-shrink: 0; /* Search form doesn't shrink */
        }

        .content-area > .row:last-child {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
        }

        .content-area > .row:last-child > .col {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-area > .row:last-child > .col > .card {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body {
            flex: 1;
            overflow: hidden;
            min-height: 0;
            padding: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body > .row {
            height: 100%;
            margin: 0;
        }

        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6 {
            padding: 15px;
        }

        .scrollable-list-container {
            height: 100%;
            overflow-y: auto;
            overflow-x: hidden;
            padding-right: 15px; /* Account for scrollbar */
        }

        /* Ensure the map column has fixed height */
        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6:last-child {
            height: 100%;
            overflow: hidden;
        }

        /* Ensure the properties list column can scroll */
        .content-area > .row:last-child > .col > .card > .card-body > .row > .col-lg-6:first-child {
            height: 100%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        /* Responsive adjustments */
        @@media (max-height: 600px) {
            .content-area {
                height: calc(100vh - 120px);
                min-height: 300px;
            }
        }

        @@media (max-width: 768px) {
            .content-area {
                height: calc(100vh - 120px);
                min-height: 350px;
            }
        }
    </style>
}

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([

                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { text: 'Properties' }]);
        });

    </script>
}
