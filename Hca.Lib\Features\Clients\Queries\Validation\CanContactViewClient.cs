﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewClient : IQuery<ValidationResult>
{
    public CanContactViewClient(Guid clientId, Guid contactId)
    {
        ClientId = clientId;
        ContactId = contactId;
    }

    public Guid ClientId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewClientHandler : DapperRequestHandler<CanContactViewClient, ValidationResult>
{
    public CanContactViewClientHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewClient request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.Contacts} " +
            $"WHERE {nameof(ContactDto.ClientId)} = @{nameof(request.ClientId)} " +
            $"AND {nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}
