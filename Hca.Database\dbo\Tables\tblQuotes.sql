﻿CREATE TABLE [dbo].[tblQuotes] (
    [Id]               UNIQUEIDENTIFIER CONSTRAINT [DF_Quotes_Id] DEFAULT (newid()) NOT NULL,
    [Created]          DATETIME         CONSTRAINT [DF_Quotes_Created] DEFAULT (getdate()) NOT NULL,
    [ClientId]         UNIQUEIDENTIFIER NOT NULL,
    [ContactId]        UNIQUEIDENTIFIER NULL,
    [Quote<PERSON><PERSON><PERSON>]      NVARCHAR (16)    NOT NULL,
    [InspectionTypeId] UNIQUEIDENTIFIER NOT NULL,
    [BaseFee]          DECIMAL (18)     DEFAULT ((0)) NOT NULL,
    [Notes]            NVARCHAR (MAX)   NULL,
    CONSTRAINT [PK_Quotes] PRIMARY KEY CLUSTERED ([Id] ASC),
    CONSTRAINT [FK_Quotes_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[tblClients] ([Id]),
    CONSTRAINT [FK_Quotes_InspectionTypes] FOR<PERSON><PERSON><PERSON> KEY ([InspectionTypeId]) REFERENCES [dbo].[tblValueLists] ([Id])
);

