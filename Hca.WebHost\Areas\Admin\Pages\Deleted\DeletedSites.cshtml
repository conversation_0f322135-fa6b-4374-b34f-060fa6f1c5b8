﻿@page "/deletedsites"
@model Hca.WebHost.Areas.Admin.Pages.DeletedSitesModel
@{
    ViewData["Title"] = "Deleted Sites";
}
<div class="card card-default">
    <div class="card-header">
        <h5>DELETED SITES</h5>
    </div>
    <div id="divUsers" class="card-body">
        <table class="table table-striped table-bordered table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>Client Name</th>
                    <th>Site Name</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                @foreach (var site in Model.Sites.Items)
                {
                    <tr>
                        <td>@site.ClientName</td>
                        <td>@site.SiteName</td>
                        <td class="d-flex">
                            <div class="ml-auto">
                                <anchor action="@Urls.ClientSite(site.ClientId, site.Id)" text="view" style=""></anchor>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>

        @if (Model.Sites.TotalPages > 1)
        {
            <nav>
                <ul class="pagination">
                    @if (Model.Sites.CurrentPage > 1)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Sites.CurrentPage - 1)">Previous</a>
                        </li>
                    }

                    @for (int i = 1; i <= Model.Sites.TotalPages; i++)
                    {
                        <li class="page-item @(i == Model.Sites.CurrentPage ? "active" : "")">
                            <a class="page-link" href="?pageNum=@i">@i</a>
                        </li>
                    }

                    @if (Model.Sites.CurrentPage < Model.Sites.TotalPages)
                    {
                        <li class="page-item">
                            <a class="page-link" href="?pageNum=@(Model.Sites.CurrentPage + 1)">Next</a>
                        </li>
                    }
                </ul>
            </nav>
        }
    </div>
</div>

@section scripts {
    <script>
        $(() => {
            drawBreadcrumb([{ url: '@Urls.DeletedSites', text: 'Deleted Sites' }]);
        });</script>
}