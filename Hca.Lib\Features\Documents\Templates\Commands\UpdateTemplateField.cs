﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Templates.Fields;

namespace Hca.Lib.Features.Templates.Commands
{
    public class UpdateTemplateField : TemplateCommand
    {
        public UpdateTemplateField(
            Guid templateId,
            Guid sectionId,
            ITemplateField<Field> field) : base(templateId)
        {
            SectionId = sectionId;
            Field = field;
        }

        public Guid SectionId { get; }
        public ITemplateField<Field> Field { get; }
    }

    public class UpdateTemplateFieldHandler : DapperRequestHandler<UpdateTemplateField, CommandResult>
    {
        public UpdateTemplateFieldHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UpdateTemplateField request)
        {
            var dto = await db.GetAsync<TemplateFieldDto>(request.Field.TemplateFieldId);

            dto.FieldContent = request.Field.Value.FieldContent;
            dto.FieldOrder = request.Field.Value.FieldOrder;
            dto.Optional = request.Field.Value.IsOptional;

            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
