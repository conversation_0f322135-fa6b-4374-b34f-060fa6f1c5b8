﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Sas;
using MediatR;

namespace Hca.Lib.Azure.Blob
{
    public class GetSasUrl : IRequest<Uri>
    {
        public GetSasUrl(
            string containerName,
            string blobName,
            bool canWrite)
        {
            ContainerName = containerName;
            BlobName = blobName;
            CanWrite = canWrite;
        }

        public string ContainerName { get; }
        public string BlobName { get; }
        public bool CanWrite { get; }
    }

    public class GetSasUrlHandler : IRequestHandler<GetSasUrl, Uri>
    {
        private readonly BlobServiceClient _client;
        public GetSasUrlHandler(BlobServiceClient blobClient) { _client = blobClient; }

        public async Task<Uri> Handle(GetSasUrl request, CancellationToken cancellationToken)
        {
            // todo: can this be cached?
            var key = await _client.GetUserDelegationKeyAsync(
                DateTimeOffset.UtcNow.AddMinutes(-5), 
                DateTimeOffset.UtcNow.AddDays(1), 
                cancellationToken);

            BlobSasBuilder sasBuilder = new()
            {
                BlobContainerName = request.ContainerName,
                BlobName = request.BlobName,
                Resource = "b",
                StartsOn = DateTimeOffset.UtcNow.AddMinutes(-5),
                ExpiresOn = DateTimeOffset.UtcNow.AddHours(1)
            };

            sasBuilder.SetPermissions(request.CanWrite ? BlobSasPermissions.All : BlobSasPermissions.Read);
            
            var sasToken = sasBuilder.ToSasQueryParameters(key, _client.AccountName).ToString();

            var fullUri = new UriBuilder()
            {
                Scheme = "https",
                Host = string.Format("{0}.blob.core.windows.net", _client.AccountName),
                Path = string.Format("{0}/{1}", request.ContainerName, request.BlobName),
                Query = sasToken
            };

            return fullUri.Uri;
        }
    }
}
