﻿using System;
using System.Collections.Generic;
using Hca.Lib.Features.Documents.Publishing;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Reports.Publishing
{
    public class ReportModel 
    {
        public Guid ReportId { get; set; }
        public IEnumerable<(InspectionSampleDto Sample, IEnumerable<InspectionSampleImageDto> Images)> Samples { get; set; }
        public DocumentModel DocumentModel { get; set; }
    }
}
