﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Events
{
    public class DocumentSectionCompleted : IEvent
    {
        public DocumentSectionCompleted(
            Guid documentId,
            Guid sectionId)
        {
            DocumentId = documentId;
            SectionId = sectionId;
        }

        public Guid DocumentId { get; }
        public Guid SectionId { get; }
    }

    public class DocumentSectionCompleted_UpdateDocumentStatus : DapperEventHandler<DocumentSectionCompleted>
    {
        public DocumentSectionCompleted_UpdateDocumentStatus(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task OnHandleAsync(IDbHelper db, DocumentSectionCompleted request)
        {
            var sections = await db.GetListAsync<DocumentSectionDto>("WHERE DocumentId = @DocumentId", request);

            if (sections.Any(f => !f.SectionComplete)) return;

            var document = await db.GetAsync<DocumentDto>(request.DocumentId);
            document.DocumentStatus = DocumentStatus.ReadyForPublishing; // todo: business object needed now
            await db.UpdateAsync(document);
        }
    }
}
