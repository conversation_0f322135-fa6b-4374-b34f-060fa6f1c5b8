﻿@model IEnumerable<Hca.Lib.Features.Clients.ClientDto>
<table class="table table-striped table-bordered table-hover" id="tblClients">
    <thead class="thead-dark">
        <tr>
            <th width="100%">Client Name</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var client in Model)
        {
            <tr style="cursor: pointer;"
                data-toggle="tooltip"
                data-placement="top"
                data-html="true"
                data-original-title="@(client.ArchiveReason.IsPopulated() ? $"<strong>Archive Reason</strong>: {client.ArchiveReason}" : "")"
                onclick="window.location.href='@Urls.Clients/@client.Id';">
                <td>@client.ClientName</td>
            </tr>
        }
    </tbody>
</table>

<script>
    $('[data-toggle="tooltip"]').tooltip({
        container: 'body'
    });
</script>