﻿using System;
using System.Linq;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Projects.Queries;
using MediatR;

namespace Hca.Lib.Features.Projects.Commands
{
    public class ConvertQuoteToProject : ICommand<Guid>
    {
        public Guid QuoteId { get; set; }

        public ConvertQuoteToProject(Guid quoteId)
        {
            QuoteId = quoteId;
        }
    }

    public class ConvertQuoteToProjectHandler : DapperRequestHandler<ConvertQuoteToProject, CommandResult<Guid>>
    {
        private readonly IMediator _mediator;

        public ConvertQuoteToProjectHandler(IDb<PERSON>elper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<CommandResult<Guid>> OnHandleAsync(IDbHelper db, ConvertQuoteToProject request)
        {
            var quote = await db.GetAsync<QuoteDto>(request.QuoteId);
            if (quote == null) return CommandResult<Guid>.Fail("Invalid quote ID");
            if (!quote.InspectionTypeId.HasValue) return CommandResult<Guid>.Fail("Quote type not selected");

            var existing = await db.GetAsync<ProjectDto>(
                $"WHERE {nameof(ProjectDto.QuoteId)} = @{nameof(ConvertQuoteToProject.QuoteId)}",
                request);
            if (existing != null) return CommandResult<Guid>.Success(existing.Id);

            var quoteProperties = await db.GetListAsync<QuotePropertyDto>(
                $"WHERE {nameof(QuotePropertyDto.QuoteId)} = @{nameof(ConvertQuoteToProject.QuoteId)}",
                request);

            var project = new ProjectDto
            {
                Id = Guid.NewGuid(),
                QuoteId = request.QuoteId,
                ClientId = quote.ClientId,
                ProjectNumber = (await _mediator.Send(new GetNextProjectNumber())).GetValue()
            };

            var properties = quoteProperties.Select(p =>
                new InspectionDto
                {
                    Id = Guid.NewGuid(),
                    ProjectId = project.Id,
                    PropertyId = p.PropertyId,
                    InspectionTypeId = quote.InspectionTypeId.Value,
                });

            await db.InsertAsync(project);
            await Task.WhenAll(properties.Select(p => db.InsertAsync(p)));

            return CommandResult<Guid>.Success(project.Id);
        }
    }
}
