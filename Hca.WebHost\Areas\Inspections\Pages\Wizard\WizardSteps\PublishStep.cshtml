﻿@page "/inspections/{inspectionId:guid}/publishstep"
@model Hca.WebHost.Areas.Inspections.Pages.Wizard.PublishModel
@{
    Layout = null;
}
<div class="card card-default">
    <div class="card-header">Report publish</div>
    <div class="card-body">
        <p>Some summary details about the report perhaps?</p>

        @if (Model.Reports.Count() == 0)
        {
            <p>No report found</p>
        }

        @if (Model.Reports.Count() > 1)
        {
            <p>I can't deal with more than one report right now</p>
        }

        @if (Model.ReportId.HasValue)
        {
            <form method="post" id="frmPublish">
                <div class="form-group row">
                    <label asp-for="SendToEmailAddress" class="col-sm-3 col-form-label"></label>
                    <div class="col-sm-7">
                        <input asp-for="SendToEmailAddress" class="form-control">
                    </div>
                </div>
                <input type="hidden" asp-for="ReportId" />
                <input type="submit" class="btn btn-primary" value="Publish" id="btnPublish" />
            </form>
        }
    </div>
</div>

<script>(() => {
    $(() => {
        $('input#btnPublish').on('click', function (evt) {
            evt.preventDefault();
            $.post('/inspections/@Model.InspectionId/publishstep', $('form#frmPublish').serialize(), function () {
                alert('Posted using jQuery');
            });
        });
    });
    })();</script>