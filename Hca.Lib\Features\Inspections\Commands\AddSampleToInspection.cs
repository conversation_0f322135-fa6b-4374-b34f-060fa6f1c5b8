﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class AddSampleToInspection : InspectionCommand
    {
        public AddSampleToInspection(
            Guid inspectionId,
            Guid sampleId,
            string sampleReference,
            string sampleNotes,
            string status,
            Guid? floorPlanColour,
            Guid floor,
            string floorActual,
            string floorPlanReference,
            string roomUsage,
            string location,
            Guid? material,
            string materialActual,
            string materialColour,
            string quantity,
            Guid? quantityUnit,
            int? productType,
            int? extentOfDamage,
            int? surfaceTreatment,
            int? asbestosType,
            int? occupantActivity,
            int? likelihoodOfDisturbance,
            int? numberOfOccupants,
            int? typeOfMaintenance,
            int? accessibility,
            Guid? managementRecommendation,
            Guid? priority) : base(inspectionId)
        {
            SampleId = sampleId;
            SampleReference = sampleReference;
            SampleNotes = sampleNotes;
            Status = status;
            FloorPlanColour = floorPlanColour;
            Floor = floor;
            FloorActual = floorActual;
            FloorPlanReference = floorPlanReference;
            RoomUsage = roomUsage;
            Location = location;
            Material = material;
            MaterialActual = materialActual;
            MaterialColour = materialColour;
            Quantity = quantity;
            QuantityUnit = quantityUnit;
            ProductType = productType;
            ExtentOfDamage = extentOfDamage;
            SurfaceTreatment = surfaceTreatment;
            AsbestosType = asbestosType;
            OccupantActivity = occupantActivity;
            LikelihoodOfDisturbance = likelihoodOfDisturbance;
            NumberOfOccupants = numberOfOccupants;
            TypeOfMaintenance = typeOfMaintenance;
            Accessibility = accessibility;
            ManagementRecommendation = managementRecommendation;
            Priority = priority;
        }

        public Guid SampleId { get; }
        public string SampleReference { get; }
        public string SampleNotes { get; }
        public string Status { get; set; }
        public Guid? FloorPlanColour { get; set; }
        public Guid Floor { get; set; }
        public string FloorActual { get; }
        public string FloorPlanReference { get; set; }
        public string RoomUsage { get; set; }
        public string Location { get; set; }
        public Guid? Material { get; set; }
        public string MaterialActual { get; }
        public string MaterialColour { get; }
        public string Quantity { get; set; }
        public Guid? QuantityUnit { get; set; }
        public int? ProductType { get; set; }
        public int? ExtentOfDamage { get; set; }
        public int? SurfaceTreatment { get; set; }
        public int? AsbestosType { get; set; }
        public int? OccupantActivity { get; set; }
        public int? LikelihoodOfDisturbance { get; set; }
        public int? NumberOfOccupants { get; set; }
        public int? TypeOfMaintenance { get; set; }
        public int? Accessibility { get; set; }
        public Guid? ManagementRecommendation { get; set; }
        public Guid? Priority { get; set; }
    }

    public class AddSampleToInspectionHandler : DapperRequestHandler<AddSampleToInspection, CommandResult>
    {
        public AddSampleToInspectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, AddSampleToInspection request)
        {
            await db.InsertAsync(new InspectionSampleDto
            {
                Id = request.SampleId,
                ImageCount = 0,
                InspectionId = request.InspectionId,
                SampleNotes = request.SampleNotes,
                SampleReference = request.SampleReference,
                Status = request.Status,
                FloorPlanColour = request.FloorPlanColour,
                Floor = request.Floor,
                FloorPlanReference = request.FloorPlanReference,
                RoomUsage = request.RoomUsage,
                Location = request.Location,
                Material = request.Material,
                MaterialColour = request.MaterialColour,
                Quantity = request.Quantity,
                QuantityUnit = request.QuantityUnit,
                ProductType = request.ProductType,
                ExtentOfDamage = request.ExtentOfDamage,
                SurfaceTreatment = request.SurfaceTreatment,
                AsbestosType = request.AsbestosType,
                OccupantActivity = request.OccupantActivity,
                LikelihoodOfDisturbance = request.LikelihoodOfDisturbance,
                NumberOfOccupants = request.NumberOfOccupants,
                TypeOfMaintenance = request.TypeOfMaintenance,
                Accessibility = request.Accessibility,
                ManagementRecommendation = request.ManagementRecommendation,
                Priority = request.Priority,
            });

            return CommandResult.Success();
        }
    }
}
