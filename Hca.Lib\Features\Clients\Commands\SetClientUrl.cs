﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class SetClientUrl : ICommand
    {
        public SetClientUrl(
            Guid clientId,
            string url)
        {
            ClientId = clientId;
            Url = url;
        }

        public string Url { get; }
        public Guid ClientId { get; }
    }

    public class SetClientUrlHandler : DapperRequestHandler<SetClientUrl, CommandResult>
    {
        public SetClientUrlHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, SetClientUrl request)
        {
            var dto = await db.GetAsync<ClientDto>(request.ClientId);
            dto.UrlSafeClientName = request.Url;
            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}
