﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Clients.Queries.Models;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Queries.Validation;

public class CanContactViewPropertyDocument : IQuery<ValidationResult>
{
    public CanContactViewPropertyDocument(Guid propertyDocumentId, Guid contactId)
    {
        PropertyDocumentId = propertyDocumentId;
        ContactId = contactId;
    }

    public Guid PropertyDocumentId { get; }
    public Guid ContactId { get; }
}

public class CanContactViewPropertyDocumentHandler : DapperRequestHandler<CanContactViewPropertyDocument, ValidationResult>
{
    public CanContactViewPropertyDocumentHandler(IDbHelper dbHelper) : base(dbHelper)
    {
    }

    public async override Task<ValidationResult> OnHandleAsync(IDbHelper db, CanContactViewPropertyDocument request)
    {
        var sql = $"SELECT COUNT(1) FROM {TableNames.PropertyDocuments} " +
            $"JOIN {TableNames.Properties} ON {TableNames.Properties}.{nameof(PropertyDto.Id)} = {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.PropertyId)} " +
            $"JOIN {TableNames.Contacts} ON {TableNames.Contacts}.{nameof(ContactDto.ClientId)} = {TableNames.Properties}.{nameof(PropertyDto.ClientId)} " +
            $"WHERE {TableNames.PropertyDocuments}.{nameof(PropertyDocumentDto.Id)} = @{nameof(request.PropertyDocumentId)} " +
            $"AND {TableNames.Contacts}.{nameof(ContactDto.Id)} = @{nameof(request.ContactId)}";
        var count = await db.ExecuteScalarAsync<int>(sql, request);
        return new ValidationResult { IsValid = count > 0 };
    }
}