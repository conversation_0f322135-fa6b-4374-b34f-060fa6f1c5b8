﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.TagHelpers;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers.Forms
{
    [HtmlTargetElement("select", Attributes = "row-label")]
    public class FormRowChoiceTagHelper : SelectTagHelper
    {
        private readonly HttpContext _httpContext;

        public FormRowChoiceTagHelper(
            IHtmlGenerator htmlGenerator,
            IHttpContextAccessor httpContextAccessor) : base(htmlGenerator)
        {
            _httpContext = httpContextAccessor.HttpContext;
        }

        [HtmlAttributeName("row-label")]
        public string HcaLabel { get; set; }

        [HtmlAttributeName("can-text-filter")]
        public bool HasTextFilter { get; set; }

        [HtmlAttributeName("read-only")]
        public bool ReadOnly { get; set; }

        [HtmlAttributeName("validation-content")]
        public string ValidationContent { get; set; }

        [HtmlAttributeName("asp-id")]
        public string Id { get; set; }

        [HtmlAttributeName("asp-style")]
        public string Style { get; set; }

        [HtmlAttributeName("asp-single-row")]
        public bool SingleRow { get; set; }

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            var preContent = $"<div class=\"form-group {(SingleRow ? "row" : "")}\" id=\"{Id}\" style=\"{Style}\">" +
                $"<label class=\"{(SingleRow ? "col-xl-2 col-form-label" : "text-bold")}\">{HcaLabel.HtmlEncode()}</label>";

            if (SingleRow) preContent += $"<div class=\"col-xl-10 input-group\">";

            var postContent = SingleRow ? "</div></div>" : "</div>";

            // OUTPUT LABEL IF NOT IN EDIT OR NEW MODE
            if (ReadOnly || !(_httpContext.IsEditMode() || _httpContext.IsNewMode()))
            {
                var text = "";
                var value = "";

                IEnumerable<SelectListItem> allItems = null;
                if (Items != null)
                {
                    if (Items is IEnumerable)
                    {
                        var enumerable = Items as IEnumerable;
                        if (Items is IEnumerable<SelectListItem>)
                            allItems = Items;
                        else
                            throw new InvalidOperationException(
                                $"The {nameof(IModelMetadataProvider)} was unable to provide metadata " +
                                $"about '{"ForAttributeName"}' expression value '{"For.Name"}' for <options>.");
                    }
                    else
                    {
                        throw new InvalidOperationException("Invalid items for <options>");
                    }

                    var selectedItem = allItems?.SingleOrDefault(i => i.Value == $"{For.Model}");

                    if (selectedItem != null)
                    {
                        text = selectedItem.Text;
                        value = selectedItem.Value;
                    }
                }

                var name = output.Attributes["name"].Value;
                var id = output.Attributes["id"].Value;

                output.Reinitialize("input", TagMode.StartTagAndEndTag);
                output.Attributes.SetAttribute("type", "hidden");
                output.Attributes.SetAttribute("value", value);
                output.Attributes.SetAttribute("name", name);
                output.Attributes.SetAttribute("id", id);

                // append a label
                preContent += $"<label class=\"form-control\">{text}</label>";

                // this clears out any additional options that may have been added
                output.Content.AppendHtml("");

                output.PreElement.SetHtmlContent(preContent);
                output.PostElement.SetHtmlContent(postContent);
                return;
            }

            if (output.Attributes.Any(a => a.Name == "class"))
            {
                var existingClass = output.Attributes["class"].Value;
                output.Attributes.Remove(output.Attributes["class"]);
                output.Attributes.Add("class", $"{existingClass} chosen-select form-control");
            }
            else
            {
                output.Attributes.Add("class", "chosen-select form-control");
            }

            // ENABLE SELECT2 TEXT FILTER?
            if (HasTextFilter)
            {
                postContent += @$"
<script>
  jQuery(function($) {{
    $('select#{output.Attributes["id"].Value}').select2({{
      theme: 'bootstrap4'
    }});
  }});
</script>";
            }

            output.TagMode = TagMode.StartTagAndEndTag;
            output.PreElement.SetHtmlContent(preContent);
            output.PostElement.SetHtmlContent(postContent);
        }

        private static TagHelperContext CreateTagHelperContext()
        {
            return new TagHelperContext(
                new TagHelperAttributeList(),
                new Dictionary<object, object>(),
                Guid.NewGuid().ToString("N"));
        }

        private static TagHelperOutput CreateTagHelperOutput(string tagName)
        {
            return new TagHelperOutput(
                tagName,
                new TagHelperAttributeList(),
                (a, b) =>
                {
                    var tagHelperContent = new DefaultTagHelperContent();
                    tagHelperContent.SetContent(string.Empty);
                    return Task.FromResult<TagHelperContent>(tagHelperContent);
                });
        }
    }
}
