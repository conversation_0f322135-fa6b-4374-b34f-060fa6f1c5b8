﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Queries
{
    public class GetUser : IQuery<UserDto>
    {
        public GetUser(Guid userId)
        {
            UserId = userId;
        }

        public Guid UserId { get; }
    }

    public class GetUserHandler : DapperRequestHandler<GetUser, UserDto>
    {
        public GetUserHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<UserDto> OnHandleAsync(IDbHelper db, GetUser request) => db.GetAsync<UserDto>(request.UserId);
    }
}
