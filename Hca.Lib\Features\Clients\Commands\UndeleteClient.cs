﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class UndeleteClient : ICommand
    {
        public Guid ClientId { get; }

        public UndeleteClient(Guid clientId)
        {
            ClientId = clientId;
        }
    }

    public class UndeleteClientHandler : DapperRequestHandler<UndeleteClient, CommandResult>
    {
        public UndeleteClientHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, UndeleteClient request)
        {
            var deletedDate = await db.ExecuteScalarAsync<DateTime?>(
                $"SELECT {nameof(ClientDto.Deleted)} " +
                $"FROM {TableNames.Clients} " +
                $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}",
                request);

            // undelete the client
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Clients} SET {nameof(ClientDto.Deleted)}=NULL " +
                $"WHERE {nameof(ClientDto.Id)}=@{nameof(request.ClientId)}", request);

            // undelete the sites
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET {nameof(SiteDto.Deleted)}=NULL " +
                $"WHERE {nameof(SiteDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(SiteDto.Deleted)}=@{nameof(deletedDate)}",
                new { request.ClientId, deletedDate });

            // undelete the properties
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET {nameof(PropertyDto.Deleted)}=NULL " +
                $"WHERE {nameof(PropertyDto.ClientId)}=@{nameof(request.ClientId)} " +
                $"AND {nameof(PropertyDto.Deleted)}=@{nameof(deletedDate)}",
                new { request.ClientId, deletedDate });

            return CommandResult.Success();
        }
    }
}
