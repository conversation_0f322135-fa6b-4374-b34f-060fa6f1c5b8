﻿@inject Hca.WebHost.Pipeline.ViewManager ViewManager

<!-- START Top Navbar-->
<nav class="navbar topnavbar navbar-expand-lg navbar-light">
    <!-- START navbar header-->
    <div class="navbar-header">
        <a class="navbar-brand" href="/">
            <div class="brand-logo">
                <tenant-logo class="img-fluid" default-logo-url="logo.png" style="max-height: 55px;" />
                @*<img class="img-fluid" src="~/images/logo.png" alt="App Logo">*@
            </div>
            <div class="brand-logo-collapsed">
                <img class="img-fluid" src="~/images/logo-single.png" alt="App Logo">
            </div>
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#topnavbar">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
    <!-- END navbar header-->
    <!-- START Nav wrapper-->
    <div class="navbar-collapse collapse" id="topnavbar">
        <!-- START Left navbar-->
        <ul class="nav navbar-nav mr-auto flex-column flex-lg-row"></ul>
        <!-- END Left navbar-->
        <!-- START Right Navbar-->
        <ul class="navbar-nav flex-row">
            <!-- Search icon-->
            @if (await ViewManager.IsHcaUser)
            {
                <li class="nav-item">
                    <a class="nav-link" href="#" data-search-open="">
                        <em class="icon-magnifier"></em>
                    </a>
                </li>
            }
            <!-- Fullscreen (only desktops)-->
            <li class="nav-item d-none d-md-block">
                <a class="nav-link" href="#" data-toggle-fullscreen="">
                    <em class="fa fa-expand"></em>
                </a>
            </li>
            <!-- START Offsidebar button-->
            @if (false && await ViewManager.IsHcaUser)
            {
                <li class="nav-item">
                    <a class="nav-link" href="#" data-toggle-state="offsidebar-open" data-no-persist="true">
                        <em class="icon-notebook"></em>
                    </a>
                </li>
            }
            <!-- END Offsidebar menu-->
        </ul>
        <!-- END Right Navbar-->
    </div>
    <!-- END Nav wrapper-->
    <!-- START Search form-->
    <form class="navbar-form" role="search" action="/search" method="get">
        <div class="form-group">
            <input class="form-control" name="searchTerm" type="text" placeholder="Type and hit enter ...">
            <div class="fa fa-times navbar-form-close" data-search-dismiss=""></div>
        </div>
        <button class="d-none" type="submit">Submit</button>
    </form>
    <!-- END Search form-->
</nav>
<!-- END Top Navbar-->
