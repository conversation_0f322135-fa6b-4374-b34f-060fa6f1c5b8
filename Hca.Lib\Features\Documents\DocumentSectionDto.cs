﻿using System;
using Dapper;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Documents
{
    [Table("tblDocumentSections")]
    public class DocumentSectionDto
    {
        public Guid Id { get; set; }

        public Guid DocumentId { get; set; }

        public SectionType SectionType { get; set; }

        public string SectionTitle { get; set; }

        public string SectionContent { get; set; }

        public int SectionOrder { get; set; }

        public bool SectionComplete { get; set; }

        public string SectionCode { get; set; }

        //public JObject DocumentContent { get; set; }
    }
}
