﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Azure.Blob;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using MediatR;

namespace Hca.Lib.Features.Documents.Commands
{
    public class DownloadDocument : IRequest<byte[]>
    {
        public DownloadDocument(Guid documentId)
        {
            DocumentId = documentId;
        }

        public Guid DocumentId { get; }
    }

    public class RetrieveDocumentHandler : DapperRequestHandler<DownloadDocument, byte[]>
    {
        private readonly IMediator _mediator;

        public RetrieveDocumentHandler(IDbHelper dbHelper, IMediator mediator) : base(dbHelper)
        {
            _mediator = mediator;
        }

        public override async Task<byte[]> OnHandleAsync(IDbHelper db, DownloadDocument request)
        {
            var documentDto = await db.GetAsync<DocumentDto>(request.DocumentId);
            return await _mediator.Send(new DownloadBlob(
                    documentDto.Folder,
                    documentDto.FileName
                ));
        }
    }
}
