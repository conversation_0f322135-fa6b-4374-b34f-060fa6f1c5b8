﻿using Refit;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Services.QRFileSpot;

internal interface IQrFileSpotOAuthService
{
    [Post("/oauth/token")]
    [Headers("Content-Type: application/x-www-form-urlencoded")]
    Task<OAuthResponse> OAuthToken([Body(BodySerializationMethod.UrlEncoded)] FormUrlEncodedContent content);
}

internal interface IQrFileSpotTokenService
{
    Task<string> GetTokenAsync();
}

internal class QrFileSpotTokenService : IQrFileSpotTokenService
{
    private readonly IQrFileSpotOAuthService _qrFileSpotOAuthService;
    private readonly string _clientId;
    private readonly string _clientSecret;
    private DateTime _tokenExpiryTime;
    private OAuthResponse _cachedToken;

    public QrFileSpotTokenService(string clientId, string clientSecret, IQrFileSpotOAuthService qrFileSpotOAuthService)
    {
        _clientId = clientId;
        _clientSecret = clientSecret;
        _qrFileSpotOAuthService = qrFileSpotOAuthService;
    }

    public async Task<string> GetTokenAsync()
    {
        // Check if the token is cached and not expired
        if (_cachedToken != null && DateTime.UtcNow < _tokenExpiryTime)
        {
            return _cachedToken.Access_Token;
        }

        // Token is not cached or expired, obtain a new token
        var contentKey = new List<KeyValuePair<string, string>>
        {
            new("client_id", _clientId),
            new("client_secret", _clientSecret),
        };

        var content = new FormUrlEncodedContent(contentKey);

        try
        {
            _cachedToken = await _qrFileSpotOAuthService.OAuthToken(content).ConfigureAwait(false);
            _tokenExpiryTime = DateTime.UtcNow.AddSeconds(_cachedToken.Expires_In);

            return _cachedToken.Access_Token;
        }
        catch
        {
            return null;
        }
    }
}

internal class TokenDelegatingHandler : DelegatingHandler
{
    private readonly IQrFileSpotTokenService _tokenProvider;

    public TokenDelegatingHandler(IQrFileSpotTokenService tokenProvider)
    {
        _tokenProvider = tokenProvider;
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        var token = await _tokenProvider.GetTokenAsync();
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return await base.SendAsync(request, cancellationToken);
    }
}