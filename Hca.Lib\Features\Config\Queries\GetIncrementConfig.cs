﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Config.Queries
{
    public class GetIncrementConfig : IQuery<ConfigDto>
    {
        public GetIncrementConfig(string configName)
        {
            ConfigName = configName;
        }

        public string ConfigName { get; }
    }

    public class GetIncrementConfigHandler : DapperRequestHandler<GetIncrementConfig, ConfigDto>
    {
        public GetIncrementConfigHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<ConfigDto> OnHandleAsync(IDbHelper db, GetIncrementConfig request) =>
            db.QuerySingleOrDefaultAsync<ConfigDto>(@$"UPDATE tblConfig  
SET IntValue = IntValue + 1
OUTPUT INSERTED.IntValue, INSERTED.StringValue
WHERE ConfigName = '{request.ConfigName}'");
    }
}
