﻿using System.Collections.Generic;

namespace Hca.WebHost.Pages.Shared.Widgets.Models
{
    public class SelectRowModel
    {
        public string Id { get; set; }

        public string Name { get; set; }

        public string Label { get; set; }

        public IEnumerable<(string Value, string Text)> Values { get; set; }

        public string SelectedValue { get; set; }

        public static SelectRowModel Create(
            string id,
            string name,
            string label,
            string selectedValue,
            IEnumerable<(string Value, string Text)> values = null)
        {
            return new SelectRowModel
            {
                Id = id,
                Name = name,
                Label = label,
                Values = values,
                SelectedValue = selectedValue,
            };
        }
    }
}
