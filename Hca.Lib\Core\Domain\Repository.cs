﻿using System;
using System.Threading.Tasks;

namespace Hca.Lib.Core
{
    public abstract class Repository<T> : IAggregateRepository<T> where T : IAggregate
    {
        protected readonly IEventPublisher _publisher;

        public Repository(IEventPublisher publisher)
        {
            _publisher = publisher;
        }

        public async Task SaveAsync(T aggregate)
        {
            var changes = aggregate.GetChanges();

            await PersistAsync(aggregate);

            foreach (var @event in changes)
            {
                await _publisher.Publish(@event);
            }

            aggregate.ClearChanges();
        }

        public abstract Task<T> LoadByIdAsync(EntityId id);

        protected abstract Task PersistAsync(T aggregate);

        public abstract Task BeginTransactionAsync();
        public abstract Task CommitTransactionAsync();
        public abstract Task RollbackTransactionAsync();
    }
}
