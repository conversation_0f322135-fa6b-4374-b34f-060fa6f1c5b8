﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetContactsByClientId : IQueryMany<ContactDto>
    {
        public GetContactsByClientId(Guid clientId) 
        {
            ClientId = clientId;
        }

        public Guid ClientId { get; }
    }

    public class GetContactsByClientIdHandler : DapperRequestHandler<GetContactsByClientId, DtoSet<ContactDto>>
    {
        public GetContactsByClientIdHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<ContactDto>> OnHandleAsync(IDbHelper db, GetContactsByClientId request)
        {
            var items = await db.GetListAsync<ContactDto>("WHERE ClientId=@ClientId", request);

            return DtoSet.From(items);
        }
    }
}
