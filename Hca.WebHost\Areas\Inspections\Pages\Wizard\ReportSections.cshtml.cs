﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Documents.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

public class ReportSectionsModel : HcaPageModel
{
    public ReportSectionsModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {

    }

    public IEnumerable<DocumentSectionDto> Sections { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid reportId, CancellationToken cancellationToken)
    {
        return new OkObjectResult((await _mediator.Send(new FindDocumentSections(reportId), cancellationToken)).Items);
    }
}
