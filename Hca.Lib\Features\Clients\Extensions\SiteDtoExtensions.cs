﻿using System.Collections.Generic;
using System.Linq;

namespace Hca.Lib.Features.Clients
{
    public static class SiteDtoExtensions
    {
        public static string GetDisplayText(this SiteDtoExtended site) => GetDisplayText(site, site.Address);

        public static string GetDisplayText(this SiteDtoExtended site, AddressDto addressDto)
        {
            var resultParts = new List<string>
            {
                site.SiteName,
                addressDto?.StreetName,
                addressDto?.Town,
                addressDto?.City,
                addressDto?.County,
                addressDto?.Country,
                addressDto?.Postcode,
            };

            return string.Join(", ", resultParts.Where(p => !p.IsNullOrWhiteSpace()).Select(p => p.Trim()));
        }
    }
}
