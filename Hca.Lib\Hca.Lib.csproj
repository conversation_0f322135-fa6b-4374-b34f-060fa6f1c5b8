﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>disable</Nullable>
	</PropertyGroup>

	<ItemGroup>
		<Folder Include="Core\" />
		<Folder Include="Core\Exceptions\" />
		<Folder Include="Features\Inspections\" />
		<Folder Include="Features\Inspections\Commands\" />
		<Folder Include="Features\Inspections\Queries\" />
		<Folder Include="Core\Domain\" />
		<Folder Include="Core\Commands\" />
		<Folder Include="Core\Events\" />
		<Folder Include="Core\Queries\" />
		<Folder Include="Features\Users\" />
		<Folder Include="Features\Users\Queries\" />
		<Folder Include="Features\Users\Commands\" />
		<Folder Include="Identity\" />
		<Folder Include="Identity\Stores\" />
		<Folder Include="Identity\Services\" />
		<Folder Include="Features\Projects\" />
		<Folder Include="Features\Projects\Commands\" />
		<Folder Include="Features\Projects\Queries\" />
		<Folder Include="Features\Documents\" />
		<Folder Include="Features\Documents\Publishing\" />
		<Folder Include="Features\Documents\Fields\" />
		<Folder Include="Features\Documents\Commands\" />
		<Folder Include="Features\Documents\Queries\" />
		<Folder Include="Features\Documents\Events\" />
		<Folder Include="Features\Inspections\Publishing\" />
		<Folder Include="Features\Projects\Documents\" />
		<Folder Include="Features\Projects\Documents\Commands\" />
		<Folder Include="Features\Projects\Documents\Queries\" />
		<Folder Include="Features\Documents\Templates\" />
		<Folder Include="Features\Projects\Documents\Publishing\" />
		<Folder Include="Features\Documents\Templates\Fields\" />
		<Folder Include="Features\Config\" />
		<Folder Include="Features\Config\Queries\" />
		<Folder Include="Features\Config\Commands\" />
		<Folder Include="Features\Projects\Queries\Models\" />
		<Folder Include="Features\Inspections\Queries\Models\" />
		<Folder Include="Features\Clients\Extensions\" />
		<Folder Include="Features\Inspections\Extensions\" />
	</ItemGroup>

	<ItemGroup>
		<!--<PackageReference Include="Auth0.AuthenticationApi" Version="7.24.0" />-->
		<PackageReference Include="Dapper" Version="2.0.123" />
		<PackageReference Include="Dapper.SimpleCRUD" Version="2.3.0" />
		<PackageReference Include="LazyCache" Version="2.4.0" />
		<PackageReference Include="Mediatr" Version="10.0.1" />
		<PackageReference Include="Microsoft.Azure.Services.AppAuthentication" Version="1.6.2" />
		<PackageReference Include="microsoft.entityframeworkcore.design" Version="6.0.6">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="microsoft.entityframeworkcore.sqlserver" Version="6.0.6" />
		<PackageReference Include="microsoft.entityframeworkcore.tools" Version="6.0.6">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
		<PackageReference Include="SixLabors.ImageSharp" Version="2.1.8" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="Azure.Identity" Version="1.11.1" />
		<!-- using 1.2.0 until the defaultazurecredential responds correctly -->
		<PackageReference Include="OpenXmlPowerToolsStandard" Version="5.0.145" />
		<PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="6.0.6" />
		<PackageReference Include="Refit.HttpClientFactory" Version="6.3.2" />
		<PackageReference Include="SendGrid" Version="9.28.0" />
		<PackageReference Include="SixLabors.ImageSharp.Web.Providers.Azure" Version="2.0.1" />
	</ItemGroup>
	<ItemGroup>
		<None Remove="Features\Projects\" />
		<None Remove="Features\Projects\Commands\" />
		<None Remove="Features\Projects\Queries\" />
		<None Remove="Features\Documents\" />
		<None Remove="Features\Documents\Publishing\" />
		<None Remove="Features\Documents\Fields\" />
		<None Remove="Features\Documents\Commands\" />
		<None Remove="Features\Documents\Queries\" />
		<None Remove="Features\Documents\Events\" />
		<None Remove="Features\Reports\PublishReport\" />
		<None Remove="Features\Inspections\Publishing\" />
		<None Remove="Features\Projects\Documents\" />
		<None Remove="Features\Projects\Documents\Commands\" />
		<None Remove="Features\Projects\Documents\Queries\" />
		<None Remove="Features\Documents\Templates\" />
		<None Remove="Features\Projects\Documents\Publishing\" />
		<None Remove="Features\Documents\Templates\Fields\" />
		<None Remove="Features\Config\" />
		<None Remove="Features\Config\Queries\" />
		<None Remove="Features\Config\Commands\" />
		<None Remove="Features\Projects\Queries\Models\" />
		<None Remove="Features\Clients\Queries\Models\" />
		<None Remove="Features\Inspections\Queries\Models\" />
		<None Remove="Features\Clients\Extensions\" />
		<None Remove="SendGrid" />
		<None Remove="Features\Inspections\Extensions\" />
		<None Remove="SixLabors.ImageSharp.Web.Providers.Azure" />
	</ItemGroup>
</Project>
