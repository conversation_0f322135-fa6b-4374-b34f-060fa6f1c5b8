﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateSectionFieldByIdQuery : IQuery<TemplateFieldDto>
    {
        public GetTemplateSectionFieldByIdQuery(Guid fieldId)
        {
            FieldId = fieldId;
        }

        public Guid FieldId { get; }
    }

    public class GetTemplateSectionFieldByIdQueryHandler : DapperRequestHandler<GetTemplateSectionFieldByIdQuery, TemplateFieldDto>
    {
        public GetTemplateSectionFieldByIdQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<TemplateFieldDto> OnHandleAsync(IDbHelper db, GetTemplateSectionFieldByIdQuery request)
        {
            return await db.QuerySingleOrDefaultAsync<TemplateFieldDto>(
                "SELECT * FROM tblTemplateFields WHERE Id = @FieldId",
                new { request.FieldId });
        }
    }
}
