﻿using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Hca.WebHost.Areas.Admin.Pages;

public class IndexModel : HcaPageModel
{
    public IndexModel(ViewManager viewManager, IMediator mediator) : base(mediator, viewManager)
    {
    }

    public async Task<IActionResult> OnGetAsync()
    {
        if (!await IsHcaUser) return Forbid();

        return Redirect("/users");
    }
}
