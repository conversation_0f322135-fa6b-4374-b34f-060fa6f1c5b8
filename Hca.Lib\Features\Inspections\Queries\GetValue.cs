﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetValue : IQuery<ValueDto>
    {
        public GetValue(Guid valueId)
        {
            ValueId = valueId;
        }

        public Guid ValueId { get; }
    }

    public class GetValueHandler : DapperRequestHandler<GetValue, ValueDto>
    {
        public GetValueHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<ValueDto> OnHandleAsync(IDbHelper db, GetValue request) =>
            db.GetAsync<ValueDto>(request.ValueId);
    }
}
