﻿using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Projects.Queries.Models;

namespace Hca.Lib.Features.Projects.Queries
{
    public class FindProjects : PagedQuery<ProjectQueryModel>
    {
    }

    public class FindProjectsHandler : DapperPagedQueryHandler<FindProjects, ProjectQueryModel>
    {
        public FindProjectsHandler(IDbHelper dbHelper) : base(dbHelper) { }

        protected override (string sql, object args) Build(FindProjects request)
        {
            var sql = @"
SELECT tblProjects.*, ClientName
FROM tblProjects
JOIN tblClients ON ClientId = tblClients.Id
ORDER BY Id DESC
OFFSET @Offset ROWS
FETCH NEXT @PageSize ROWS ONLY

SELECT COUNT(Id)
FROM tblProjects";

            return (sql, request);
        }
    }
}
