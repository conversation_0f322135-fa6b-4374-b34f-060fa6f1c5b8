﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel
@using Hca.Lib.Features.Inspections
@{
    var floorList = new SelectList(Model.Floors, nameof(ValueDto.Id), nameof(ValueDto.DisplayText), Model.FloorPlan.Floor);
}
<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">EDIT FLOOR PLAN</small>
    </div>
    <form method="post">
        <div class="card-body">
            <select asp-for="FloorPlan.Floor"
                    asp-items="floorList"
                    row-label="Floor">
                <option selected="selected" value="">Please select</option>
            </select>
            <textarea asp-for="FloorPlan.Notes" row-label="Notes" rows="6" />
            <input type="hidden" asp-for="FloorPlan.Id" />
        </div>
        <save-cancel-footer CancelUrl="@Urls.FloorPlans(Model.Client.Id, Model.Property.Id)"></save-cancel-footer>
    </form>
</div>
