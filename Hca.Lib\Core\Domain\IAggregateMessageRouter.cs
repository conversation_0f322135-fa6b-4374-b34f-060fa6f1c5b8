﻿using System;

namespace Hca.Lib.Core
{
    public interface IAggregateMessageRouter
    {
        void Route(AggregateMessage message);
    }

    public interface IAggregateMessageRouterWithConfig : IAggregateMessageRouter
    {
        void ConfigureRoute(Type message, Action<AggregateMessage> handler);
        void ConfigureRoute<TMessage>(Action<TMessage> handler) where TMessage : AggregateMessage;
    }
}