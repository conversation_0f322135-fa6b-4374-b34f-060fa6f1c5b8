﻿using System;
using System.Collections.ObjectModel;
using System.Linq;

namespace Hca.Lib.Core
{
    public class EntityCollection<TEntity> : Collection<TEntity> where TEntity : Entity
    {
        private readonly Aggregate aggregateRoot;

        public EntityCollection(Aggregate aggregateRoot)
        {
            this.aggregateRoot = aggregateRoot;
        }

        public TEntity FindById(EntityId id)
        {
            return this.SingleOrDefault(entity => entity.EntityId == id);
        }
    }
}
