﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Inspections;
using Hca.Lib.Features.Inspections.Queries.Models;
using Hca.Lib.Features.Projects.Queries.Models;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages;

public class InspectionModel : HcaPageModel
{
    private readonly InspectionService _inspectionService;
    private readonly ProjectService _projectService;
    private readonly ClientService _clientService;
    private readonly DocumentService _documentService;
    private readonly ValueListsService _valueListsService;

    public InspectionModel(
        ProjectService projectService,
        ClientService clientService,
        InspectionService inspectionService,
        DocumentService documentService,
        ValueListsService inspectionValuesService,
        ViewManager viewManager,
        IMediator mediator) : base(mediator, viewManager)
    {
        _projectService = projectService;
        _clientService = clientService;
        _inspectionService = inspectionService;
        _documentService = documentService;
        _valueListsService = inspectionValuesService;
    }

    public ProjectQueryModel Project { get; private set; }
    public ClientDto Client { get; private set; }
    public IEnumerable<ValueDto> InspectionTypes { get; private set; }
    public IEnumerable<PropertyDtoExtended> Properties { get; private set; }
    public IEnumerable<DocumentSectionDto> ReportSections { get; private set; }
    public bool IsNew { get; private set; }

    [BindProperty]
    public InspectionQueryModel Inspection { get; set; }

    public async Task OnGetAsync(Guid projectId, Guid clientId, string id, CancellationToken cancellationToken)
    {
        Project = await _projectService.GetProjectAsync(projectId, cancellationToken);
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (Guid.TryParse(id, out var inspectionId))
        {
            Inspection = await _inspectionService.GetAsync(inspectionId, cancellationToken);
            var reportDocuments = await _inspectionService.GetInspectionReports(inspectionId, cancellationToken);
            var report = reportDocuments.Single(); // todo: only dealing with one report per inspection atm
            ReportSections = await _documentService.GetDocumentSectionsAsync(report.DocumentId, cancellationToken);
        }
        else
        {
            InspectionTypes = await _valueListsService.GetAllAsync(ValueListType.InspectionType, cancellationToken);
            Properties = (await _clientService.GetPropertiesAsync(clientId, ComplianceStatus.ShowAll, cancellationToken)).Items;
            Inspection = new InspectionQueryModel { Id = Guid.NewGuid() };
            IsNew = true;
        }
    }

    public async Task<IActionResult> OnPostAsync(Guid projectId, Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            Project = await _projectService.GetProjectAsync(projectId, cancellationToken);
            Client = await _clientService.GetClientAsync(clientId, cancellationToken);
            InspectionTypes = await _valueListsService.GetAllAsync(ValueListType.InspectionType, cancellationToken);
            IsNew = true;

            return Page();
        }

        await _inspectionService.CreateInspectionAsync(
            Inspection.Id,
            Inspection.InspectionTypeId,
            Inspection.PropertyId,
            projectId,
            cancellationToken);

        return Redirect(Urls.ClientProjectInspection(clientId, projectId, Inspection.Id));
    }
}
