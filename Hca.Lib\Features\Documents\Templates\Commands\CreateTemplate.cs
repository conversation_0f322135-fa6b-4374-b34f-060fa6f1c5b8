﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Commands
{
    public class CreateTemplate : ICommand
    {
        public CreateTemplate(
            Guid templateId,
            string templateCode,
            TemplateType templateType,
            string templateName)
        {
            TemplateId = templateId;
            TemplateCode = templateCode;
            TemplateName = templateName;
            TemplateType = templateType;
        }

        public CreateTemplate(string templateCode, TemplateType templateType, string templateName) :
            this(Guid.NewGuid(), templateCode, templateType, templateName) { }

        public string TemplateName { get; set; }

        public Guid TemplateId { get; set; }
        public string TemplateCode { get; }
        public TemplateType TemplateType { get; set; }
    }

    public class CreateTemplateHandler : DapperRequestHandler<CreateTemplate, CommandResult>
    {
        public CreateTemplateHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, CreateTemplate request)
        {
            await db.InsertAsync(new TemplateDto
            {
                Id = request.TemplateId,
                TemplateCode = request.TemplateCode,
                TemplateName = request.TemplateName,
                TemplateType = request.TemplateType,
            });

            return CommandResult.Success();
        }
    }
}
