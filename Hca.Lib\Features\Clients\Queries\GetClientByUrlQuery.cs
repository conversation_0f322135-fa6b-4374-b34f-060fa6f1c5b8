﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Queries
{
    public class GetClientByUrlQuery : IQuery<ClientDto>
    {
        public GetClientByUrlQuery(string url)
        {
            Url = url;
        }

        public string Url { get; }
    }

    public class GetClientByUrlQueryHandler : DapperRequestHandler<GetClientByUrlQuery, ClientDto>
    {
        public GetClientByUrlQueryHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<ClientDto> OnHandleAsync(IDbHelper db, GetClientByUrlQuery request) =>
            db.QuerySingleOrDefaultAsync<ClientDto>(
               $"SELECT {TableNames.Clients}.* FROM {TableNames.Clients} WHERE {nameof(ClientDto.UrlSafeClientName)} = @{nameof(GetClientByUrlQuery.Url)}",
                request);
    }
}
