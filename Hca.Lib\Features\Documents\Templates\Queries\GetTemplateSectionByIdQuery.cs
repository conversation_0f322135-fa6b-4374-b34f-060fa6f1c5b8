﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateSectionByIdQuery : IQuery<TemplateSectionDto>
    {
        public GetTemplateSectionByIdQuery(Guid sectionId)
        {
            SectionId = sectionId;
        }

        public Guid SectionId { get; }
    }

    public class GetTemplateSectionByIdQueryHandler : DapperRequestHandler<GetTemplateSectionByIdQuery, TemplateSectionDto>
    {
        public GetTemplateSectionByIdQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override Task<TemplateSectionDto> OnHandleAsync(IDbHelper db, GetTemplateSectionByIdQuery request) =>
            db.GetAsync<TemplateSectionDto>(request.SectionId);
    }
}
