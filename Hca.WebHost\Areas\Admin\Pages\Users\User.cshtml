﻿@page "/users/{id}"
@inject Hca.WebHost.Services.UserService UserService
@model Hca.WebHost.Areas.Identity.Pages.UserModel

@{
    ViewData["Title"] = Model.NewUser ? "New User" : $"{Model.UserDto.LastName}, {Model.UserDto.FirstName}";
}

<div class="card card-default">
    <div class="card-header">
        <h5>USER</h5>
    </div>
    <div id="divUsers" class="card-body">
        <form method="post" enctype="multipart/form-data">
            <input type="hidden" asp-for="UserDto.Id" />
            <input asp-for="UserDto.FirstName" row-label="First Name" />
            <input asp-for="UserDto.LastName" row-label="Last Name" />
            <input asp-for="UserDto.Email" row-label="Email" />
            <input asp-for="UserDto.Position" row-label="Position" />
            <input asp-for="UserDto.OfficePhone" row-label="Office Phone" />
            <input asp-for="UserDto.MobilePhone" row-label="Mobile Phone" />
            <input asp-for="UserDto.Password" row-label="Password" />
            <input asp-for="Admin" row-label="Admin User?" />
            <input asp-for="UserPermissions.CanDelete" row-label="Can Delete Data?" />

            <div class="py-4">
                @if (string.IsNullOrWhiteSpace(Model.SignatureUrl))
                {
                    <p row-label="Signature">Signature not set</p>
                }
                else
                {
                    <img row-label="Signature" class="img-fluid img-thumbnail" style="height: 96px;" src="@Model.SignatureUrl" alt="Signature" />
                }
            </div>

            <div class="form-group row">
                <label class="col-xl-2 col-form-label" for="LogoUrl">Change Signature</label>
                <div class="col-xl-10">
                    <input type="hidden" asp-for="UserDto.SignatureFileName" />
                    <input class="form-control filestyle"
                           type="file"
                           data-classbutton="btn btn-secondary"
                           data-classinput="form-control inline"
                           data-icon="&lt;span class='fa fa-upload mr'&gt;&lt;/span&gt;"
                           asp-for="Signature" />
                </div>
            </div>

            <div class="py-4">
                @if (string.IsNullOrWhiteSpace(Model.ProfileImageUrl))
                {
                    <p row-label="Profile Image">Profile image not set</p>
                }
                else
                {
                    <img row-label="Profile Image" class="img-fluid img-thumbnail" style="height: 96px;" src="@Model.ProfileImageUrl" alt="Profile Image" />
                }
            </div>

            <div class="form-group row">
                <label class="col-xl-2 col-form-label" for="LogoUrl">Change Profile Image</label>
                <div class="col-xl-10">
                    <input type="hidden" asp-for="UserDto.ProfileImageFileName" />
                    <input class="form-control filestyle"
                           type="file"
                           data-classbutton="btn btn-secondary"
                           data-classinput="form-control inline"
                           data-icon="&lt;span class='fa fa-upload mr'&gt;&lt;/span&gt;"
                           asp-for="ProfileImage" />
                </div>
            </div>

            <save-cancel-footer EditButton="true"></save-cancel-footer>
        </form>
    </div>

    @section scripts {
        <script src="~/vendor/bootstrap-filestyle/src/bootstrap-filestyle.js"></script>

        <script>
            $(() => {
                drawBreadcrumb([{ url: '@Urls.Users', text: 'Users' }, { text: '@(Model.NewUser ? "New" : $"{Model.UserDto.LastName}, {Model.UserDto.FirstName}")' }]);
            });</script>
    }
