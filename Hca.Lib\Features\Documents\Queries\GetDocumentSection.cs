﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Documents.Queries
{
    public class GetDocumentSection : IQuery<DocumentSectionDto>
    {
        public GetDocumentSection(Guid sectionId)
        {
            SectionId = sectionId;
        }

        public Guid SectionId { get; }
    }

    public class GetDocumentSectionHandler : DapperRequestHandler<GetDocumentSection, DocumentSectionDto>
    {
        public GetDocumentSectionHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override Task<DocumentSectionDto> OnHandleAsync(IDbHelper db, GetDocumentSection request) =>
            db.GetAsync<DocumentSectionDto>(request.SectionId);
    }
}
