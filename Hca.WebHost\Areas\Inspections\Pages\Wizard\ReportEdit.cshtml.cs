﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Documents;
using Hca.Lib.Features.Documents.Fields;
using Hca.Lib.Features.Documents.Queries;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

[IgnoreAntiforgeryToken(Order = 1001)]
public class ReportEditModel : HcaPageModel
{
    public ReportEditModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    [BindProperty]
    public SectionModel SectionModel { get; set; }

    public async Task OnGetAsync(Guid reportId, Guid sectionId, CancellationToken cancellationToken)
    {
        SectionModel = new SectionModel
        {
            Section = await _mediator.Send(new GetDocumentSection(sectionId), cancellationToken),
            //Fields = (await _mediator.Send(new FindDocumentFields(sectionId), cancellationToken))
            //    .Items
            //    .Select(f => null) // todo: this won't work
            //    .ToArray(),
        };
    }

    public Task<IActionResult> OnPostAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult<IActionResult>(new OkResult());
    }
}

public class SectionModel
{
    public DocumentSectionDto Section { get; set; }

    //[ModelBinder(BinderType = typeof(ReportFieldModelBinder))]
    public IDocumentField<Field>[] Fields { get; set; }
}

//public class ReportFieldModelBinder : IModelBinder
//{

//    public Task BindModelAsync(ModelBindingContext bindingContext)
//    {
//        if (bindingContext == null)
//            throw new ArgumentNullException(nameof(bindingContext));

//        ValueProviderResult values = bindingContext.ValueProvider.GetValue(nameof(ReportFieldDto.SpecialisedType));
//        if (values.Length == 0)
//            return Task.CompletedTask;

//        string typeString = values.FirstValue;
//        Type type = Type.GetType(typeString, true);
//        //    "Magic.Core.Models." + typeString + ", Magic.Core.Models",
//        //    true
//        //);

//        object model = Activator.CreateInstance(type);

//        //*get form values from provider
//        var field = model as ReportFieldDto;
//        if (field != null)
//        {
//            //var provider = bindingContext.ValueProvider;

//            //var contentType = provider.GetValue("ContentType");
//            //content.ContentType = contentType != ValueProviderResult.None ? contentType.ToString() : string.Empty;

//            //var name = provider.GetValue("Name");
//            //content.Name = name != ValueProviderResult.None ? name.ToString() : string.Empty;

//            var infoList = type.GetProperties();
//            var provider = bindingContext.ValueProvider;
//            foreach (var info in infoList)
//            {
//                var val = provider.GetValue(info.Name).FirstValue;
//                info.SetValue(model, val, null);
//            }
//        }
//        //*/

//        var metadataProvider = (IModelMetadataProvider)bindingContext.HttpContext.RequestServices.GetService(typeof(IModelMetadataProvider));
//        bindingContext.ModelMetadata = metadataProvider.GetMetadataForType(type);
//        bindingContext.Result = ModelBindingResult.Success(model);

//        return Task.CompletedTask;
//    }
//}
