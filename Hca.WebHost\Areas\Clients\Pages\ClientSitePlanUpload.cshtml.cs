﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2010.Excel;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSitePlanUploadModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSitePlanUploadModel(
        IMediator mediator,
        ViewManager viewManager,
        ClientService clientService) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    [BindProperty]
    public IFormFile UploadFile { get; set; }
    [BindProperty]
    public SiteDtoExtended Site { get; set; }
    [BindProperty]
    public SitePlanDto Plan { get; set; }
    public ClientDto Client { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Client = await _clientService.GetClientAsync(clientId, cancellationToken);
        Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, Guid siteId, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();

            valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (!ModelState.IsValid || UploadFile == null)
        {
            ModelState.AddModelError("", "Must select a file for upload");
            Client = await _clientService.GetClientAsync(clientId, cancellationToken);
            Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);

            return Page();
        }

        using var ms = new MemoryStream();
        UploadFile.CopyTo(ms);

        await _clientService.UploadSitePlanAsync(
            clientId,
            siteId,
            ms.ToArray(),
            UploadFile.ContentType,
            UploadFile.FileName,
            Plan.Notes,
            cancellationToken);

        return Redirect(Urls.ClientSitePlans(clientId, siteId));
    }
}
