﻿using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Services;
using System;
using System.Threading.Tasks;

namespace Hca.Lib.Features.Clients.Commands
{
    public class ReinstateSite : ICommand
    {
        public ReinstateSite(Guid clientId, Guid siteId)
        {
            ClientId = clientId;
            SiteId = siteId;
        }

        public Guid ClientId { get; }
        public Guid SiteId { get; }
    }

    public class ReinstateSiteHandler : DapperRequestHandler<ReinstateSite, CommandResult>
    {
        private readonly ClientCountsService _clientCountsService;

        public ReinstateSiteHandler(IDbHelper dbHelper, ClientCountsService clientCountsService) : base(dbHelper)
        {
            _clientCountsService = clientCountsService;
        }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, ReinstateSite request)
        {
            var site = await db.GetAsync<SiteDto>(request.SiteId);
            var archivedDate = site.Archived;

            // restore the site
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Sites} SET " +
                $"{nameof(SiteDto.Archived)} = NULL, " +
                $"{nameof(SiteDto.ArchiveReason)} = NULL " +
                $"WHERE {nameof(SiteDto.Id)} = @{nameof(request.SiteId)}", request);

            // restore the properties
            await db.ExecuteAsync(
                $"UPDATE {TableNames.Properties} SET " +
                $"{nameof(PropertyDto.Archived)} = NULL, " +
                $"{nameof(PropertyDto.ArchiveReason)} = NULL " +
                $"WHERE {nameof(PropertyDto.SiteId)} = @{nameof(request.SiteId)} " +
                $"AND {nameof(PropertyDto.Archived)} = @{nameof(archivedDate)}",
                new { request.SiteId, archivedDate });

            _clientCountsService.ClearClientCountsAsync(site.ClientId);

            return CommandResult.Success();
        }
    }
}
