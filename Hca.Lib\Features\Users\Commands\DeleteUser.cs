﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Users.Commands
{
    public class DeleteUser : ICommand
    {
        public DeleteUser(Guid userId)
        {
            UserId = userId;
        }

        public Guid UserId { get; }
    }

    public class DeleteUserHandler : DapperRequestHandler<DeleteUser, CommandResult>
    {
        public DeleteUserHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteUser request)
        {
            await db.DeleteAsync<UserDto>(request.UserId);

            return CommandResult.Success();
        }
    }
}
