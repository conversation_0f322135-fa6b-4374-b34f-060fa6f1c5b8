﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Inspections;

namespace Hca.Lib.Features.Clients.Domain.Inspections.Commands
{
    public class DeleteValue : ICommand
    {
        public DeleteValue(Guid id) 
        {
            Id = id;
        }

        public Guid Id { get; }
    }

    public class DeleteValueHandler : DapperRequestHandler<DeleteValue, CommandResult>
    {
        public DeleteValueHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, DeleteValue request)
        {
            await db.DeleteAsync<ValueDto>(request.Id);
            return CommandResult.Success();
        }
    }
}
