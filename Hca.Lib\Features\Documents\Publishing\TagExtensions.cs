﻿using System.Collections.Generic;
using System.Linq;
using OpenXmlPowerTools;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class TagExtensions
    {
        public static CompiledDocument ReplaceTags(this CompiledDocument doc, IEnumerable<(string Tag, string Text)> tags)
        {
            if (!tags?.Any() ?? true) return doc;

            using var wordDoc = doc.AsWordProcessingDocument;

            foreach(var (Tag, Text) in tags)
            {
                try
                {
                    TextReplacer.SearchAndReplace(wordDoc, "{{" + Tag + "}}", Text, true);
                }
                catch
                {
                    // element not found
                }

                //SearchAndReplacer.SearchAndReplace(wordDoc, "{{" + Tag + "}}", Text, true);
            }

            return doc;
        }
    }
}
