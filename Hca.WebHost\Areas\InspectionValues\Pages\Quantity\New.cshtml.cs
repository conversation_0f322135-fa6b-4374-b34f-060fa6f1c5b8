﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Quantity
{
    public class New : InspectionValueCreatePage
    {
        public New(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override   ValueListType  ValueListType =>   ValueListType.Quantity;

        public override string IndexUrl => Urls.Quantity;
    }
}
