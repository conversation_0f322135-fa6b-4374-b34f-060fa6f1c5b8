﻿using MediatR;
using System.Threading;
using System.Threading.Tasks;

namespace Hca.Lib.Core
{
    public class EventPublisher : IEventPublisher
    {
        private readonly IMediator _mediator;

        public EventPublisher(IMediator mediator)
        {
            _mediator = mediator;
        }

        public async Task Publish(IEvent @event)
        {
            if (@event is INotification)
            {
                await _mediator.Publish(@event as INotification, CancellationToken.None);
            }
        }
    }
}
