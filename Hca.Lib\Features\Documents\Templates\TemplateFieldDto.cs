﻿using System;
using Dapper;
using Hca.Lib.Features.Documents.Fields;

namespace Hca.Lib.Features.Templates
{
    [Table("tblTemplateFields")]
    public class TemplateFieldDto
    {
        public Guid Id { get; set; }

        public Guid TemplateSectionId { get; set; }

        public FieldType FieldType { get; set; }

        public string FieldContent { get; set; }

        public int FieldOrder { get; set; }

        public bool Optional { get; set; }
    }
}
