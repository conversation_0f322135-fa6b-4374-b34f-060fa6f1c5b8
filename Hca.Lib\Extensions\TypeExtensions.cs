﻿using System;

namespace Hca
{
    public static class TypeExtensions
    {
        //todo: any need to include the source type as a generic parameter?
        //todo: make a generic root object for both template and report fields
        public static T Convert<T, X>(this X result)
        {
            var derivedClassInstance = Activator.CreateInstance<T>();
            var derivedType = derivedClassInstance.GetType();

            var properties = result.GetType().GetProperties();
            foreach (var property in properties)
            {
                var propToSet = derivedType.GetProperty(property.Name);
                if (propToSet.SetMethod != null)
                {
                    propToSet.SetValue(derivedClassInstance, property.GetValue(result));
                }
            }
            return derivedClassInstance;
        }

        public static T Convert<T>(this object result)
        {
            var derivedClassInstance = Activator.CreateInstance<T>();
            var derivedType = derivedClassInstance.GetType();

            var properties = result.GetType().GetProperties();
            foreach (var property in properties)
            {
                var propToSet = derivedType.GetProperty(property.Name);
                if (propToSet.SetMethod != null)
                {
                    propToSet.SetValue(derivedClassInstance, property.GetValue(result));
                }
            }
            return derivedClassInstance;
        }
    }
}
