﻿using System;
using System.Collections.Generic;

namespace Hca.Lib.Core
{
    public class Router<T, TOut>
    {
        readonly Dictionary<Type, Func<T, TOut>> _handlers = new();

        public void Register(Type routableObjectType, Func<T, TOut> handler)
        {
            if (routableObjectType == null) throw new ArgumentNullException(nameof(routableObjectType));
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            _handlers.Add(routableObjectType, handler);
        }

        public void Register<TRoutableObject>(Func<TRoutableObject, TOut> handler) where TRoutableObject : T
        {
            if (handler == null) throw new ArgumentNullException(nameof(handler));
            _handlers.Add(typeof(TRoutableObject), routableObject => handler((TRoutableObject)routableObject));
        }

        public TOut Map(T routableObject)
        {
            if (routableObject == null) throw new ArgumentNullException(nameof(routableObject));

            if (_handlers.TryGetValue(routableObject.GetType(), out Func<T, TOut> handler))
            {
                return handler(routableObject);
            }

            return default;
        }
    }
}
