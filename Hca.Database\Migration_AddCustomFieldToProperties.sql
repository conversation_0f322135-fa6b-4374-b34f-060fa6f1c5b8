-- Migration Script: Add Custom field to tblProperties table
-- Date: 2025-01-29
-- Description: Adds a new Custom field to store 'Landlord Areas' or 'Client Specified Areas'

-- Check if the Custom column already exists before adding it
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tblProperties' 
    AND COLUMN_NAME = 'Custom'
)
BEGIN
    -- Add the Custom column to tblProperties
    ALTER TABLE [dbo].[tblProperties]
    ADD [Custom] NVARCHAR(MAX) NULL;
    
    PRINT 'Custom column added to tblProperties table successfully.';
END
ELSE
BEGIN
    PRINT 'Custom column already exists in tblProperties table.';
END

-- Optional: Add a comment or description for the column (SQL Server 2012+)
-- This helps document the purpose of the field
IF NOT EXISTS (
    SELECT * FROM sys.extended_properties 
    WHERE major_id = OBJECT_ID('dbo.tblProperties') 
    AND minor_id = (SELECT column_id FROM sys.columns WHERE object_id = OBJECT_ID('dbo.tblProperties') AND name = 'Custom')
    AND name = 'MS_Description'
)
BEGIN
    EXEC sys.sp_addextendedproperty 
        @name = N'MS_Description',
        @value = N'Custom field for storing Landlord Areas or Client Specified Areas',
        @level0type = N'SCHEMA',
        @level0name = N'dbo',
        @level1type = N'TABLE',
        @level1name = N'tblProperties',
        @level2type = N'COLUMN',
        @level2name = N'Custom';
        
    PRINT 'Description added to Custom column.';
END

-- Verify the column was added successfully
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'tblProperties' 
AND COLUMN_NAME = 'Custom';

PRINT 'Migration completed successfully.';