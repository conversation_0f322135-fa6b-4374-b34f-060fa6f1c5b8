﻿using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Core;
using MediatR;

namespace Hca.Lib.Data.Core
{
    public abstract class DapperEventHandler<TReq> : INotificationHandler<TReq> where TReq : IEvent
    {
        private readonly IDbHelper _dbHelper;

        public DapperEventHandler(IDbHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        public async Task Handle(TReq request, CancellationToken cancellationToken)
        {
            await OnHandleAsync(_dbHelper, request);
        }

        public abstract Task OnHandleAsync(IDbHelper db, TReq request);
    }
}