﻿
using System;
using System.IO;
using System.Linq;

using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;

namespace Hca.Lib.Features.Documents.Publishing
{
    public static class OpenXmlExtensions
    {
        public static TableCell AppendText(this TableCell cell, string text)
        {
            cell.Append(new Paragraph(new Run(new Text(text))));
            return cell;
        }

        public static TableCell AppendImage(this TableCell cell, Drawing drawing)
        {
            cell.Append(new Paragraph(new Run(drawing)));
            return cell;
        }

        public static TableRow AppendCell(this TableRow row, TableCell cell)
        {
            row.Append(cell);
            return row;
        }

        public static TableRow AppendCell(this TableRow row, string text)
        {
            return row.AppendCell(new TableCell().AppendText(text));
        }

        public static TableRow AppendImageCell(this TableRow row, string imageReference)
        {
            return row.AppendCell(new TableCell().AppendImage(OpenXmlFactory.CreateDrawing(imageReference)));
        }

        public static Table AppendRow(this Table table, TableRow row)
        {
            table.Append(row);
            return table;
        }

        public static TableRow AppendRow(this Table table)
        {
            var row = new TableRow();
            table.Append(row);
            return row;
        }

        public static string CreateImage(this MainDocumentPart mainDocumentPart, byte[] image, string imageName)
        {
            var type = imageName.Split('.').Last().ToLower() switch
            {
                "png" => ImagePartType.Png,
                "jpg" => ImagePartType.Jpeg,
                _ => throw new NotImplementedException(),
            };

            return CreateImage(mainDocumentPart, image, type);
        }

        public static string CreateImage(this MainDocumentPart mainDocumentPart, byte[] image, ImagePartType type)
        {
            var imagePart = mainDocumentPart.AddImagePart(type);
            using var stream = new MemoryStream(image);
            imagePart.FeedData(stream);
            return mainDocumentPart.GetIdOfPart(imagePart);
        }

        public static Text FindFirstTextElement(this MainDocumentPart mainDocumentPart, string text)
        {
            return mainDocumentPart.Document.Body
                .Descendants<Text>()
                .Where(x => x.Text.Contains(text))
                .FirstOrDefault();
        }

        public static Paragraph Heading1(this Paragraph paragraph)
        {
            paragraph.ParagraphProperties = new ParagraphProperties(new ParagraphStyleId() { Val = "Heading1" });
            return paragraph;
        }
    }
}
