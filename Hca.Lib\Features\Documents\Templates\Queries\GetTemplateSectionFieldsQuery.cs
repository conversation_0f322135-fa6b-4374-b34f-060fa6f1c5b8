﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplateSectionFieldsQuery : IQueryMany<TemplateFieldDto>
    {
        public GetTemplateSectionFieldsQuery(Guid templateId, Guid sectionId)
        {
            TemplateId = templateId;
            SectionId = sectionId;
        }

        public Guid SectionId { get; }
        public Guid TemplateId { get; }
    }

    public class GetTemplateSectionFieldsQueryHandler : DapperRequestHandler<GetTemplateSectionFieldsQuery, DtoSet<TemplateFieldDto>>
    {
        public GetTemplateSectionFieldsQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<TemplateFieldDto>> OnHandleAsync(IDbHelper db, GetTemplateSectionFieldsQuery request)
        {
            return DtoSet.From(await db.GetListAsync<TemplateFieldDto>(
                "WHERE TemplateSectionId = @SectionId " +
                "ORDER BY FieldOrder ASC",
                new { request.TemplateId, request.SectionId }));
        }
    }
}
