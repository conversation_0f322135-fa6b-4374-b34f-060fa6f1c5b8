﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Commands
{
    public class RemoveTemplateFile : ICommand
    {
        public RemoveTemplateFile(Guid templateId, Guid fileId)
        {
            TemplateId = templateId;
            FileId = fileId;
        }

        public Guid TemplateId { get; }
        public Guid FileId { get; }
    }

    public class RemoveTemplateFileHandler : DapperRequestHandler<RemoveTemplateFile, CommandResult>
    {
        public RemoveTemplateFileHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, RemoveTemplateFile request)
        {
            await db.DeleteAsync<TemplateFileDto>(request.FileId);

            return CommandResult.Success();
        }
    }
}
