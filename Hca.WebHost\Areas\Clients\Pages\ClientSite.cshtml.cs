﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients;
using Hca.Lib.Features.Clients.Queries.Validation;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using Hca.WebHost.Services;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Identity.Client;

namespace Hca.WebHost.Areas.Clients.Pages;

public class ClientSiteModel : HcaPageModel
{
    private readonly ClientService _clientService;

    public ClientSiteModel(
        ClientService clientService,
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    {
        _clientService = clientService;
    }

    [BindProperty]
    public AddressDto Address { get; set; }
    [BindProperty]
    public SiteDtoExtended Site { get; set; }
    public string Id { get; private set; }
    public ClientDto Client { get; private set; }

    public async Task<IActionResult> OnGetAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        if (Guid.TryParse(id, out var siteId))
        {
            if (!await IsHcaUser)
            {
                var valid = await _mediator.Send(new CanContactViewSite(siteId, ContactId), cancellationToken);
                if (!valid.IsValid) return Forbid();
            }

            Site = await _clientService.GetSiteAsync(clientId, siteId, cancellationToken);

            if (Site.AddressId.HasValue)
            {
                Address = await _clientService.GetAddressAsync(Site.AddressId.Value, cancellationToken);
            }
            else
            {
                // model needs an address id to accept the save attempt
                Address = new();
            }
        }
        else
        {
            Address = new AddressDto();
            Site = new SiteDtoExtended();
        }

        return Page();
    }

    public async Task<IActionResult> OnPostAsync(Guid clientId, string id, CancellationToken cancellationToken)
    {
        if (!await IsHcaUser)
        {
            var valid = await _mediator.Send(new CanContactViewClient(clientId, ContactId), cancellationToken);
            if (!valid.IsValid) return Forbid();
        }

        Id = id;
        Client = await _clientService.GetClientAsync(clientId, cancellationToken);

        if (!ModelState.IsValid)
        {
            return Page();
        }

        if (id.ToLower() == "new")
        {
            await _clientService.AddSiteAsync(clientId, Site, Address, cancellationToken);
        }
        else
        {
            if (!await IsHcaUser)
            {
                var valid = await _mediator.Send(new CanContactViewSite(Site.Id, ContactId), cancellationToken);
                if (!valid.IsValid) return Forbid();
            }

            await _clientService.UpdateSiteAsync(clientId, Site, Address, cancellationToken);
        }

        return Redirect(Urls.ClientSites(clientId));
    }
}
