﻿using System;
using System.Linq;
using Microsoft.AspNetCore.Http;

namespace Hca.WebHost
{
    public static class HttpContextExtensions
    {
        public static bool IsAjaxRequest(this HttpContext context)
        {
            if (context?.Request == null)
                throw new ArgumentNullException(nameof(context));

            if (context.Request.Headers != null)
                return !string.IsNullOrEmpty(context.Request.Headers["X-Requested-With"]) &&
                    string.Equals(
                        context.Request.Headers["X-Requested-With"],
                        "XmlHttpRequest",
                        StringComparison.OrdinalIgnoreCase);

            return false;
        }

        public static bool IsEditMode(this HttpContext context) =>
            context.Request.Query[Constants.QuerystringModeKey].ToString().ToLower() == Constants.EditMode.ToLower();

        public static bool IsNewMode(this HttpContext context) =>
            context.Request.Path.Value.ToLower().EndsWith("/new")
            || context.Request.Query[Constants.QuerystringModeKey].ToString().ToLower() == Constants.NewMode.ToLower();

        public static string GetTenantPath(this HttpContext context) => context.Request.RouteValues["tenant"]?.ToString();
    }
}
