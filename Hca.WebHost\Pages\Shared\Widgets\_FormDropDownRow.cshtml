﻿@model Models.SelectRowModel
@{
    var unselected = string.IsNullOrWhiteSpace(Model.SelectedValue) ? "selected" : null;
}
@{ #pragma warning disable 1998 }
@functions {
    string Selected(string value) =>
        Model.SelectedValue == value ? "selected" : null;

    async Task RenderOption(string value, string text)
    {
        <option selected="@Selected(value)" value="@value">@text</option>
    }
}
<div class="form-group row">
    <label class="col-md-2 col-form-label">@Model.Label</label>
    <div class="col-md-10">
        <select id="@Model.Id" name="@Model.Name" class="form-control">
            <option selected="@unselected" value="">Please select</option>
            @foreach (var value in Model.Values)
            {
                await RenderOption(value.Value, value.Text);
            }
        </select>
    </div>
</div>