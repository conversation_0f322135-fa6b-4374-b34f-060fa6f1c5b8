﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Inspections.Queries
{
    public class GetValuesByType : IQueryMany<ValueDto>
    {
        public GetValuesByType(ValueListType type)
        {
            Type = type;
        }

        public ValueListType Type { get; }
    }

    public class GetValuesByTypeHandler : DapperRequestHandler<GetValuesByType, DtoSet<ValueDto>>
    {
        public GetValuesByTypeHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DtoSet<ValueDto>> OnHandleAsync(IDbHelper db, GetValuesByType request)
        {
            return DtoSet.From(await db.GetListAsync<ValueDto>(
                $"WHERE {nameof(ValueDto.ValueListType)}=@{nameof(request.Type)}",
                request));
        }
    }
}
