﻿@page "/inspections/{inspectionId:guid}"
@using Hca.WebHost.Areas.Inspections.Models
@model Hca.WebHost.Areas.Inspections.Pages.ExistingModel

<div class="content-heading">
    <div>
        Inspection for @Model.Inspection.ClientName, @Model.Inspection.PropertyCode
        <small>Started @Model.Inspection.Created.ToString("HH:mm ddd dd MMMM, yyyy")</small>
    </div>
</div>

<div class="card card-default">
    <div class="card-body">
        <partial name="Wizard/_InspectionWizard" model="new InspectionWizardModel { Stage = InspectionMenuStage.Details, Inspection = Model.Inspection }" />
    </div>
</div>


@section scripts {
    @Html.Raw(Model.WriteScriptBlocks())
}