﻿@model Hca.WebHost.Areas.Clients.Pages.Property.PropertyIndexModel

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">FLOOR PLANS</small>
    </div>
    <div class="card-body">
        <div class="row">
            @if (Model.FloorPlans.Any())
            {
                <table class="table table-striped table-bordered table-hover">
                    <thead class="thead-dark">
                        <tr>
                            <th></th>
                            <th>Floor</th>
                            <th>Notes</th>
                            <th style="width: 20px;"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var floorPlan in Model.FloorPlans)
                        {
                            var url = await Model.GetFloorPlanUrl(floorPlan);
                            var thumbnailUrl = floorPlan.ThumbnailName.IsEmpty() ? "/images/no-preview.jpg" : await Model.GetFloorPlanThumbnailUrl(floorPlan);

                            <tr style="cursor: pointer;"
                                onclick="window.location.href='@Urls.FloorPlan(Model.Client.Id, Model.Property.Id, floorPlan.Id).AddEditMode()';">
                                <td>
                                    <a class="text-muted mr-1" href="@url" title="Download"><img src="@thumbnailUrl" height="100" /></a>
                                    <a class="text-muted mr-1" href="@url" title="Download"><em class="fa fa-download fa-fw"></em></a>
                                </td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 100px;">@floorPlan.FloorName</td>
                                <td style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden; max-width: 250px;">
                                    @floorPlan.Notes
                                </td>
                                <td style="vertical-align: middle;">
                                    <delete action="@Urls.FloorPlanDelete(Model.Client.Id, Model.Property.Id, floorPlan.Id)" item-name="floor plan"></delete>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
            else
            {
                <div class="col">
                    <p>No floor plans uploaded</p>
                </div>
            }
        </div>
    </div>
</div>