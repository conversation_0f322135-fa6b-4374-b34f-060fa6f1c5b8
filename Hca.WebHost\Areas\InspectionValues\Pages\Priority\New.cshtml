﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Priority.New
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valueModel = new ValueModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Priorty Name" },
            { nameof( ValueDto.HintText), "Hint Text" },
        },
        Header = "Priority",
        Value = Model.Value,
    };

}

<partial name="../Widgets/_InspectionValuePartial" model="valueModel" />