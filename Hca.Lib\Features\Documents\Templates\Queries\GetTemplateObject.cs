﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Templates.Fields;
using Hca.Lib.Features.Templates;

namespace Hca.Lib.Features.Documents.Templates.Queries
{
    public class GetTemplateObject : IQuery<TemplateObject>
    {
        public GetTemplateObject(Guid templateId)
        {
            TemplateId = templateId;
        }

        public Guid TemplateId { get; }
    }

    public class GetTemplateObjectHandler : DapperRequestHandler<GetTemplateObject, TemplateObject>
    {
        public GetTemplateObjectHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<TemplateObject> OnHandleAsync(IDbHelper db, GetTemplateObject request)
        {
            var templateDto = await db.GetAsync<TemplateDto>(request.TemplateId);
            var templateFileDtos = await db.GetListAsync<TemplateFileDto>("WHERE TemplateId = @TemplateId", request);
            var templateSectionDtos = await db.GetListAsync<TemplateSectionDto>("WHERE TemplateId = @TemplateId", request);
            var templateFieldDtos = await db.QueryAsync<TemplateFieldDto>(
                "SELECT tblTemplateFields.* " +
                "FROM tblTemplateFields " +
                "JOIN tblTemplateSections ON TemplateSectionId = tblTemplateSections.Id " +
                "WHERE TemplateId = @TemplateId", request);

            return new TemplateObject
            {
                Files = templateFileDtos,
                Id = templateDto.Id,
                TemplateCode = templateDto.TemplateCode,
                TemplateName = templateDto.TemplateName,
                TemplateType = templateDto.TemplateType,
                Sections = TemplateUtils.BuildTemplateSections(templateSectionDtos, templateFieldDtos),
            };
        }
    }
}
