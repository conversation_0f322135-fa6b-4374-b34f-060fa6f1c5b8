﻿@model TemplateSelectViewModel

<h2>Template</h2>
<p>Customise a report template</p>

@Html.DropDownList(
    "TemplateId",
    Model.Templates.Select(_ => new SelectListItem { Text = _.TemplateName, Value = _.Id.ToString() }),
    "Select Template",
    new { @class = "dropdown" })
<button class="btn btn-primary" id="btnLoad">Load</button>
<div id="divTemplate"></div>

@section scripts{
    <script>$(function () {
            $('#btnLoad').on('click', function () {
                $('#divTemplate').load('/ajaxpartial?handler=CarPartial');
            });
        });</script>
}
