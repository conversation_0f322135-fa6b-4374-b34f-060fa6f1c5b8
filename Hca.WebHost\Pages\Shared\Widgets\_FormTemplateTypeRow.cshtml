﻿@model Models.SelectRowModel
@using Hca.Lib.Features.Templates
@{
    var unselected = string.IsNullOrWhiteSpace(Model.SelectedValue) ? "selected" : null;
}
@{ #pragma warning disable 1998 }
@functions {
    string Selected(Hca.Lib.Features.Templates.TemplateType value) =>
        Model.SelectedValue == ((int)value).ToString() ? "selected" : null;

    async Task RenderOption(TemplateType templateType)
    {
        <option selected="@Selected(templateType)" value="@templateType">@templateType.DisplayName()</option>
    }
}
<div class="form-group row">
    <label class="col-md-2 col-form-label">Inspection Type</label>
    <div class="col-md-10">
        <select id="@Model.Id" name="@Model.Name" class="form-control">
            <option selected="@unselected" value="">Please select</option>
            @RenderOption(TemplateType.Report)
            @RenderOption(TemplateType.Quote)
        </select>
    </div>
</div>