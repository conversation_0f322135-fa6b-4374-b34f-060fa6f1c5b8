﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetTemplate : IQuery<TemplateDto>
    {
        public GetTemplate(Guid templateId)
        {
            TemplateId = templateId;
        }

        public Guid TemplateId { get; }
    }

    public class GetTemplateHandler : DapperRequestHandler<GetTemplate, TemplateDto>
    {
        public GetTemplateHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<TemplateDto> OnHandleAsync(IDbHelper db, GetTemplate request)
        {
            return await db.GetAsync<TemplateDto>(request.TemplateId);
        }
    }
}
