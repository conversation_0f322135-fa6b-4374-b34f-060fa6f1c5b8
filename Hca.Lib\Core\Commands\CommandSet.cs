﻿using System;
using System.Collections.Generic;
using MediatR;

namespace Hca.Lib.Core
{
    // Convenience command collection only suitable when it is understood command
    // validation will not take account of previous commands/events
    // Not usually suitable for anything but test/utility code
    public class CommandSet<T> : AggregateMessage, IRequest<CommandResult> where T:IAggregate
    {
        public CommandSet(Guid aggregateId, IEnumerable<AggregateCommand<T>> requests) : base(aggregateId)
        {
            Commands = requests;
        }

        public IEnumerable<AggregateCommand<T>> Commands { get; }
    }
}
