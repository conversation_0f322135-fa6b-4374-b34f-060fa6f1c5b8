﻿using System;
using Dapper;

namespace Hca.Lib.Features.Projects
{
    [Table("tblProjects")]
    public class ProjectDto
    {
        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        public Guid? QuoteId { get; set; }

        [Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid ClientId { get; set; }

        [Required]
        public string ProjectNumber { get; set; }
    }
}
