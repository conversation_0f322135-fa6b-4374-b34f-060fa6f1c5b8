﻿@page "/clients/{clientId:guid}/sites/{siteId:guid}/plans/{id}"
@model Hca.WebHost.Areas.Clients.Pages.ClientSitePlanModel
@using Hca.Lib.Features.Clients
@{
    Layout = "_LayoutSite";
    ViewData["Client"] = Model.Client;
    ViewData["Site"] = Model.Site;
}

<div class="card card-default">
    <div class="card-header">
        <small class="text-muted">SITE PLAN</small>
    </div>
    <form method="post">
        <div class="card-body">
            <input asp-for="Plan.Notes" row-label="Notes" />
        </div>
        <save-cancel-footer CancelUrl="@Urls.ClientSitePlans(Model.Client.Id, Model.Site.Id)"></save-cancel-footer>
    </form>
</div>

@section scripts{
    <script>

        $(() => {

         drawBreadcrumb([
                
                { url: '@Urls.Client(Model.Client.Id)', text: '@Model.Client.ClientName' },
                { url: '@Urls.ClientSites(Model.Client.Id)', text: 'Sites' },
                { url: '@Urls.ClientSite(Model.Client.Id, Model.Site.Id)', text: '@Model.Site.GetDisplayText()' },
                { url: '@Urls.ClientSitePlans(Model.Client.Id, Model.Site.Id)', text: 'Plans' },
                { text: 'Plan' },
            ]);
    });

    </script>
}