﻿using System;
using System.Data.Common;
using System.Threading;
using System.Threading.Tasks;
using Azure.Core;
using Azure.Identity;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore.Diagnostics;

namespace Hca.Lib.Data
{

    public class AzureTokenInterceptor : DbConnectionInterceptor
    {
        private static readonly string[] _scopes = new[] { "https://database.windows.net/.default" };
        private readonly TokenCredential _credentials;
        private AccessToken _accessToken;

        public AzureTokenInterceptor(string clientId)
        {
            if (!string.IsNullOrWhiteSpace(clientId))
            {
                _credentials = new DefaultAzureCredential(new DefaultAzureCredentialOptions { ManagedIdentityClientId = clientId });
            }
        }

        public override async ValueTask<InterceptionResult> ConnectionOpeningAsync(
            DbConnection connection,
            ConnectionEventData eventData,
            InterceptionResult result,
            CancellationToken cancellationToken = default)
        {
            var sqlConnection = (SqlConnection)connection;

            if (sqlConnection.ConnectionString.ToLower().Contains("database.windows.net")
                && !sqlConnection.ConnectionString.ToLower().Contains("password")
                && !sqlConnection.ConnectionString.ToLower().Contains("user id")
                && !sqlConnection.ConnectionString.ToLower().Contains("userid")
                && !sqlConnection.ConnectionString.ToLower().Contains("uid")
                && !sqlConnection.ConnectionString.ToLower().Contains("pwd"))
            {
                await RefreshToken();
                sqlConnection.AccessToken = _accessToken.Token;
            }

            return result;
        }

        private async ValueTask RefreshToken()
        {
            if (_credentials == null ||
                _accessToken.ExpiresOn != default &&
                DateTimeOffset.UtcNow < _accessToken.ExpiresOn)
            {
                return;
            }

            _accessToken = await _credentials.GetTokenAsync(
                new TokenRequestContext(_scopes),
                CancellationToken.None
            );
        }
    }
}
