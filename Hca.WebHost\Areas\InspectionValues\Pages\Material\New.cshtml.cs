﻿using Hca.Lib.Features.Inspections;
using Hca.WebHost.Areas.InspectionValues.Models;
using Hca.WebHost.Services;

namespace Hca.WebHost.Areas.InspectionValues.Pages.Material
{
    public class New : InspectionValueCreatePage
    {
        public New(ValueListsService inspectionValuesService) : base(inspectionValuesService)
        {
        }

        public override   ValueListType  ValueListType =>   ValueListType.Material;

        public override string IndexUrl => Urls.Material;
    }
}
