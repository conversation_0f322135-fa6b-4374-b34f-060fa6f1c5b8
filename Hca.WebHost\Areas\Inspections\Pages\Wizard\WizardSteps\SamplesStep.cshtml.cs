﻿using System;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;

namespace Hca.WebHost.Areas.Inspections.Pages.Wizard;

public class SamplesModel : HcaPageModel
{
    public SamplesModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public Guid InspectionId { get; private set; }
    
    public void OnGet(Guid inspectionId)
    {
        InspectionId = inspectionId;
    }
}
