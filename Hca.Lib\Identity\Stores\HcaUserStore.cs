﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Clients.Queries;
using Hca.Lib.Features.Users;
using Hca.Lib.Features.Users.Commands;
using Hca.Lib.Features.Users.Queries;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Hca.Lib.Identity.Stores
{
    public class HcaUserStore :
        IUserStore<HcaUser>,
        IUserPasswordStore<HcaUser>,
        IUserEmailStore<HcaUser>,
        IUserRoleStore<HcaUser>
    {
        private readonly IMediator _mediator;

        public HcaUserStore(IMediator mediator)
        {
            _mediator = mediator;
        }

        public Task AddToRoleAsync(HcaUser user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public async Task<IdentityResult> CreateAsync(HcaUser user, CancellationToken cancellationToken)
        {
            var command = new CreateUser(user.Email, user.PasswordHash, user.Role);
            await _mediator.Send(command, cancellationToken);
            return IdentityResult.Success;
        }

        public Task<IdentityResult> DeleteAsync(<PERSON><PERSON><PERSON>ser user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public void Dispose() { }

        public Task<HcaUser> FindByEmailAsync(string normalizedEmail, CancellationToken cancellationToken) =>
            _mediator.Send(new FindUserByEmail(normalizedEmail), cancellationToken)
                .ContinueWith(u => u.Result == null
                    ? null
                    : new HcaUser(
                        u.Result.Id,
                        u.Result.Email,
                        u.Result.Role,
                        u.Result.Permissions)
                    {
                        PasswordHash = u.Result.Password
                    });

        public async Task<HcaUser> FindByIdAsync(string userId, CancellationToken cancellationToken)
        {
            Debug.WriteLine("User loaded by id");

            if (!Guid.TryParse(userId, out var userGuid))
            {
                throw new ApplicationException("Invalid user ID format");
            }
            var userDto = await _mediator.Send(new GetUser(userGuid), cancellationToken);

            if (userDto == null)
            {
                return null;
            }

            return new HcaUser(userGuid, userDto.Email, userDto.Role, userDto.Permissions) { PasswordHash = userDto.Password };
        }

        public async Task<HcaUser> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
        {
            // client logins are stored in the contacts table
            var contactDto = await _mediator.Send(new GetContactByEmail(normalizedUserName), cancellationToken);
            if (contactDto != null)
            {
                return new HcaUser(contactDto.Id, contactDto.Email, UserRole.Client, null, contactDto.ClientId) { PasswordHash = contactDto.Password };
            }

            // staff users are stored in the users table
            var userDto = await _mediator.Send(new FindUserByEmail(normalizedUserName), cancellationToken);
            if (userDto != null)
            {
                return new HcaUser(userDto.Id, userDto.Email, userDto.Role, userDto.Permissions) { PasswordHash = userDto.Password };
            }

            return null;
        }

        public Task<string> GetEmailAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.Email);

        public Task<bool> GetEmailConfirmedAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(true);

        public Task<string> GetNormalizedEmailAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.Email);

        public Task<string> GetNormalizedUserNameAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.Email);

        public Task<string> GetPasswordHashAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.PasswordHash);

        public Task<IList<string>> GetRolesAsync(HcaUser user, CancellationToken cancellationToken) =>
            Task.FromResult((IList<string>)new List<string> { user.Role.ToTokenString() });

        public Task<string> GetUserIdAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.Id);

        public Task<string> GetUserNameAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(user.Email);

        public Task<IList<HcaUser>> GetUsersInRoleAsync(string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<bool> HasPasswordAsync(HcaUser user, CancellationToken cancellationToken) => Task.FromResult(!string.IsNullOrWhiteSpace(user.PasswordHash));

        public Task<bool> IsInRoleAsync(HcaUser user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task RemoveFromRoleAsync(HcaUser user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task SetEmailAsync(HcaUser user, string email, CancellationToken cancellationToken)
        {
            user.Email = email;
            return Task.CompletedTask;
        }

        public Task SetEmailConfirmedAsync(HcaUser user, bool confirmed, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task SetNormalizedEmailAsync(HcaUser user, string normalizedEmail, CancellationToken cancellationToken)
        {
            user.Email = normalizedEmail;
            return Task.CompletedTask;
        }

        public Task SetNormalizedUserNameAsync(HcaUser user, string normalizedName, CancellationToken cancellationToken)
        {
            user.NormalizedUserName = normalizedName;
            return Task.CompletedTask;
        }

        public Task SetPasswordHashAsync(HcaUser user, string passwordHash, CancellationToken cancellationToken)
        {
            user.PasswordHash = passwordHash;
            return Task.CompletedTask;
        }

        public Task SetUserNameAsync(HcaUser user, string userName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IdentityResult> UpdateAsync(HcaUser user, CancellationToken cancellationToken) =>
            _mediator.Send(new SetUserPassword(user.UserId, user.PasswordHash), cancellationToken).ContinueWith(u => IdentityResult.Success);
    }
}
