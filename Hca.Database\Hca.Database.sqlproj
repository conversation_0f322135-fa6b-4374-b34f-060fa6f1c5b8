﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <Name>Hca.Database</Name>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>4.1</ProjectVersion>
    <ProjectGuid>{6442e01b-ca07-427b-87f7-10b0270ad0b3}</ProjectGuid>
    <DSP>Microsoft.Data.Tools.Schema.Sql.Sql130DatabaseSchemaProvider</DSP>
    <OutputType>Database</OutputType>
    <RootPath>
    </RootPath>
    <RootNamespace>Hca.Database</RootNamespace>
    <AssemblyName>Hca.Database</AssemblyName>
    <ModelCollation>1033, CI</ModelCollation>
    <DefaultFileStructure>BySchemaAndSchemaType</DefaultFileStructure>
    <DeployToDatabase>True</DeployToDatabase>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <TargetLanguage>CS</TargetLanguage>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <SqlServerVerification>False</SqlServerVerification>
    <IncludeCompositeObjects>True</IncludeCompositeObjects>
    <TargetDatabaseSet>True</TargetDatabaseSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <OutputPath>bin\Release\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>False</TreatWarningsAsErrors>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <OutputPath>bin\Debug\</OutputPath>
    <BuildScriptName>$(MSBuildProjectName).sql</BuildScriptName>
    <TreatWarningsAsErrors>false</TreatWarningsAsErrors>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">11.0</VisualStudioVersion>
    <!-- Default to the v11.0 targets path if the targets file for the current VS version is not found -->
    <SSDTExists Condition="Exists('$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets')">True</SSDTExists>
    <VisualStudioVersion Condition="'$(SSDTExists)' == ''">11.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Condition="'$(SQLDBExtensionsRefPath)' != ''" Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <Import Condition="'$(SQLDBExtensionsRefPath)' == ''" Project="$(MSBuildExtensionsPath)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.SqlTasks.targets" />
  <ItemGroup>
    <Folder Include="Properties" />
    <Folder Include="dbo\" />
    <Folder Include="dbo\Tables\" />
    <Folder Include="dbo\Views\" />
    <Folder Include="dbo\Functions\" />
  </ItemGroup>
  <ItemGroup>
    <Build Include="dbo\Tables\tblValueLists.sql" />
    <Build Include="dbo\Tables\tblUsers.sql" />
    <Build Include="dbo\Tables\tblTemplateSections.sql" />
    <Build Include="dbo\Tables\tblTemplates.sql" />
    <Build Include="dbo\Tables\tblTemplateFiles.sql" />
    <Build Include="dbo\Tables\tblTemplateFields.sql" />
    <Build Include="dbo\Tables\tblSites.sql" />
    <Build Include="dbo\Tables\tblSitePlans.sql" />
    <Build Include="dbo\Tables\tblQuotes.sql" />
    <Build Include="dbo\Tables\tblQuoteProperties.sql" />
    <Build Include="dbo\Tables\tblQuoteFields.sql" />
    <Build Include="dbo\Tables\tblQuoteDocuments.sql" />
    <Build Include="dbo\Tables\tblPropertyDocuments.sql" />
    <Build Include="dbo\Tables\tblProperties.sql" />
    <Build Include="dbo\Tables\tblProjects.sql" />
    <Build Include="dbo\Tables\tblInspectionSamples.sql" />
    <Build Include="dbo\Tables\tblInspectionSampleImages.sql" />
    <Build Include="dbo\Tables\tblInspections.sql" />
    <Build Include="dbo\Tables\tblInspectionDocuments.sql" />
    <Build Include="dbo\Tables\tblFloorPlans.sql" />
    <Build Include="dbo\Tables\tblDocumentSections.sql" />
    <Build Include="dbo\Tables\tblDocuments.sql" />
    <Build Include="dbo\Tables\tblDocumentFields.sql" />
    <Build Include="dbo\Tables\tblContacts.sql" />
    <Build Include="dbo\Tables\tblConfig.sql" />
    <Build Include="dbo\Tables\tblClients.sql" />
    <Build Include="dbo\Tables\tblAddresses.sql" />
    <Build Include="dbo\Views\newGuid.sql" />
    <Build Include="dbo\Functions\RandomString.sql" />
    <Build Include="dbo\Tables\tblActionLog.sql" />
  </ItemGroup>
  <ItemGroup>
    <RefactorLog Include="Hca.Database.refactorlog" />
  </ItemGroup>
</Project>