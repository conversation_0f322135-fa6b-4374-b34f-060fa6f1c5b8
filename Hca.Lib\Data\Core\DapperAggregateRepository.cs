﻿//using System.Threading.Tasks;
//using Hca.Lib.Core;

//namespace Hca.Lib.Data.Core
//{
//    public abstract class DapperAggregateRepository<T> : AggregateRepository<T> where T : IAggregate
//    {
//        private readonly IDbHelper _dbHelper;

//        public DapperAggregateRepository(IDbHelper dbHelper, IEventPublisher eventPublisher) : base(eventPublisher)
//        {
//            _dbHelper = dbHelper;
//        }

//        public override async Task<T> LoadByIdAsync(EntityId entityId)
//        {
//            return await RetrieveByIdAsync(_dbHelper, entityId);
//        }

//        protected abstract Task<T> RetrieveByIdAsync(IDbHelper db, EntityId entityId);

//        protected override Task PersistAsync(T aggregate) => SaveAsync(_dbHelper, aggregate);

//        protected abstract Task SaveAsync(IDbHelper db, T aggregate);
//    }
//}