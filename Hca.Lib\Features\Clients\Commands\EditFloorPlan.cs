﻿using System;
using System.Threading.Tasks;
using Hca.Lib.Core;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Clients.Commands
{
    public class EditFloorPlan : ICommand
    {
        public EditFloorPlan(
            Guid planId,
            Guid floorId,
            string notes)
        {
            PlanId = planId;
            FloorId = floorId;
            Notes = notes;
        }

        public Guid PlanId { get; }
        public Guid FloorId { get; }
        public string Notes { get; }
    }

    public class EditFloorPlanHandler : DapperRequestHandler<EditFloorPlan, CommandResult>
    {
        public EditFloorPlanHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<CommandResult> OnHandleAsync(IDbHelper db, EditFloorPlan request)
        {
            var dto = await db.GetAsync<FloorPlanDto>(request.PlanId);
            dto.FloorId = request.FloorId;
            dto.Notes = request.Notes;

            await db.UpdateAsync(dto);

            return CommandResult.Success();
        }
    }
}

