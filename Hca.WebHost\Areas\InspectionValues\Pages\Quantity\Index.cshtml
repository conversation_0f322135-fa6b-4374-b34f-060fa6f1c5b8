﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.Quantity.Index
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Quantity Name" }
        },
        Header = "Quantity",
        UrlPath = Urls.Quantity,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />