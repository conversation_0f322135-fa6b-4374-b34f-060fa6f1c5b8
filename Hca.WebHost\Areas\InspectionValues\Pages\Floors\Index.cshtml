﻿@page
@model Hca.WebHost.Areas.InspectionValues.Pages.FloorsModel
@using Hca.WebHost.Areas.InspectionValues.Models
@using Hca.Lib.Features.Inspections
@{
    var valuesModel = new InspectionValuesModel
    {
        ColumnNames = new Dictionary<string, string>
        {
            { nameof( ValueDto.DisplayText), "Floor Name" }
        },
        Header = "Floors",
        UrlPath = Urls.Floors,
        InspectionValues = Model.Values
    };
}

<partial name="../Widgets/_InspectionValuesPartial" model="valuesModel" />