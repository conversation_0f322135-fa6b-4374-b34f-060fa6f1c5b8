﻿using System.IO;

namespace Hca.Lib.Identity;

public static class HcaClaimTypes
{
    public const string CLIENTID = "cli";
    public const string USERACCESS = "uac";
    public const string CONTACTID = "cid";
    public const string USERID = "uid";
}

public static class HcaClaimValues
{
    // these can get more granular for specific page access
    public const string CLIENT = "Client";

    public const string HCA = "Hca";

    public const string ADMIN = "Admin";
}
