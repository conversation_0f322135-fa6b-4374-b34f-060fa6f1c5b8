﻿using Microsoft.AspNetCore.Razor.TagHelpers;

namespace Hca.WebHost.TagHelpers
{
    [HtmlTargetElement("edit")]
    public class FormEditRowTagHelper : TagHelper
    {
        [HtmlAttributeName("action")]
        public string Action { get; set; }

        [HtmlAttributeName("text")]
        public string Text { get; set; } = "edit";

        public override void Process(TagHelperContext context, TagHelperOutput output)
        {
            output.TagName = "a";
            output.Attributes.Add("class", "btn btn-sm btn-info mr-2 command-edit");
            output.Attributes.Add("href", Action);
            
            output.PreContent.SetHtmlContent($"<em class=\"fa fa-edit fa-fw\"></em>{Text}");
        }
    }
}
