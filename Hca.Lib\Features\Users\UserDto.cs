﻿using System;
using System.ComponentModel.DataAnnotations;
using Dapper;

namespace Hca.Lib.Features.Users
{
    [Table("tblUsers")]
    public class UserDto
    {
        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public Guid Id { get; set; }

        [DataType(DataType.EmailAddress)]
        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public string Email { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        [DataType(DataType.Password)]
        [Dapper.Required]
        public string Password { get; set; }

        [Dapper.Required]
        [System.ComponentModel.DataAnnotations.Required]
        public UserRole Role { get; set; }

        public string Position { get; set; }

        public string OfficePhone { get; set; }

        public string MobilePhone { get; set; }

        public string SignatureFileName { get; set; }

        public string ProfileImageFileName { get; set; }

        public string Permissions { get; set; }
    }
}
