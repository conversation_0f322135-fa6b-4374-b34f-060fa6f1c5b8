﻿using System.Threading.Tasks;
using Hca.Lib.Core.Queries;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;

namespace Hca.Lib.Features.Templates.Data.Queries
{
    public class GetAllTemplatesQuery : IQueryMany<TemplateDto>
    {
    }

    public class GetAllTemplatesQueryHandler : DapperRequestHandler<GetAllTemplatesQuery, DtoSet<TemplateDto>>
    {
        public GetAllTemplatesQueryHandler(IDbHelper dbHelper) : base(dbHelper)
        {
        }

        public override async Task<DtoSet<TemplateDto>> OnHandleAsync(IDbHelper db, GetAllTemplatesQuery request)
        {
            return DtoSet.From(await db.GetListAsync<TemplateDto>());
        }
    }
}
