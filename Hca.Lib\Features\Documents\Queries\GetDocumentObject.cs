﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Hca.Lib.Data;
using Hca.Lib.Data.Core;
using Hca.Lib.Features.Documents.Fields;
using MediatR;

namespace Hca.Lib.Features.Documents.Queries
{
    public class GetDocumentObject : IRequest<DocumentObject>
    {
        public GetDocumentObject(Guid documentId)
        {
            DocumentId = documentId;
        }

        public Guid DocumentId { get; }
    }

    public class GetDocumentObjectHandler : DapperRequestHandler<GetDocumentObject, DocumentObject>
    {
        public GetDocumentObjectHandler(IDbHelper dbHelper) : base(dbHelper) { }

        public override async Task<DocumentObject> OnHandleAsync(IDbHelper db, GetDocumentObject request)
        {
            var documentDto = await db.GetAsync<DocumentDto>(request.DocumentId);
            var documentSections = await db.GetListAsync<DocumentSectionDto>("WHERE DocumentId = @DocumentId", request);

            return new DocumentObject
            {
                DocumentId = request.DocumentId,
                TemplateCode = documentDto.TemplateCode,
                DocumentSections = await GetDocumentSections(db, documentSections)
            };
        }

        private async Task<List<DocumentSection>> GetDocumentSections(IDbHelper db, IEnumerable<DocumentSectionDto> documentSections)
        {
            var sections = new List<DocumentSection>();
            foreach (var sectionDto in documentSections)
            {
                sections.Add(await GetDocumentSection(db, sectionDto));
            }
            return sections;
        }

        private async Task<DocumentSection> GetDocumentSection(IDbHelper dbHelper, DocumentSectionDto sectionDto)
        {
            return new DocumentSection
            {
                DocumentSectionId = sectionDto.Id,
                SectionCode = sectionDto.SectionCode,
                SectionComplete = sectionDto.SectionComplete,
                SectionOrder = sectionDto.SectionOrder,
                SectionTitle = sectionDto.SectionTitle,
                DocumentFields = await GetDocumentFields(sectionDto.Id, dbHelper)
            };
        }

        private async Task<IList<IDocumentField<Field>>> GetDocumentFields(Guid id, IDbHelper db)
        {
            var fields = new List<IDocumentField<Field>>();
            var fieldDtos = await db.GetListAsync<DocumentFieldDto>(
                "WHERE DocumentSectionId = @DocumentSectionId",
                new { DocumentSectionId = id });

            foreach(var fieldDto in fieldDtos)
            {
                fields.Add(fieldDto.FieldType switch
                {
                    FieldType.Choice => new DocumentChoiceField(fieldDto.Id, fieldDto.FieldOrder, fieldDto.IsOptional, fieldDto.FieldContent),
                    FieldType.Text => new DocumentTextField(fieldDto.Id, fieldDto.FieldOrder, fieldDto.IsOptional, fieldDto.FieldContent),
                    _ => throw new NotImplementedException()
                });
            }

            return fields;
        }
    }
}
