﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Hca.Lib.Features.Inspections.Queries;
using Hca.Lib.Features.Inspections.Queries.Models;
using Hca.WebHost.Models;
using Hca.WebHost.Pipeline;
using MediatR;

namespace Hca.WebHost.Areas.Inspections.Pages;

public class ExistingModel : HcaPageModel
{
    public ExistingModel(
            ViewManager viewManager,
            IMediator mediator) : base(mediator, viewManager)
    { }

    public async Task OnGetAsync(Guid inspectionId, CancellationToken cancellationToken)
    {
        Inspection = await _mediator.Send(new GetInspection(inspectionId), cancellationToken);
    }

    public InspectionQueryModel Inspection { get; private set; }
}
