# Spotlite System

## Overview

Spotlite is a comprehensive property and inspection management system built with .NET 6.0. The system provides a web-based portal for managing clients, properties, inspections, documents, and projects. It features a modern frontend with Bootstrap-based UI components and a robust backend architecture using clean architecture principles.

## Repository Information

The source code for Spotlite is stored in a private GitHub repository. Developers should have appropriate access permissions to clone and contribute to the codebase.

## Deployment

Spotlite is deployed to Microsoft Azure with the following services:

- **Azure App Service**: Hosts the web application
- **Azure SQL Database**: Primary database storage
- **Azure Blob Storage**: File storage for documents and images
- **Azure Key Vault**: Secure storage for secrets and connection strings
- **Azure Application Insights**: Application monitoring and telemetry

## Prerequisites

Before setting up the project locally, ensure you have the following installed:

- [.NET 6.0 SDK](https://dotnet.microsoft.com/download/dotnet/6.0)
- [Node.js](https://nodejs.org/) (v14 or later)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) or SQL Server Express
- [Visual Studio 2022](https://visualstudio.microsoft.com/vs/) or [Visual Studio Code](https://code.visualstudio.com/)

## Local Development Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd spotlite
```

### 2. Database Configuration

1. Ensure SQL Server is running locally
2. Create a database named `HCA`
3. Update the connection string in `Hca.WebHost/appsettings.json` if needed:
   ```json
   "ConnectionStrings": {
     "DbConnectionString": "Server=tcp:localhost,1433;Initial Catalog=HCA;Persist Security Info=False;User ID=sa;Password=********;MultipleActiveResultSets=False;Encrypt=False;TrustServerCertificate=False;Connection Timeout=30;"
   }
   ```

### 3. Restore Dependencies

```bash
# Restore .NET dependencies
dotnet restore

# Restore Node.js dependencies
cd Hca.WebHost
npm install
```

### 4. Build Frontend Assets

The project uses Gulp to process SCSS and JavaScript files:

```bash
# Build development assets
npx gulp

# Build production assets (minified)
npx gulp min
```

### 5. Run the Application

```bash
# From the solution root directory
dotnet run --project Hca.WebHost
```

The application will be available at `https://localhost:5001` or `http://localhost:5000`.

## Project Structure

```
Hca.Database/          # Database schema and scripts
Hca.Lib/               # Core library with business logic
  ├── Core/            # Domain models and interfaces
  ├── Data/            # Data access layer
  ├── Features/        # Feature modules (Clients, Documents, Inspections, etc.)
  └── Services/        # Shared services
Hca.WebHost/           # Web application host
  ├── Areas/           # MVC areas for different sections
  ├── Controllers/     # API controllers
  ├── Pages/           # Razor Pages
  ├── Services/        # Web-specific services
  ├── wwwroot/         # Static files and compiled assets
  └── Startup.cs       # Application configuration
```

## Azure Services Configuration

The application integrates with several Azure services:

### Azure Blob Storage
Used for storing documents and images:
- Configuration in `appsettings.json` under `BlobStorage`
- Service URI: `https://hcastore.blob.core.windows.net/`

### Azure Key Vault
Used for secure storage of secrets:
- Configuration in `appsettings.json` under `KeyVault`
- Vault URI: `https://hca-production.vault.azure.net/`

### Data Protection
The application uses Azure Blob Storage and Key Vault for data protection keys in production:
- Development: Keys stored in local file system
- Production: Keys stored in Azure Blob Storage and protected with Key Vault

## Authentication

The system uses ASP.NET Core Identity for authentication with custom implementations:
- Users are managed through the `HcaUser` entity
- Password hashing using `PasswordHasher<HcaUser>`
- Role-based access control with custom policies

## Build Process

The frontend build process uses Gulp with the following tasks:

- `gulp`: Builds development assets
- `gulp min`: Builds production assets (minified)
- `gulp sass`: Compiles SCSS files
- `gulp js`: Concatenates JavaScript files
- `gulp vendor`: Copies vendor dependencies

Key dependencies include:
- Bootstrap 4.5.0
- jQuery 3.5.1
- FontAwesome 5.5.0
- DataTables
- Select2
- And many other UI components

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Ensure SQL Server is running
   - Verify connection string in `appsettings.json`
   - Check SQL Server authentication settings

2. **Frontend Assets Not Loading**
   - Run `npx gulp` to rebuild assets
   - Check for errors in the Gulp build process
   - Ensure Node.js dependencies are installed

3. **Authentication Issues**
   - Verify user accounts in the database
   - Check password hashing implementation
   - Review role assignments

### Useful Commands

```bash
# Clean and rebuild the solution
dotnet clean
dotnet build

# Run with verbose logging
dotnet run --project Hca.WebHost --verbosity detailed

# Run database migrations (if applicable)
dotnet ef database update
```

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Ensure all tests pass
4. Submit a pull request with a clear description of changes

## Support

For issues or questions, contact the development team or create an issue in the GitHub repository.